import { UserNavMenuContainer } from "../../components/Layout/UserNavMenuContainer";
import { useAuth } from "./AuthContext";
import LoggedNavMenu from "./LoggedNavMenu";

interface Props {
  onClick: () => void;
}

const UserNavMenu = ({ onClick }: Props) => {
  const { user } = useAuth();

  return (
    <UserNavMenuContainer data-testid="user-nav-menu-container">
      {user.email ? <LoggedNavMenu onProfileClick={onClick} /> : <></>}
    </UserNavMenuContainer>
  );
};

export default UserNavMenu;
