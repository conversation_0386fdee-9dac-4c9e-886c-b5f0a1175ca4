import { render, screen } from "@testing-library/react";
import Form from "../Form";
import "@testing-library/jest-dom";

describe("Form Component", () => {
  it("renders children correctly", () => {
    render(
      <Form>
        <div data-testid="child-component">Test Child</div>
      </Form>
    );

    const child = screen.getByTestId("child-component");
    expect(child).toBeInTheDocument();
    expect(child.textContent).toBe("Test Child");
  });

  it("has Header attached to its default export", () => {
    expect(Form.Header).toBeDefined();
  });
});
