<svg xmlns="http://www.w3.org/2000/svg" width="546.727" height="383.518" viewBox="0 0 546.727 383.518">
  <g id="StartAnimation" transform="translate(-276.805 -156.454)" opacity="0.1">
    <g id="Group_94" data-name="Group 94" transform="translate(294.559 162.58)">
      <g id="Group_88" data-name="Group 88" transform="translate(27.917)">
        <path id="Path_201" data-name="Path 201" d="M673.666,532.178c61.522,0,118.138.921,163.07,2.464,109.95-62.85,71.877-185.35,32.4-254.643-54.946-96.438-184.777-131.1-238.479-54.154-72.891,104.438-143.207,67.994-181.176,147.31S469.174,528.513,510.6,534.643c44.928-1.541,101.544-2.464,163.067-2.464Zm-96.34-267.431c-28.143,21.42-74.692,38.019-90.151,15.246-11.548-17.012,2.678-48.932,30.539-73.135,27.843-24.183,65.146-38.823,85.476-21.013C627.318,206.977,599,248.253,577.326,264.747Z" transform="translate(-437.098 -177.954)" fill="#eaeff8" fill-rule="evenodd"/>
      </g>
      <g id="Group_89" data-name="Group 89" transform="translate(0 354.225)">
        <path id="Path_202" data-name="Path 202" d="M603.6,1421.19c146.073,0,264.488,5.186,264.488,11.585s-118.415,11.582-264.488,11.582-264.485-5.185-264.485-11.582S457.528,1421.19,603.6,1421.19Z" transform="translate(-339.115 -1421.19)" fill="#d2dfee" fill-rule="evenodd"/>
      </g>
      <g id="Group_90" data-name="Group 90" transform="translate(166.958 9.875)">
        <path id="Path_203" data-name="Path 203" d="M1107.916,237.212c66.056,14.716,108.09,75.466,112.21,104.795,1.692,12.034-1.443-16.13-35.14-41.775-30.115-22.919-53.563-27.165-89.39-29.935-25.321-1.956-51.832-5.068-49.949-20.17,1.489-11.927,19.622-22.416,62.269-12.915Zm-172.731-24.57c11.373.682,18.295,10.021,18.771,20.789.2,4.582-3.4-7.4-16-9.064-7.818-1.033-8.994,1.271-11.8-1.956S926.014,212.09,935.184,212.642Z" transform="translate(-925.094 -212.611)" fill="#fff" fill-rule="evenodd"/>
      </g>
      <g id="Group_91" data-name="Group 91" transform="translate(230.034 57.693)">
        <path id="Path_204" data-name="Path 204" d="M1161.721,399.562c-.945,12.992-2.861,14.785-15.248,15.785,12.387,1,14.3,2.793,15.248,15.784.947-12.991,2.864-14.784,15.251-15.784-12.387-1-14.3-2.793-15.251-15.785Zm50.351,34.266c-.508,6.937-1.531,7.893-8.143,8.427,6.612.534,7.635,1.492,8.143,8.428.5-6.937,1.525-7.894,8.14-8.428-6.613-.535-7.636-1.49-8.14-8.427Zm-22.05-53.385c-.511,7.025-1.549,8-8.247,8.536,6.7.541,7.736,1.51,8.247,8.535.511-7.025,1.549-7.994,8.246-8.535C1191.57,388.437,1190.532,387.467,1190.022,380.442Z" transform="translate(-1146.473 -380.442)" fill="#fff" fill-rule="evenodd"/>
      </g>
      <g id="Group_92" data-name="Group 92" transform="translate(179.634 274.333)">
        <path id="Path_205" data-name="Path 205" d="M1016.974,1156.281c.766,10.524,2.319,11.977,12.353,12.786-10.034.809-11.587,2.262-12.353,12.789-.766-10.527-2.319-11.98-12.354-12.789,10.035-.811,11.588-2.264,12.354-12.786Zm-40.793,27.757c.41,5.619,1.239,6.392,6.6,6.828-5.357.431-6.186,1.206-6.6,6.828-.41-5.621-1.239-6.4-6.6-6.828,5.358-.434,6.187-1.206,6.6-6.828Zm17.865-43.248c.414,5.691,1.254,6.475,6.68,6.913-5.425.438-6.266,1.224-6.68,6.916-.415-5.692-1.254-6.477-6.681-6.916C992.793,1147.266,993.631,1146.483,994.047,1140.79Z" transform="translate(-969.585 -1140.79)" fill="#fff" fill-rule="evenodd"/>
      </g>
      <g id="Group_93" data-name="Group 93" transform="translate(377.874 252.018)">
        <path id="Path_206" data-name="Path 206" d="M1674.813,1074.325c-.586,8.063-1.777,9.173-9.458,9.793,7.681.62,8.872,1.733,9.458,9.792.589-8.06,1.777-9.172,9.46-9.792-7.682-.618-8.871-1.729-9.46-9.793Zm31.233,21.258c-.314,4.3-.947,4.9-5.051,5.227,4.1.332,4.737.926,5.051,5.227.314-4.3.947-4.9,5.051-5.227-4.1-.329-4.737-.928-5.051-5.227Zm-13.679-33.114c-.316,4.358-.96,4.96-5.115,5.295,4.156.334,4.8.936,5.115,5.294.316-4.358.96-4.96,5.115-5.294C1693.327,1067.427,1692.683,1066.826,1692.367,1062.47Z" transform="translate(-1665.355 -1062.47)" fill="#fff" fill-rule="evenodd"/>
      </g>
    </g>
    <g id="Group_95" data-name="Group 95" transform="translate(726.532 380.483)">
      <path id="Path_207" data-name="Path 207" d="M1869.6,945.438l-2.724,1.585a1.936,1.936,0,0,0-.7,2.638l.6,1.035c.708,1.217.4,1.769.179,1.966-.6.542-1.167,1.131-1.73,1.717-.166.172-.693.763-2.075-.044l-.952-.552a1.937,1.937,0,0,0-2.638.7l-1.582,2.721a1.937,1.937,0,0,0,.7,2.641l.942.547c1.3.752,1.263,1.141,1.1,1.692-.226.755-.47,1.51-.635,2.293-.145.7-.078,1.193-1.845,1.193h-1.082a1.933,1.933,0,0,0-1.93,1.93v3.149a1.935,1.935,0,0,0,1.93,1.932h1.082c1.766,0,1.7.5,1.845,1.193.166.781.41,1.538.635,2.293.163.552.2.936-1.1,1.694l-.942.544a1.937,1.937,0,0,0-.7,2.641l1.582,2.721a1.934,1.934,0,0,0,2.638.7l.952-.55c1.383-.8,1.909-.218,2.075-.044.563.586,1.126,1.175,1.73,1.717.223.2.529.75-.179,1.969l-.6,1.033a1.936,1.936,0,0,0,.7,2.638l2.724,1.585a1.938,1.938,0,0,0,2.641-.7l.6-1.03c.721-1.237,1.429-1.188,1.727-1.092.869.275,1.671.454,2.568.688.327.086.887.324.887,1.8v1.1a1.933,1.933,0,0,0,1.93,1.93h3.149a1.933,1.933,0,0,0,1.93-1.93v-1.1c0-1.474.56-1.712.887-1.8.9-.233,1.7-.413,2.568-.688.3-.1,1.006-.145,1.728,1.092l.6,1.03a1.934,1.934,0,0,0,2.638.7l2.724-1.585a1.933,1.933,0,0,0,.7-2.638l-.6-1.033c-.711-1.219-.4-1.772-.182-1.969.607-.542,1.168-1.131,1.733-1.717.166-.174.69-.76,2.075.044l.949.55a1.933,1.933,0,0,0,2.638-.7l1.585-2.721a1.938,1.938,0,0,0-.7-2.641l-.939-.544c-1.3-.758-1.266-1.142-1.1-1.694.226-.755.467-1.513.633-2.293.145-.7.08-1.193,1.847-1.193h1.082a1.937,1.937,0,0,0,1.93-1.932V967.5a1.935,1.935,0,0,0-1.93-1.93h-1.082c-1.766,0-1.7-.5-1.847-1.193-.166-.783-.407-1.538-.633-2.293-.166-.55-.2-.939,1.1-1.692l.939-.547a1.938,1.938,0,0,0,.7-2.641l-1.585-2.721a1.936,1.936,0,0,0-2.638-.7l-.949.552c-1.385.807-1.909.215-2.075.044-.565-.586-1.126-1.175-1.733-1.717-.218-.2-.529-.75.182-1.966l.6-1.035a1.934,1.934,0,0,0-.7-2.638l-2.724-1.585a1.936,1.936,0,0,0-2.638.7l-.6,1.032c-.721,1.237-1.427,1.188-1.728,1.092-.869-.275-1.67-.454-2.568-.687-.327-.085-.887-.322-.887-1.8V944.67a1.933,1.933,0,0,0-1.93-1.93h-3.148a1.933,1.933,0,0,0-1.93,1.93v1.105c0,1.476-.56,1.712-.887,1.8-.9.233-1.7.413-2.568.688-.3.1-1.006.145-1.728-1.092l-.6-1.032a1.941,1.941,0,0,0-2.642-.7Zm11.927,19.48a4.158,4.158,0,1,1-4.158,4.161,4.158,4.158,0,0,1,4.158-4.161Zm-13.035,4.16a.89.89,0,1,1,1.78,0,11.257,11.257,0,0,0,11.256,11.256.89.89,0,1,1,0,1.779,13.036,13.036,0,0,1-13.035-13.035Zm25.9-2.1a.887.887,0,1,1-1.751.277,11.273,11.273,0,0,0-9.183-9.271.888.888,0,1,1,.3-1.751,13.043,13.043,0,0,1,10.636,10.745Zm-12.867-13.351a15.448,15.448,0,1,1-15.448,15.451A15.448,15.448,0,0,1,1881.528,953.628Z" transform="translate(-1855.23 -942.74)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_105" data-name="Group 105" transform="translate(582.285 389.704)">
      <g id="Group_96" data-name="Group 96" transform="translate(51.001)">
        <path id="Path_208" data-name="Path 208" d="M1527.96,993.495h5.666v-4.246a8.5,8.5,0,0,1,8.493-8.488h37.64a8.5,8.5,0,0,1,8.485,8.488v3.933h5.666v-3.933a14.166,14.166,0,0,0-14.151-14.148h-37.64a14.171,14.171,0,0,0-14.158,14.148v4.246Z" transform="translate(-1527.96 -975.1)" fill="#f57a44" fill-rule="evenodd"/>
      </g>
      <g id="Group_97" data-name="Group 97" transform="translate(107.219 16.201)">
        <path id="Path_209" data-name="Path 209" d="M1725.27,1040.136a1.884,1.884,0,0,0,1.886,1.886h9.746a1.889,1.889,0,0,0,1.891-1.886v-6.293a1.886,1.886,0,0,0-1.891-1.881h-9.746a1.881,1.881,0,0,0-1.886,1.881Z" transform="translate(-1725.27 -1031.962)" fill="#fc9774" fill-rule="evenodd"/>
      </g>
      <g id="Group_98" data-name="Group 98" transform="translate(46.827 16.201)">
        <path id="Path_210" data-name="Path 210" d="M1513.31,1040.136a1.881,1.881,0,0,0,1.881,1.886h9.753a1.885,1.885,0,0,0,1.889-1.886v-6.293a1.883,1.883,0,0,0-1.889-1.881h-9.753a1.879,1.879,0,0,0-1.881,1.881Z" transform="translate(-1513.31 -1031.962)" fill="#fc9774" fill-rule="evenodd"/>
      </g>
      <g id="Group_99" data-name="Group 99" transform="translate(0 23.599)">
        <path id="Path_211" data-name="Path 211" d="M1348.96,1165.486a11.321,11.321,0,0,0,11.331,11.323h144.348a11.318,11.318,0,0,0,11.326-11.323v-101.9a5.666,5.666,0,0,0-5.668-5.663H1354.621a5.664,5.664,0,0,0-5.66,5.663v101.9Z" transform="translate(-1348.96 -1057.927)" fill="#fc9774" fill-rule="evenodd"/>
      </g>
      <g id="Group_100" data-name="Group 100" transform="translate(0.271 29.261)">
        <path id="Path_212" data-name="Path 212" d="M1355.581,1077.8h155.682a5.662,5.662,0,0,1,5.655,5.658v33.967a22.652,22.652,0,0,1-22.641,22.646h-52.641v5.806a8.493,8.493,0,1,1-16.986,0v-5.806h-52.087a22.656,22.656,0,0,1-22.651-22.646v-33.967A5.667,5.667,0,0,1,1355.581,1077.8Z" transform="translate(-1349.911 -1077.8)" fill="#f57a44" fill-rule="evenodd"/>
      </g>
      <g id="Group_101" data-name="Group 101" transform="translate(0.271 23.597)">
        <path id="Path_213" data-name="Path 213" d="M1349.91,1097.546a22.651,22.651,0,0,0,22.651,22.641h121.715a22.647,22.647,0,0,0,22.641-22.641v-33.964a5.662,5.662,0,0,0-5.655-5.663H1355.581a5.668,5.668,0,0,0-5.671,5.663v33.964Z" transform="translate(-1349.91 -1057.919)" fill="#fbbfa8" fill-rule="evenodd"/>
      </g>
      <g id="Group_102" data-name="Group 102" transform="translate(75.008 78.151)">
        <path id="Path_214" data-name="Path 214" d="M1612.22,1262.914a8.493,8.493,0,1,0,16.986,0v-13.526H1612.22Z" transform="translate(-1612.22 -1249.388)" fill="#fc9774" fill-rule="evenodd"/>
      </g>
      <g id="Group_103" data-name="Group 103" transform="translate(76.895 80.599)">
        <path id="Path_215" data-name="Path 215" d="M1618.84,1268.506a6.606,6.606,0,1,0,13.212,0v-10.524H1618.84Z" transform="translate(-1618.84 -1257.982)" fill="#ffe2dc" fill-rule="evenodd"/>
      </g>
      <g id="Group_104" data-name="Group 104" transform="translate(76.627 85.904)">
        <rect id="Rectangle_100" data-name="Rectangle 100" width="14.548" height="2.275" fill="#fc9774"/>
      </g>
    </g>
    <g id="Group_162" data-name="Group 162" transform="translate(358.679 187.554)">
      <g id="Group_106" data-name="Group 106" transform="translate(4.372 0.844)">
        <path id="Path_216" data-name="Path 216" d="M661.949,433.448a82.44,82.44,0,1,0-82.44-82.439A82.6,82.6,0,0,0,661.949,433.448Z" transform="translate(-579.509 -268.569)" fill="#fc9774" fill-rule="evenodd"/>
      </g>
      <g id="Group_107" data-name="Group 107" transform="translate(0 1.811)">
        <path id="Path_217" data-name="Path 217" d="M647.449,271.961a83.285,83.285,0,1,1-83.286,83.283A83.283,83.283,0,0,1,647.449,271.961Z" transform="translate(-564.163 -271.961)" fill="#1e77d1" fill-rule="evenodd"/>
      </g>
      <g id="Group_108" data-name="Group 108" transform="translate(4.372 0.844)">
        <path id="Path_218" data-name="Path 218" d="M661.949,433.448a82.44,82.44,0,1,0-82.44-82.439A82.6,82.6,0,0,0,661.949,433.448Z" transform="translate(-579.509 -268.569)" fill="#f7f7f7" fill-rule="evenodd"/>
      </g>
      <g id="Group_109" data-name="Group 109" transform="translate(36.542 3.131)">
        <path id="Path_219" data-name="Path 219" d="M747.309,276.6a75.671,75.671,0,0,1,41.5,138.943,75.67,75.67,0,0,0-96.4-115.354A75.45,75.45,0,0,1,747.309,276.6Z" transform="translate(-692.415 -276.595)" fill="#dfedfa" fill-rule="evenodd"/>
      </g>
      <g id="Group_122" data-name="Group 122" transform="translate(23.303 20.4)">
        <g id="Group_110" data-name="Group 110" transform="translate(114.835 57.431)">
          <path id="Path_220" data-name="Path 220" d="M1049.212,538.77q.193,0,.785.047c.3.025.54.036.717.036l1.668-.083c.141,0,.211.071.211.218a.207.207,0,0,1-.191.231.915.915,0,0,0-.575.239,1.829,1.829,0,0,0-.223.811c-.053.424-.079,1.379-.079,2.873q0,2.57.054,3.977a5.618,5.618,0,0,0,.222,1.732.577.577,0,0,0,.556.331c.179,0,.269.073.269.213a.254.254,0,0,1-.29.287c-.054,0-.134,0-.24-.008-.681-.073-1.148-.109-1.4-.109a7.442,7.442,0,0,0-1.215.07,2.447,2.447,0,0,1-.293.03c-.134,0-.2-.078-.2-.233a.2.2,0,0,1,.167-.217.835.835,0,0,0,.62-.385,4.045,4.045,0,0,0,.192-1.427c.021-.755.032-1.923.032-3.515,0-1.673-.019-2.8-.06-3.362a2.624,2.624,0,0,0-.2-1.043.725.725,0,0,0-.527-.245.22.22,0,0,1-.206-.253c0-.142.07-.213.2-.213Zm4.285,0c.128,0,.39.016.785.047.3.025.54.036.717.036l1.668-.083c.141,0,.211.071.211.218a.208.208,0,0,1-.191.231.915.915,0,0,0-.574.239,1.807,1.807,0,0,0-.223.811c-.053.424-.079,1.379-.079,2.873q0,2.57.055,3.977a5.677,5.677,0,0,0,.22,1.732.579.579,0,0,0,.558.331c.179,0,.268.073.268.213a.254.254,0,0,1-.289.287c-.054,0-.134,0-.24-.008q-1.024-.109-1.4-.109a7.441,7.441,0,0,0-1.215.07,2.445,2.445,0,0,1-.293.03c-.135,0-.2-.078-.2-.233a.2.2,0,0,1,.167-.217.838.838,0,0,0,.62-.385,4.012,4.012,0,0,0,.191-1.427c.022-.755.034-1.923.034-3.515,0-1.673-.021-2.8-.06-3.362a2.6,2.6,0,0,0-.2-1.043.72.72,0,0,0-.525-.245.22.22,0,0,1-.206-.253c0-.142.066-.213.2-.213Zm4.285,0q.193,0,.785.047.449.037.717.036l1.668-.083c.142,0,.21.071.21.218a.208.208,0,0,1-.189.231.915.915,0,0,0-.575.239,1.8,1.8,0,0,0-.223.811c-.053.424-.08,1.379-.08,2.873q0,2.57.056,3.977a5.668,5.668,0,0,0,.221,1.732.578.578,0,0,0,.556.331c.18,0,.27.073.27.213a.254.254,0,0,1-.289.287c-.054,0-.135,0-.24-.008q-1.024-.109-1.4-.109a7.416,7.416,0,0,0-1.214.07,2.475,2.475,0,0,1-.293.03q-.2,0-.2-.233a.2.2,0,0,1,.169-.217.84.84,0,0,0,.62-.385,4.022,4.022,0,0,0,.19-1.427c.022-.755.034-1.923.034-3.515,0-1.673-.021-2.8-.06-3.362a2.594,2.594,0,0,0-.2-1.043.723.723,0,0,0-.527-.245.22.22,0,0,1-.2-.253C1057.583,538.842,1057.648,538.77,1057.782,538.77Z" transform="translate(-1048.989 -538.77)" fill="#4f8ecc" fill-rule="evenodd"/>
        </g>
        <g id="Group_111" data-name="Group 111" transform="translate(57.655 114.785)">
          <path id="Path_221" data-name="Path 221" d="M852.309,751.128h-.441l-1-3.9q-.37-1.479-.944-3.615a24.709,24.709,0,0,0-.738-2.485,1.344,1.344,0,0,0-.3-.46.782.782,0,0,0-.409-.148c-.117-.005-.176-.077-.176-.217,0-.156.069-.232.206-.232l1.47.083c.51,0,1.016-.022,1.515-.07.14-.008.223-.013.252-.013a.233.233,0,0,1,.261.268c0,.119-.078.195-.233.223a.734.734,0,0,0-.621.833,41.242,41.242,0,0,0,1.427,6.726q.476-1.8.911-3.626l.531-2.234a3.59,3.59,0,0,0,.094-.781,1.162,1.162,0,0,0-.178-.652.518.518,0,0,0-.4-.275c-.166-.01-.25-.083-.25-.222,0-.175.068-.261.2-.261a3.558,3.558,0,0,1,.381.025,6.676,6.676,0,0,0,.83.058,4.171,4.171,0,0,0,.686-.051,2.578,2.578,0,0,1,.368-.032c.129,0,.191.083.191.25a.225.225,0,0,1-.191.249.931.931,0,0,0-.494.293,2.51,2.51,0,0,0-.4.742q-.2.51-.658,2.3l-.96,3.73c-.461,1.825-.77,2.986-.928,3.491Zm4.29-10.994c.128,0,.389.017.784.047.3.026.54.036.717.036l1.668-.083c.141,0,.211.073.211.219a.207.207,0,0,1-.191.231.916.916,0,0,0-.575.239,1.826,1.826,0,0,0-.222.811c-.054.424-.08,1.38-.08,2.873q0,2.57.056,3.975a5.645,5.645,0,0,0,.22,1.733.577.577,0,0,0,.556.329c.18,0,.268.073.268.214a.254.254,0,0,1-.289.287c-.053,0-.134,0-.239-.009q-1.024-.107-1.4-.108a7.64,7.64,0,0,0-1.214.069,2.551,2.551,0,0,1-.293.031q-.2,0-.2-.233a.2.2,0,0,1,.167-.217.833.833,0,0,0,.621-.386,4.075,4.075,0,0,0,.191-1.427c.022-.755.034-1.924.034-3.515q0-2.51-.06-3.361a2.629,2.629,0,0,0-.2-1.044.721.721,0,0,0-.526-.244.221.221,0,0,1-.206-.253C856.4,740.207,856.465,740.134,856.6,740.134Z" transform="translate(-848.304 -740.068)" fill="#4f8ecc" fill-rule="evenodd"/>
        </g>
        <g id="Group_112" data-name="Group 112" transform="translate(55.262)">
          <path id="Path_222" data-name="Path 222" d="M844.148,341.135a9.187,9.187,0,0,0,1.175-2.732.725.725,0,0,0-.122-.445,1.022,1.022,0,0,0-.5-.288.232.232,0,0,1-.174-.253c0-.141.068-.214.2-.214a3.858,3.858,0,0,1,.419.036,6.189,6.189,0,0,0,.689.047c.259,0,.6-.019,1.026-.056.2-.018.322-.027.368-.027.174,0,.262.078.262.236a.2.2,0,0,1-.2.231,1.256,1.256,0,0,0-.751.336,3.255,3.255,0,0,0-.7.872c-.213.368-.7,1.285-1.45,2.742q.62,1.574,1.436,3.327a9.8,9.8,0,0,0,1.218,2.169,1.418,1.418,0,0,0,.706.466c.17.027.256.107.256.241,0,.149-.087.226-.262.226l-2-.084c-.261,0-.52.012-.777.034q-.337.021-.435.031a2.264,2.264,0,0,0-.289.005,1.037,1.037,0,0,1-.123.014c-.107,0-.158-.077-.158-.228a.2.2,0,0,1,.183-.231c.4-.053.591-.257.591-.616a13.45,13.45,0,0,0-1.332-3.431,13.168,13.168,0,0,0-1.424,3.293c0,.381.289.637.868.763.128.027.192.1.192.208,0,.161-.066.242-.2.242-.062,0-.226-.012-.494-.036-.321-.031-.643-.048-.966-.048a5.851,5.851,0,0,0-.709.034,3.506,3.506,0,0,1-.433.051c-.209,0-.315-.084-.315-.25a.2.2,0,0,1,.169-.217,1.7,1.7,0,0,0,.784-.349,4,4,0,0,0,.709-1.034q.42-.778,1.584-3.157a43.968,43.968,0,0,0-1.918-4.3,1.974,1.974,0,0,0-1.132-1.056.239.239,0,0,1-.217-.249c0-.156.066-.233.2-.233a1.962,1.962,0,0,1,.284.031,12.7,12.7,0,0,0,1.5.052c.323,0,.712-.015,1.169-.049.274-.022.433-.034.477-.034.2,0,.293.075.293.226a.22.22,0,0,1-.233.241,1.148,1.148,0,0,0-.358.132.4.4,0,0,0-.222.374,2.8,2.8,0,0,0,.227.819c.153.393.453,1.108.906,2.14ZM848.7,337.2c.129,0,.39.017.785.047.3.026.54.036.717.036l1.668-.083c.14,0,.211.073.211.221a.208.208,0,0,1-.191.23.922.922,0,0,0-.575.238,1.817,1.817,0,0,0-.222.811c-.054.425-.08,1.38-.08,2.873q0,2.57.056,3.976a5.6,5.6,0,0,0,.22,1.733.576.576,0,0,0,.555.331c.18,0,.27.071.27.214a.254.254,0,0,1-.289.285c-.053,0-.134,0-.24-.008-.681-.073-1.149-.109-1.4-.109a7.614,7.614,0,0,0-1.214.069,2.564,2.564,0,0,1-.293.031c-.136,0-.2-.078-.2-.233a.2.2,0,0,1,.167-.217.829.829,0,0,0,.62-.386,4.009,4.009,0,0,0,.191-1.427c.023-.755.035-1.924.035-3.514,0-1.674-.022-2.8-.06-3.362a2.621,2.621,0,0,0-.2-1.044.716.716,0,0,0-.525-.244.22.22,0,0,1-.206-.253c0-.142.068-.214.2-.214Zm4.286,0c.129,0,.39.017.785.047.3.026.538.036.718.036l1.668-.083c.14,0,.211.073.211.221a.208.208,0,0,1-.191.23.917.917,0,0,0-.575.238,1.81,1.81,0,0,0-.223.811c-.053.425-.079,1.38-.079,2.873q0,2.57.056,3.976a5.588,5.588,0,0,0,.221,1.733.576.576,0,0,0,.555.331c.18,0,.268.071.268.214a.253.253,0,0,1-.288.285c-.054,0-.134,0-.24-.008-.681-.073-1.149-.109-1.4-.109a7.636,7.636,0,0,0-1.214.069,2.525,2.525,0,0,1-.292.031c-.136,0-.2-.078-.2-.233a.2.2,0,0,1,.166-.217.831.831,0,0,0,.621-.386,4.008,4.008,0,0,0,.191-1.427c.023-.755.034-1.924.034-3.514q0-2.512-.058-3.362a2.625,2.625,0,0,0-.2-1.044.718.718,0,0,0-.526-.244.22.22,0,0,1-.2-.253C852.785,337.276,852.852,337.2,852.985,337.2Z" transform="translate(-839.904 -337.203)" fill="#4f8ecc" fill-rule="evenodd"/>
        </g>
        <g id="Group_113" data-name="Group 113" transform="translate(0 57.43)">
          <path id="Path_223" data-name="Path 223" d="M646.172,538.77q.193,0,.784.047c.3.025.54.036.719.036l1.667-.083a.19.19,0,0,1,.213.218.208.208,0,0,1-.192.231.919.919,0,0,0-.575.239,1.826,1.826,0,0,0-.222.811c-.054.424-.08,1.379-.08,2.873q0,2.57.057,3.977a5.536,5.536,0,0,0,.221,1.732.575.575,0,0,0,.555.331c.18,0,.268.073.268.213a.254.254,0,0,1-.289.287c-.053,0-.132,0-.239-.008q-1.024-.109-1.4-.109a7.433,7.433,0,0,0-1.214.07,2.485,2.485,0,0,1-.292.03c-.136,0-.2-.078-.2-.233a.2.2,0,0,1,.167-.217.837.837,0,0,0,.621-.385,4.076,4.076,0,0,0,.191-1.427c.022-.755.034-1.923.034-3.515,0-1.673-.021-2.8-.06-3.362a2.618,2.618,0,0,0-.2-1.043.723.723,0,0,0-.527-.245.22.22,0,0,1-.2-.253c0-.142.066-.213.2-.213Zm8.074,3.93a9.159,9.159,0,0,0,1.175-2.732.716.716,0,0,0-.123-.444,1.007,1.007,0,0,0-.5-.289.233.233,0,0,1-.175-.253.185.185,0,0,1,.2-.213,3.787,3.787,0,0,1,.42.035,6.2,6.2,0,0,0,.689.048c.26,0,.6-.019,1.026-.056.2-.019.322-.027.368-.027.174,0,.262.077.262.236a.2.2,0,0,1-.2.23,1.256,1.256,0,0,0-.75.336,3.284,3.284,0,0,0-.7.872q-.321.554-1.45,2.743.621,1.574,1.435,3.326a9.773,9.773,0,0,0,1.218,2.167,1.42,1.42,0,0,0,.706.467c.171.028.257.109.257.242,0,.149-.088.224-.262.224l-2-.083c-.261,0-.52.01-.778.032-.225.014-.368.025-.433.031a2.575,2.575,0,0,0-.289.005.806.806,0,0,1-.125.014c-.105,0-.157-.075-.157-.228a.2.2,0,0,1,.183-.23c.4-.053.591-.258.591-.617a13.427,13.427,0,0,0-1.333-3.431,13.214,13.214,0,0,0-1.423,3.292q0,.576.868.764a.2.2,0,0,1,.191.209q0,.241-.195.241c-.064,0-.227-.01-.494-.036-.322-.031-.645-.047-.966-.047a5.831,5.831,0,0,0-.709.032,3.6,3.6,0,0,1-.434.05c-.209,0-.315-.083-.315-.25a.2.2,0,0,1,.169-.217,1.722,1.722,0,0,0,.785-.35,3.985,3.985,0,0,0,.709-1.033q.42-.778,1.584-3.156a43.907,43.907,0,0,0-1.92-4.3,1.963,1.963,0,0,0-1.132-1.054A.242.242,0,0,1,650,539c0-.156.068-.232.2-.232a1.887,1.887,0,0,1,.283.03,12.652,12.652,0,0,0,1.5.053q.485,0,1.169-.051c.273-.022.433-.033.477-.033.195,0,.293.074.293.225a.22.22,0,0,1-.233.241,1.142,1.142,0,0,0-.358.134.4.4,0,0,0-.223.372,2.818,2.818,0,0,0,.228.819C653.494,540.954,653.794,541.667,654.246,542.7Z" transform="translate(-645.949 -538.769)" fill="#4f8ecc" fill-rule="evenodd"/>
        </g>
        <g id="Group_114" data-name="Group 114" transform="translate(89.774 10.635)">
          <path id="Path_224" data-name="Path 224" d="M964.84,374.6h0a.54.54,0,0,1,.2.737l-3,5.193a.543.543,0,0,1-.737.2h0a.542.542,0,0,1-.2-.738l3-5.192A.539.539,0,0,1,964.84,374.6Z" transform="translate(-961.034 -374.53)" fill="#8bbae9" fill-rule="evenodd"/>
        </g>
        <g id="Group_115" data-name="Group 115" transform="translate(109.411 32.463)">
          <path id="Path_225" data-name="Path 225" d="M1036.152,451.41h0a.541.541,0,0,1-.2.737l-5.192,3a.542.542,0,0,1-.738-.2h0a.542.542,0,0,1,.2-.737l5.194-3A.54.54,0,0,1,1036.152,451.41Z" transform="translate(-1029.953 -451.141)" fill="#8bbae9" fill-rule="evenodd"/>
        </g>
        <g id="Group_116" data-name="Group 116" transform="translate(109.419 89.104)">
          <path id="Path_226" data-name="Path 226" d="M1036.182,653.74h0a.541.541,0,0,1-.737.2l-5.194-3a.54.54,0,0,1-.2-.736h0a.542.542,0,0,1,.737-.2l5.193,3A.544.544,0,0,1,1036.182,653.74Z" transform="translate(-1029.982 -649.934)" fill="#61a0e0" fill-rule="evenodd"/>
        </g>
        <g id="Group_117" data-name="Group 117" transform="translate(89.787 108.741)">
          <path id="Path_227" data-name="Path 227" d="M964.883,725.055h0a.541.541,0,0,1-.737-.2l-3-5.194a.542.542,0,0,1,.2-.737h0a.539.539,0,0,1,.735.2l3,5.193A.542.542,0,0,1,964.883,725.055Z" transform="translate(-961.076 -718.856)" fill="#61a0e0" fill-rule="evenodd"/>
        </g>
        <g id="Group_118" data-name="Group 118" transform="translate(33.145 108.749)">
          <path id="Path_228" data-name="Path 228" d="M762.55,725.083h0a.541.541,0,0,1-.2-.737l3-5.193a.539.539,0,0,1,.735-.2h0a.538.538,0,0,1,.2.735l-3,5.194A.543.543,0,0,1,762.55,725.083Z" transform="translate(-762.281 -718.883)" fill="#8bbae9" fill-rule="evenodd"/>
        </g>
        <g id="Group_119" data-name="Group 119" transform="translate(11.314 89.116)">
          <path id="Path_229" data-name="Path 229" d="M685.729,653.785h0a.54.54,0,0,1,.2-.737l5.192-3a.544.544,0,0,1,.738.2h0a.542.542,0,0,1-.2.737l-5.194,3A.541.541,0,0,1,685.729,653.785Z" transform="translate(-685.657 -649.978)" fill="#8bbae9" fill-rule="evenodd"/>
        </g>
        <g id="Group_120" data-name="Group 120" transform="translate(11.306 32.476)">
          <path id="Path_230" data-name="Path 230" d="M685.7,451.455h0a.54.54,0,0,1,.737-.2l5.194,3a.541.541,0,0,1,.2.737h0a.54.54,0,0,1-.737.2l-5.194-3A.542.542,0,0,1,685.7,451.455Z" transform="translate(-685.63 -451.186)" fill="#8bbae9" fill-rule="evenodd"/>
        </g>
        <g id="Group_121" data-name="Group 121" transform="translate(33.133 10.643)">
          <path id="Path_231" data-name="Path 231" d="M762.505,374.63h0a.542.542,0,0,1,.738.2l3,5.193a.54.54,0,0,1-.2.737h0a.538.538,0,0,1-.735-.2l-3-5.192A.541.541,0,0,1,762.505,374.63Z" transform="translate(-762.236 -374.558)" fill="#8bbae9" fill-rule="evenodd"/>
        </g>
      </g>
      <g id="Group_123" data-name="Group 123" transform="translate(6.858 3.331)">
        <path id="Path_232" data-name="Path 232" d="M598.228,357.251a69.963,69.963,0,1,1,69.959,69.959,69.958,69.958,0,0,1-69.959-69.959Zm113.267-67.219a79.975,79.975,0,1,0,36.645,67.22A79.577,79.577,0,0,0,711.495,290.031Z" transform="translate(-588.233 -277.297)" fill="#61a0e0" fill-rule="evenodd"/>
      </g>
      <g id="Group_124" data-name="Group 124" transform="translate(3.528 0)">
        <path id="Path_233" data-name="Path 233" d="M579.349,348.891a80.481,80.481,0,1,1,80.48,80.48,80.481,80.481,0,0,1-80.48-80.48Zm163.766,0a83.285,83.285,0,1,0-83.285,83.285A83.283,83.283,0,0,0,743.115,348.891Z" transform="translate(-576.546 -265.606)" fill="#61a0e0" fill-rule="evenodd"/>
      </g>
      <g id="Group_125" data-name="Group 125" transform="translate(86.595 14.244)">
        <rect id="Rectangle_101" data-name="Rectangle 101" width="0.435" height="3.252" fill="#61a0e0"/>
      </g>
      <g id="Group_126" data-name="Group 126" transform="translate(103.622 16.537)">
        <rect id="Rectangle_102" data-name="Rectangle 102" width="0.435" height="3.252" transform="translate(0.42 3.253) rotate(-164.953)" fill="#61a0e0"/>
      </g>
      <g id="Group_127" data-name="Group 127" transform="translate(119.478 23.385)">
        <rect id="Rectangle_103" data-name="Rectangle 103" width="0.436" height="3.252" transform="translate(0.378 3.034) rotate(-150.007)" fill="#61a0e0"/>
      </g>
      <g id="Group_128" data-name="Group 128" transform="translate(133.136 34.307)">
        <rect id="Rectangle_104" data-name="Rectangle 104" width="0.435" height="3.251" transform="translate(0.307 2.606) rotate(-135)" fill="#61a0e0"/>
      </g>
      <g id="Group_129" data-name="Group 129" transform="translate(143.63 48.551)">
        <rect id="Rectangle_105" data-name="Rectangle 105" width="0.436" height="3.252" transform="translate(0.218 2.003) rotate(-119.993)" fill="#61a0e0"/>
      </g>
      <g id="Group_130" data-name="Group 130" transform="translate(150.226 65.165)">
        <rect id="Rectangle_106" data-name="Rectangle 106" width="0.436" height="3.251" transform="translate(0.113 1.262) rotate(-104.987)" fill="#61a0e0"/>
      </g>
      <g id="Group_131" data-name="Group 131" transform="translate(152.532 83.021)">
        <rect id="Rectangle_107" data-name="Rectangle 107" width="3.25" height="0.436" fill="#61a0e0"/>
      </g>
      <g id="Group_132" data-name="Group 132" transform="translate(150.248 100.046)">
        <rect id="Rectangle_108" data-name="Rectangle 108" width="0.436" height="3.252" transform="translate(0 0.422) rotate(-75.013)" fill="#61a0e0"/>
      </g>
      <g id="Group_133" data-name="Group 133" transform="translate(143.611 115.914)">
        <path id="Path_234" data-name="Path 234" d="M1071.232,674.062l-.217.376-2.815-1.625.219-.378Z" transform="translate(-1068.2 -672.435)" fill="#61a0e0" fill-rule="evenodd"/>
      </g>
      <g id="Group_134" data-name="Group 134" transform="translate(133.117 129.582)">
        <rect id="Rectangle_109" data-name="Rectangle 109" width="0.435" height="3.251" transform="matrix(0.707, -0.707, 0.707, 0.707, 0, 0.307)" fill="#61a0e0"/>
      </g>
      <g id="Group_135" data-name="Group 135" transform="translate(119.467 140.05)">
        <path id="Path_235" data-name="Path 235" d="M985.465,759.96l-.378.217-1.626-2.814.379-.218Z" transform="translate(-983.461 -757.144)" fill="#61a0e0" fill-rule="evenodd"/>
      </g>
      <g id="Group_136" data-name="Group 136" transform="translate(103.593 146.666)">
        <path id="Path_236" data-name="Path 236" d="M929.008,783.505l-.422.113-.84-3.14.419-.113Z" transform="translate(-927.746 -780.365)" fill="#61a0e0" fill-rule="evenodd"/>
      </g>
      <g id="Group_137" data-name="Group 137" transform="translate(86.571 148.959)">
        <rect id="Rectangle_110" data-name="Rectangle 110" width="0.436" height="3.252" fill="#61a0e0"/>
      </g>
      <g id="Group_138" data-name="Group 138" transform="translate(68.721 146.657)">
        <rect id="Rectangle_111" data-name="Rectangle 111" width="0.435" height="3.252" transform="matrix(0.966, 0.257, -0.257, 0.966, 0.834, 0)" fill="#61a0e0"/>
      </g>
      <g id="Group_139" data-name="Group 139" transform="translate(52.093 140.04)">
        <rect id="Rectangle_112" data-name="Rectangle 112" width="0.436" height="3.252" transform="translate(1.627 0) rotate(30.014)" fill="#61a0e0"/>
      </g>
      <g id="Group_140" data-name="Group 140" transform="translate(37.86 129.545)">
        <path id="Path_237" data-name="Path 237" d="M697.349,722.882l-.309-.307,2.3-2.3.309.307Z" transform="translate(-697.04 -720.275)" fill="#61a0e0" fill-rule="evenodd"/>
      </g>
      <g id="Group_141" data-name="Group 141" transform="translate(26.946 115.904)">
        <rect id="Rectangle_113" data-name="Rectangle 113" width="0.437" height="3.251" transform="translate(2.818 0) rotate(60.087)" fill="#61a0e0"/>
      </g>
      <g id="Group_142" data-name="Group 142" transform="translate(20.109 100.018)">
        <path id="Path_238" data-name="Path 238" d="M634.853,617.905l-.111-.42,3.14-.843.111.422Z" transform="translate(-634.742 -616.642)" fill="#61a0e0" fill-rule="evenodd"/>
      </g>
      <g id="Group_143" data-name="Group 143" transform="translate(17.818 82.998)">
        <rect id="Rectangle_114" data-name="Rectangle 114" width="3.252" height="0.436" fill="#61a0e0"/>
      </g>
      <g id="Group_144" data-name="Group 144" transform="translate(20.111 65.147)">
        <rect id="Rectangle_115" data-name="Rectangle 115" width="0.435" height="3.251" transform="matrix(-0.259, 0.966, -0.966, -0.259, 3.253, 0.843)" fill="#61a0e0"/>
      </g>
      <g id="Group_145" data-name="Group 145" transform="translate(26.956 48.537)">
        <path id="Path_239" data-name="Path 239" d="M658.773,436.334l.218-.378,2.815,1.627-.217.378Z" transform="translate(-658.773 -435.957)" fill="#61a0e0" fill-rule="evenodd"/>
      </g>
      <g id="Group_146" data-name="Group 146" transform="translate(37.876 34.287)">
        <path id="Path_240" data-name="Path 240" d="M697.1,386.253l.307-.307,2.3,2.3-.309.306Z" transform="translate(-697.099 -385.946)" fill="#61a0e0" fill-rule="evenodd"/>
      </g>
      <g id="Group_147" data-name="Group 147" transform="translate(52.118 23.371)">
        <rect id="Rectangle_116" data-name="Rectangle 116" width="0.436" height="3.252" transform="matrix(-0.866, 0.5, -0.5, -0.866, 2.003, 2.817)" fill="#61a0e0"/>
      </g>
      <g id="Group_148" data-name="Group 148" transform="translate(68.747 16.535)">
        <path id="Path_241" data-name="Path 241" d="M805.448,323.753l.42-.114.842,3.141-.42.113Z" transform="translate(-805.448 -323.639)" fill="#61a0e0" fill-rule="evenodd"/>
      </g>
      <g id="Group_149" data-name="Group 149" transform="translate(106.538 9.633)">
        <path id="Path_242" data-name="Path 242" d="M938.325,299.416q3.088.86,6.072,1.96a79.394,79.394,0,0,1,23.684,13.85.49.49,0,1,1-.63.751A78.425,78.425,0,0,0,944.056,302.3q-2.934-1.082-5.971-1.927Z" transform="translate(-938.085 -299.416)" fill="#fff" fill-rule="evenodd"/>
      </g>
      <g id="Group_150" data-name="Group 150" transform="translate(23.716 125.4)">
        <path id="Path_243" data-name="Path 243" d="M672.141,729.47q-2.761-1.628-5.358-3.464A79.419,79.419,0,0,1,647.49,706.5a.491.491,0,0,1,.8-.563,78.412,78.412,0,0,0,19.057,19.27q2.553,1.8,5.267,3.407Z" transform="translate(-647.401 -705.727)" fill="#fff" fill-rule="evenodd"/>
      </g>
      <g id="Group_151" data-name="Group 151" transform="translate(20.144 10.89)">
        <path id="Path_244" data-name="Path 244" d="M635.772,338.671a.491.491,0,1,1-.834-.517,83.988,83.988,0,0,1,18.395-21.049,79.416,79.416,0,0,1,24.024-13.251.49.49,0,0,1,.323.926,78.443,78.443,0,0,0-23.732,13.09A82.945,82.945,0,0,0,635.772,338.671Z" transform="translate(-634.864 -303.828)" fill="#fff" fill-rule="evenodd"/>
      </g>
      <g id="Group_152" data-name="Group 152" transform="translate(127.229 103.075)">
        <path id="Path_245" data-name="Path 245" d="M1043.22,627.715a.491.491,0,1,1,.935.3,83.967,83.967,0,0,1-12.659,24.924,79.382,79.382,0,0,1-20.033,18.745.49.49,0,1,1-.539-.817,78.452,78.452,0,0,0,19.789-18.519A82.984,82.984,0,0,0,1043.22,627.715Z" transform="translate(-1010.703 -627.373)" fill="#fff" fill-rule="evenodd"/>
      </g>
      <g id="Group_161" data-name="Group 161" transform="translate(53.262 38.95)">
        <g id="Group_156" data-name="Group 156" transform="translate(0.842)">
          <g id="Group_153" data-name="Group 153" transform="translate(29.296 39.183)">
            <path id="Path_246" data-name="Path 246" d="M857.211,542.228a4,4,0,1,1,2.056,5.265A4,4,0,0,1,857.211,542.228Z" transform="translate(-856.873 -539.834)" fill="#ccc" fill-rule="evenodd"/>
          </g>
          <g id="Group_154" data-name="Group 154" transform="translate(0 20.157)">
            <path id="Path_247" data-name="Path 247" d="M754.053,473.059l8.3,9.906c4.039,4.82,5.629,4.781,11.328,7.65l10.784,5.432.922-1.32.909-1.327-8.889-8.174c-4.7-4.319-5.221-5.821-11.157-7.9Z" transform="translate(-754.053 -473.059)" fill="#ccc" fill-rule="evenodd"/>
          </g>
          <g id="Group_155" data-name="Group 155" transform="translate(32.058)">
            <path id="Path_248" data-name="Path 248" d="M921.341,402.312l-19.654,11.637c-9.565,5.663-10.821,7.824-19.221,15.671l-15.9,14.851.952,1.2.974,1.187,17.894-12.375c9.454-6.538,11.832-7.306,19.4-15.448Z" transform="translate(-866.567 -402.312)" fill="#ccc" fill-rule="evenodd"/>
          </g>
        </g>
        <g id="Group_160" data-name="Group 160" transform="translate(0 0.105)">
          <g id="Group_157" data-name="Group 157" transform="translate(29.296 39.184)">
            <path id="Path_249" data-name="Path 249" d="M854.258,542.6a4,4,0,1,1,2.056,5.265A4,4,0,0,1,854.258,542.6Z" transform="translate(-853.921 -540.207)" fill="#1e77d1" fill-rule="evenodd"/>
          </g>
          <g id="Group_158" data-name="Group 158" transform="translate(0 20.159)">
            <path id="Path_250" data-name="Path 250" d="M751.1,473.432l8.3,9.906c4.039,4.82,5.63,4.781,11.328,7.65l10.785,5.431.921-1.319.911-1.327-8.89-8.174c-4.7-4.319-5.221-5.821-11.157-7.9Z" transform="translate(-751.099 -473.432)" fill="#1e77d1" fill-rule="evenodd"/>
          </g>
          <g id="Group_159" data-name="Group 159" transform="translate(32.059)">
            <path id="Path_251" data-name="Path 251" d="M918.391,402.68l-19.653,11.638c-9.566,5.663-10.821,7.823-19.222,15.671l-15.9,14.85.953,1.2.973,1.186,17.894-12.374c9.455-6.538,11.833-7.307,19.4-15.448Z" transform="translate(-863.617 -402.68)" fill="#1e77d1" fill-rule="evenodd"/>
          </g>
        </g>
      </g>
    </g>
    <g id="Group_179" data-name="Group 179" transform="translate(403.136 375.023)">
      <g id="Group_166" data-name="Group 166" transform="translate(135.843 38.266)">
        <g id="Group_163" data-name="Group 163">
          <path id="Path_252" data-name="Path 252" d="M1217.244,1057.88c-11.2,0-20.276,1.678-20.276,3.748s9.077,3.746,20.276,3.746,20.273-1.676,20.273-3.746S1228.441,1057.88,1217.244,1057.88Z" transform="translate(-1196.968 -1057.88)" fill="#fff" fill-rule="evenodd"/>
        </g>
        <g id="Group_164" data-name="Group 164" transform="translate(0.001 3.748)">
          <path id="Path_253" data-name="Path 253" d="M1196.97,1180.2v-109.17c0,2.07,9.077,3.746,20.276,3.746s20.273-1.676,20.273-3.746V1180.2c0,2.07-9.077,3.748-20.273,3.748S1196.97,1182.274,1196.97,1180.2Z" transform="translate(-1196.97 -1071.033)" fill="#99c5f2" fill-rule="evenodd"/>
        </g>
        <g id="Group_165" data-name="Group 165" transform="translate(0.001 3.748)">
          <path id="Path_254" data-name="Path 254" d="M1196.97,1180.2v-109.17c0,.976,2.027,1.868,5.348,2.535V1182.74C1199,1182.072,1196.97,1181.18,1196.97,1180.2Z" transform="translate(-1196.97 -1071.033)" fill="#61a0e0" fill-rule="evenodd"/>
        </g>
      </g>
      <g id="Group_170" data-name="Group 170" transform="translate(203.769)">
        <g id="Group_167" data-name="Group 167" transform="translate(0.001 3.301)">
          <path id="Path_255" data-name="Path 255" d="M1435.37,1083.042V935.6c0-2.07,9.079,3.745,20.276,3.745s20.272-1.676,20.272-3.745v147.437c0,2.07-9.076,3.748-20.272,3.748S1435.37,1085.114,1435.37,1083.042Z" transform="translate(-1435.37 -935.159)" fill="#99c5f2" fill-rule="evenodd"/>
        </g>
        <g id="Group_168" data-name="Group 168" transform="translate(0.001 3.746)">
          <path id="Path_256" data-name="Path 256" d="M1435.37,1084.161V936.723c0,.978,2.029,1.868,5.349,2.537V1086.7C1437.4,1086.03,1435.37,1085.138,1435.37,1084.161Z" transform="translate(-1435.37 -936.723)" fill="#61a0e0" fill-rule="evenodd"/>
        </g>
        <g id="Group_169" data-name="Group 169">
          <path id="Path_257" data-name="Path 257" d="M1455.644,923.575c-11.2,0-20.276,1.676-20.276,3.746s9.079,3.746,20.276,3.746,20.272-1.676,20.272-3.746S1466.838,923.575,1455.644,923.575Z" transform="translate(-1435.368 -923.575)" fill="#fff" fill-rule="evenodd"/>
        </g>
      </g>
      <g id="Group_174" data-name="Group 174" transform="translate(67.926 74.506)">
        <g id="Group_171" data-name="Group 171" transform="translate(0 3.302)">
          <path id="Path_258" data-name="Path 258" d="M958.6,1270.036v-72.93c0-2.07,9.077,3.746,20.273,3.746s20.274-1.676,20.274-3.746v72.93c0,2.07-9.077,3.748-20.274,3.748S958.6,1272.107,958.6,1270.036Z" transform="translate(-958.596 -1196.66)" fill="#99c5f2" fill-rule="evenodd"/>
        </g>
        <g id="Group_172" data-name="Group 172" transform="translate(0 3.748)">
          <path id="Path_259" data-name="Path 259" d="M958.6,1271.154v-72.93c0,.975,2.026,1.868,5.347,2.534v72.933C960.622,1273.023,958.6,1272.131,958.6,1271.154Z" transform="translate(-958.596 -1198.224)" fill="#61a0e0" fill-rule="evenodd"/>
        </g>
        <g id="Group_173" data-name="Group 173" transform="translate(0.001)">
          <path id="Path_260" data-name="Path 260" d="M978.871,1185.07c-11.2,0-20.273,1.679-20.273,3.748s9.077,3.746,20.273,3.746,20.274-1.676,20.274-3.746S990.068,1185.07,978.871,1185.07Z" transform="translate(-958.598 -1185.07)" fill="#fff" fill-rule="evenodd"/>
        </g>
      </g>
      <g id="Group_178" data-name="Group 178" transform="translate(0 106.929)">
        <g id="Group_175" data-name="Group 175" transform="translate(0 3.3)">
          <path id="Path_261" data-name="Path 261" d="M720.193,1352.739V1310.9c0-2.07,9.077,3.748,20.274,3.748s20.274-1.679,20.274-3.748v41.842c0,2.07-9.078,3.748-20.274,3.748S720.193,1354.81,720.193,1352.739Z" transform="translate(-720.193 -1310.451)" fill="#99c5f2" fill-rule="evenodd"/>
        </g>
        <g id="Group_176" data-name="Group 176" transform="translate(0 3.746)">
          <path id="Path_262" data-name="Path 262" d="M720.193,1353.856v-41.843c0,.978,2.027,1.868,5.348,2.537v41.843C722.22,1355.725,720.193,1354.834,720.193,1353.856Z" transform="translate(-720.193 -1312.014)" fill="#61a0e0" fill-rule="evenodd"/>
        </g>
        <g id="Group_177" data-name="Group 177">
          <ellipse id="Ellipse_28" data-name="Ellipse 28" cx="20.274" cy="3.747" rx="20.274" ry="3.747" fill="#fff"/>
        </g>
      </g>
    </g>
    <g id="Group_205" data-name="Group 205" transform="translate(469.305 313.504)">
      <g id="Group_180" data-name="Group 180" transform="translate(23.595 26.862)">
        <path id="Path_263" data-name="Path 263" d="M1061.434,801.938c3.062,7.621,8.995,8.784,11.3,1.136q7.644,1.751,9.794,4.8,3.5,4.517,6.287,7.847a32.32,32.32,0,0,0-5.511,7.71,61.032,61.032,0,0,0-4.348-4.96q-4.22,6.79-14.277,27.959-7.317,6.455-22.461,2.991c3.206-17.59,6.83-25.217,7.572-34.53q-2.85,1.914-7.44,4.5a60.343,60.343,0,0,0-7.112-6.431q9.58-7.183,13.457-8.682A48.131,48.131,0,0,1,1061.434,801.938Z" transform="translate(-1035.241 -801.938)" fill="#61a0e0" fill-rule="evenodd"/>
      </g>
      <g id="Group_181" data-name="Group 181" transform="translate(43.936 0)">
        <path id="Path_264" data-name="Path 264" d="M1116.015,727.1q-3.531-1.671-2.908-3.926,1.918-1.5,4.263,2.614a2.631,2.631,0,0,0-.208-1.494q-2.167-2.531-1.662-4.143a17.49,17.49,0,0,0,7.095-3.63q.413.374.752.725c1.82-2.878,1.934-5.878.054-9.585-2.206,5.258-13.548,3.473-13.139,9.907a1.805,1.805,0,0,0-2.741-.285,2.573,2.573,0,0,1,2.258,1.8,3.685,3.685,0,0,1-3.147,2.11q.4,3.393,4.718,4.493a4.374,4.374,0,0,1-.968,1.81q1.275,2.339,5.436,1.453Q1116.8,728.454,1116.015,727.1Z" transform="translate(-1106.633 -707.66)" fill="#284191" fill-rule="evenodd"/>
      </g>
      <g id="Group_182" data-name="Group 182" transform="translate(49.953 8.86)">
        <path id="Path_265" data-name="Path 265" d="M1131.116,749.338q.78,1.354-.2,1.85a14.262,14.262,0,0,1-3.169,5.972c3.044,7.118,8.645,8.208,11,1.167-.54-.866-.638-2.361-1.033-4.608,3.907-.66,4.77-2.214,4.082-4.41q.4-6.2-1.864-7.511a5.3,5.3,0,0,0-1.486-2.314c-.226-.235-.477-.476-.752-.725a17.5,17.5,0,0,1-7.095,3.63q-.506,1.613,1.662,4.143a2.63,2.63,0,0,1,.209,1.494q-2.346-4.111-4.263-2.614Q1127.586,747.667,1131.116,749.338Z" transform="translate(-1127.751 -738.758)" fill="#fcb88d" fill-rule="evenodd"/>
      </g>
      <g id="Group_183" data-name="Group 183" transform="translate(56.543 23.152)">
        <path id="Path_266" data-name="Path 266" d="M1154.544,791.36c-.083-.531-.172-1.12-.286-1.773a7.7,7.7,0,0,1-3.378-.668Q1151.654,790.843,1154.544,791.36Z" transform="translate(-1150.88 -788.919)" fill="#f08261" fill-rule="evenodd"/>
      </g>
      <g id="Group_184" data-name="Group 184" transform="translate(72.29 33.793)">
        <path id="Path_267" data-name="Path 267" d="M1214.567,837.389q-2.086-2.051-3.99-3.793a32.338,32.338,0,0,0-4.427,6.052c3.206,3.09,6.163,4.371,8.5,4.656q7.453-1.65,19.646-8.555c2.3.093,3.668-.452,4.1-1.632a7.084,7.084,0,0,0,2.859-.521c1.162-.752.355-2.231-1.069-3.72,4.291-1.733,5.64-2.61,4.509-3.611-3.541,1.868-9.8,3.621-12.151,5.873Q1218.6,835.154,1214.567,837.389Z" transform="translate(-1206.15 -826.265)" fill="#fcb88d" fill-rule="evenodd"/>
      </g>
      <g id="Group_185" data-name="Group 185" transform="translate(19.043 38.813)">
        <path id="Path_268" data-name="Path 268" d="M1031.488,868.219c-3.694-9.707-3.411-14.563-5.506-16.937a34.342,34.342,0,0,0,4.449-2.4q-3.379-3.358-5.376-4.994a18.479,18.479,0,0,0-5.788,5.328c.089,4.5,2.48,12.19,9.02,20.651q-2,5.012.342,6.179a15.042,15.042,0,0,0,3.269-1.035,20.913,20.913,0,0,0,2.8-1.507,7.339,7.339,0,0,0-1.55-3.632A10.064,10.064,0,0,0,1031.488,868.219Z" transform="translate(-1019.267 -843.884)" fill="#fcb88d" fill-rule="evenodd"/>
      </g>
      <g id="Group_186" data-name="Group 186" transform="translate(31.703 69.362)">
        <path id="Path_269" data-name="Path 269" d="M1063.7,956.328q-.029,17.113,22.909,12.278a77.087,77.087,0,0,0,18.955-8.262q.564,9.21,21.286,17.692a5.006,5.006,0,0,0,2.828-3.253c-6.086-5.977-11.8-11.328-19.814-23.137-2.564-1.5-16.242.431-25.355,1.883Q1077.472,959.195,1063.7,956.328Z" transform="translate(-1063.7 -951.102)" fill="#4567c6" fill-rule="evenodd"/>
      </g>
      <g id="Group_187" data-name="Group 187" transform="translate(31.703 74.588)">
        <path id="Path_270" data-name="Path 270" d="M1063.7,969.444q-.029,17.113,22.909,12.278a77.087,77.087,0,0,0,18.955-8.262q.564,9.21,21.286,17.692a7.24,7.24,0,0,0,1.162-.747q-18.421-9.284-22.334-19.567-30.018,16.3-38.179-.747Q1065.668,969.853,1063.7,969.444Z" transform="translate(-1063.7 -969.444)" fill="#3b5bb3" fill-rule="evenodd"/>
      </g>
      <g id="Group_188" data-name="Group 188" transform="translate(4.876 75.133)">
        <path id="Path_271" data-name="Path 271" d="M990.995,999.366c-10.688,2.841-10.94,7.552-21.451,22.2q1.154,2.821,5.052,3.323,12.068-13.315,22.524-19.445,9.269-13.145,15.012-21.339-15.481.891-15.759-12.742Q992.25,990.616,990.995,999.366Z" transform="translate(-969.544 -971.357)" fill="#4567c6" fill-rule="evenodd"/>
      </g>
      <g id="Group_189" data-name="Group 189" transform="translate(97.356 87.831)">
        <path id="Path_272" data-name="Path 272" d="M1306.016,1019.05c-3.611,4.929-6.311,8.612-8.073,9.948-1.9,1.443-2.763.257-3.819-2.094,6.8-4.454,1.606-3.671,3.494-5.634a18.326,18.326,0,0,0,7.746-5.183C1307.219,1015.475,1307.692,1016.592,1306.016,1019.05Z" transform="translate(-1294.125 -1015.923)" fill="#284191" fill-rule="evenodd"/>
      </g>
      <g id="Group_190" data-name="Group 190" transform="translate(0 127.417)">
        <path id="Path_273" data-name="Path 273" d="M960.461,1166.885c-3.829-3.863-6.656-6.776-7.623-8.581-1.126-2.1.18-2.768,2.667-3.443,3.334,7.414,3.371,2.161,5.016,4.332a15.157,15.157,0,0,0,1.725,5.092,27.212,27.212,0,0,0,4.021,1.3c1.428,1.328.678,2.28-2.28,2a18.6,18.6,0,0,1-3.525-.7Z" transform="translate(-952.43 -1154.861)" fill="#284191" fill-rule="evenodd"/>
      </g>
      <g id="Group_191" data-name="Group 191" transform="translate(3.234 93.462)">
        <path id="Path_274" data-name="Path 274" d="M1055.774,1038.311a32.663,32.663,0,0,1,2.459,2.506c5.248-3.507,2.659-3.59,2.724-4.635a5.542,5.542,0,0,1-2.848-.5,4.964,4.964,0,0,1-2.335,2.625Zm-87.687,35.156a7.484,7.484,0,0,1,1.393-2.76,5.476,5.476,0,0,1-3.764-2.548,9.535,9.535,0,0,1-1.934,1.832C966.554,1076.009,966.953,1072.929,968.087,1073.468Z" transform="translate(-963.782 -1035.686)" fill="#fcb88d" fill-rule="evenodd"/>
      </g>
      <g id="Group_192" data-name="Group 192" transform="translate(30.573 35.106)">
        <path id="Path_275" data-name="Path 275" d="M1061.251,865.514c-.14.065-.279.13-.418.189-.092.042-.183.08-.274.117q-.415,2.043-.826,4.288c.153.034.3.07.456.1q.51-2.334,1.061-4.7Zm36.08-34.641a13.779,13.779,0,0,1-.858,8.286,61.034,61.034,0,0,1,4.348,4.96c.1-.2.2-.389.306-.586q-1.753-2.21-3.5-4.825a17.879,17.879,0,0,0-.3-7.834Zm-27.047,2.737q-1.465.9-2.977,1.969c-.585,7.352-2.966,13.65-5.52,24.628q.153.179.3.358c.1.125.188.246.275.371Q1065.652,847.7,1070.284,833.61Z" transform="translate(-1059.734 -830.873)" fill="#4f8ecc" fill-rule="evenodd"/>
      </g>
      <g id="Group_193" data-name="Group 193" transform="translate(9.4 87.189)">
        <path id="Path_276" data-name="Path 276" d="M1005.864,1033.954c-7.558,4.389-13.305,11.609-20.443,21.105.171.031.349.06.529.081q12.068-13.316,22.524-19.445,9.269-13.145,15.012-21.339a22.567,22.567,0,0,1-7.638-.685A203.652,203.652,0,0,1,1005.864,1033.954Z" transform="translate(-985.421 -1013.671)" fill="#3b5bb3" fill-rule="evenodd"/>
      </g>
      <g id="Group_204" data-name="Group 204" transform="translate(55.275 12)">
        <g id="Group_196" data-name="Group 196" transform="translate(1.959 2.706)">
          <g id="Group_194" data-name="Group 194" transform="translate(0.002 0.001)">
            <path id="Path_277" data-name="Path 277" d="M1153.737,759.3a.651.651,0,0,0-.409.761.529.529,0,0,0,.658.441.65.65,0,0,0,.41-.76A.531.531,0,0,0,1153.737,759.3Z" transform="translate(-1153.314 -759.278)" fill="#bbdbfa" fill-rule="evenodd"/>
          </g>
          <g id="Group_195" data-name="Group 195">
            <path id="Path_278" data-name="Path 278" d="M1153.98,760.5a.529.529,0,0,1-.659-.441.653.653,0,0,1,.41-.763.489.489,0,0,1,.354.029l-.042.008a.209.209,0,0,0-.132.246.171.171,0,0,0,.214.142.212.212,0,0,0,.132-.246l-.006-.022a.613.613,0,0,1,.139.287A.648.648,0,0,1,1153.98,760.5Z" transform="translate(-1153.306 -759.275)" fill="#125aa3" fill-rule="evenodd"/>
          </g>
        </g>
        <g id="Group_197" data-name="Group 197" transform="translate(0 1.677)">
          <path id="Path_279" data-name="Path 279" d="M1149.266,756.088a.219.219,0,0,1,.042.021.9.9,0,0,0-.142-.13.266.266,0,0,1,.078.038.516.516,0,0,1,.075.077.487.487,0,0,0-.045-.1.281.281,0,0,1,.066.1.429.429,0,0,0-.112-.2c.053.014.118.108.143.165a.3.3,0,0,0-.016-.1.183.183,0,0,1,.047.093l.016-.056c0,.029,0,.04,0,.062a.515.515,0,0,0,.051-.328.44.44,0,0,1-.012.134.731.731,0,0,0-.039-.189,1.337,1.337,0,0,0,.022.151.513.513,0,0,1-.008.144.635.635,0,0,0-.069-.283l.005.027a.774.774,0,0,1,.038.153c-.039-.083-.055-.167-.14-.206a.768.768,0,0,1,.071.084c-.047-.026-.083-.094-.144-.078a.9.9,0,0,1,.108.071.209.209,0,0,1,.083.13.312.312,0,0,0-.3-.183c.032,0,.135.036.155.073v.008c.008.045-.017-.036,0,.01a.231.231,0,0,1,.077.056.283.283,0,0,1,.039.048.163.163,0,0,1,.017.033.074.074,0,0,1,.005.017v.01a.375.375,0,0,0-.195-.174.462.462,0,0,0-.218-.031l0,0,.053.005a.4.4,0,0,1,.333.187c-.009,0,0,0-.014-.009l-.008-.006a.584.584,0,0,0-.118-.1.529.529,0,0,0-.146-.056.628.628,0,0,0-.311.032,1.138,1.138,0,0,1,.214-.022.36.36,0,0,1,.2.052.442.442,0,0,1-.079-.008,1.32,1.32,0,0,0-.567.056,1.024,1.024,0,0,1,.313-.01l-.057.009c.016,0,.032.007.052.009a.237.237,0,0,1,.05.005.591.591,0,0,1,.295.125c-.027-.013-.06-.023-.088-.035a.727.727,0,0,0-.089-.03,1,1,0,0,0-.184-.044,1.364,1.364,0,0,0-.379,0,.106.106,0,0,1,.058.013,3.9,3.9,0,0,0-.546.108c.059,0,.15-.03.218-.036h.063a1.8,1.8,0,0,1-.2.038,2.509,2.509,0,0,0-.6.17,1.159,1.159,0,0,0-.416.258,1.326,1.326,0,0,0-.213.285.961.961,0,0,0-.044.1,1.5,1.5,0,0,1,.261-.35,2.5,2.5,0,0,0-.376.642c.03-.043.068-.125.1-.18s.071-.118.11-.178a1.647,1.647,0,0,1,.292-.34,1.517,1.517,0,0,1-.106.143,6.075,6.075,0,0,0-.544.852c-.018.036-.05.119-.066.141.021-.019.087-.141.112-.179a2.078,2.078,0,0,1,.421-.521,5.283,5.283,0,0,0-.424.546l.254-.306a1.79,1.79,0,0,1,.312-.291,3.5,3.5,0,0,0-.562.63c.043-.039.1-.114.144-.163s.105-.109.161-.161a2.086,2.086,0,0,1,.364-.289,2.345,2.345,0,0,1,.533-.232c.053-.016.127-.031.175-.051a.544.544,0,0,0-.16.031,2.124,2.124,0,0,0-.319.105c-.042.016-.123.064-.158.071a2.488,2.488,0,0,1,.316-.175c-.01,0-.005,0-.014,0s-.005,0-.014,0a1.083,1.083,0,0,1,.265-.071c.078-.01.243-.008.25-.009a.328.328,0,0,1-.059-.021l.189-.027h-.242a1.549,1.549,0,0,1,.156-.017c.057-.006.171-.023.226-.022a.8.8,0,0,0,.232-.052,1.263,1.263,0,0,0-.136,0l-.132,0a.6.6,0,0,1-.068,0,1.517,1.517,0,0,0-.192-.01,1.2,1.2,0,0,0-.54.1,1.036,1.036,0,0,1,.555-.128l.268.009a.585.585,0,0,1,.066,0,1.762,1.762,0,0,0,.2,0l-.008-.012a.005.005,0,0,0,0,0s0,0,0,0a0,0,0,0,1,0,0,.362.362,0,0,0,.123,0c-.074-.036-.232-.024-.3-.082a.758.758,0,0,1,.182.034.464.464,0,0,0,.056.01.583.583,0,0,0-.234-.115l-.082-.017a.331.331,0,0,1,.2.038,1.006,1.006,0,0,1,.162.1.05.05,0,0,1,.012.007c.01.005.005,0,.013,0l-.084-.08c.017.017.114.068.126.095a.482.482,0,0,0-.1-.114l.042.013a.845.845,0,0,0-.337-.13.543.543,0,0,1,.345.1c.036.028.069.071.1.093l-.051-.07h.005l.054.035a.5.5,0,0,0-.2-.166.118.118,0,0,1,.074.019.331.331,0,0,1,.069.042.73.73,0,0,1,.126.13.266.266,0,0,0-.049-.089c.018,0,0,0,.018,0a.247.247,0,0,1,.083.062l-.029-.057.056.046a1.128,1.128,0,0,0-.077-.121.229.229,0,0,1,.092.086.186.186,0,0,0-.031-.07l.057.031c.016.012.032.028.047.039a.4.4,0,0,0-.089-.1c.054.013.085.054.135.095a.467.467,0,0,0-.139-.158c.052-.01.127.122.16.143l-.019-.062c.005,0,0,0,.012.005l.049.051-.028-.079c.026.014.036.039.061.066a.528.528,0,0,0-.042-.1c.012,0,0,0,.017.006s.048.044.075.06l-.042-.054c.008,0,0,0,.013,0Z" transform="translate(-1146.431 -755.663)" fill="#125aa3" fill-rule="evenodd"/>
        </g>
        <g id="Group_200" data-name="Group 200" transform="translate(5.49 1.059)">
          <g id="Group_198" data-name="Group 198" transform="translate(0.003)">
            <path id="Path_280" data-name="Path 280" d="M1166.135,753.513a.531.531,0,0,1,.659.442.653.653,0,0,1-.41.761.529.529,0,0,1-.659-.442A.652.652,0,0,1,1166.135,753.513Z" transform="translate(-1165.711 -753.492)" fill="#bbdbfa" fill-rule="evenodd"/>
          </g>
          <g id="Group_199" data-name="Group 199" transform="translate(0 0)">
            <path id="Path_281" data-name="Path 281" d="M1166.376,754.718a.651.651,0,0,0,.41-.761.53.53,0,0,0-.66-.442.572.572,0,0,0-.3.224l.04-.018a.172.172,0,0,1,.214.144.211.211,0,0,1-.132.246.173.173,0,0,1-.214-.143l0-.023a.693.693,0,0,0-.012.331A.533.533,0,0,0,1166.376,754.718Z" transform="translate(-1165.7 -753.493)" fill="#125aa3" fill-rule="evenodd"/>
          </g>
        </g>
        <g id="Group_201" data-name="Group 201" transform="translate(4.571)">
          <path id="Path_282" data-name="Path 282" d="M1162.789,751.087c-.006.016-.013.029-.018.044a1.2,1.2,0,0,1,.032-.2.384.384,0,0,0-.034.084.794.794,0,0,0-.014.115.813.813,0,0,1-.017-.109.341.341,0,0,0,.005.126.529.529,0,0,1-.023-.24.44.44,0,0,0-.017.233.275.275,0,0,1-.038-.088.241.241,0,0,0,.014.107l-.038-.032c.012.025.021.03.03.05a.431.431,0,0,1-.2-.223.437.437,0,0,0,.075.1.828.828,0,0,1-.068-.178c.025.035.038.093.06.134a.381.381,0,0,0,.078.108.71.71,0,0,1-.095-.271l.01.024a1.036,1.036,0,0,0,.051.148.427.427,0,0,1-.005-.262.638.638,0,0,0-.008.117c.019-.053.01-.134.06-.164a.858.858,0,0,0-.038.132.265.265,0,0,0,.006.161.41.41,0,0,1,.115-.358.387.387,0,0,0-.069.169l0,.006c.017.04-.007-.042,0,.009a.3.3,0,0,0-.024.1.5.5,0,0,0,0,.065.172.172,0,0,0,0,.038.1.1,0,0,1,0,.017l.005.009a.512.512,0,0,1,.047-.275.555.555,0,0,1,.135-.18.012.012,0,0,1,0,0l-.034.042a.533.533,0,0,0-.136.385c0-.008,0,0,.005-.016l0-.011a.845.845,0,0,1,.034-.162.639.639,0,0,1,.071-.147.651.651,0,0,1,.231-.2,1.155,1.155,0,0,0-.16.136.436.436,0,0,0-.113.185.647.647,0,0,0,.05-.062,1.35,1.35,0,0,1,.416-.361,1.151,1.151,0,0,0-.219.214l.044-.032a.4.4,0,0,1-.031.042l-.032.042a.774.774,0,0,0-.14.309c.012-.029.029-.06.043-.089s.028-.058.047-.089a1.292,1.292,0,0,1,.1-.164,1.464,1.464,0,0,1,.262-.267.137.137,0,0,0-.034.05,3.72,3.72,0,0,1,.431-.3c-.042.042-.119.083-.169.127l-.043.046a1.526,1.526,0,0,0,.153-.11,2.267,2.267,0,0,1,.5-.3.816.816,0,0,1,.413-.092.829.829,0,0,1,.289.074.581.581,0,0,1,.079.045c-.064-.012-.226-.109-.353-.091a1.694,1.694,0,0,1,.577.238c-.042-.013-.108-.05-.157-.071s-.109-.043-.165-.062a1.03,1.03,0,0,0-.37-.06.938.938,0,0,0,.144.035,4.056,4.056,0,0,1,.8.287c.03.014.094.057.115.064a1.266,1.266,0,0,1-.166-.062,1.347,1.347,0,0,0-.549-.11,3.527,3.527,0,0,1,.564.129l-.328-.06a1.194,1.194,0,0,0-.359-.006,2.256,2.256,0,0,1,.7.1c-.049,0-.124-.019-.179-.026s-.127-.012-.191-.014a1.535,1.535,0,0,0-.394.032,1.836,1.836,0,0,0-.481.2c-.046.026-.1.066-.145.084a.516.516,0,0,1,.123-.089,2.248,2.248,0,0,1,.271-.144c.038-.017.117-.038.146-.056a1.668,1.668,0,0,0-.306.087c.009-.005,0,0,.011-.008l.01-.012a.847.847,0,0,0-.218.132c-.06.047-.172.166-.178.171.01-.022.023-.039.031-.06l-.144.114.167-.172a1.342,1.342,0,0,0-.115.1,1.976,1.976,0,0,0-.166.144.764.764,0,0,1-.184.125.851.851,0,0,1,.095-.095l.092-.091c.016-.018.032-.031.045-.049.039-.052.084-.1.127-.145.079-.084.317-.3.424-.3-.14,0-.368.211-.446.295l-.18.2c-.017.017-.027.032-.044.051a1.7,1.7,0,0,1-.136.14v-.018a.005.005,0,0,1,0,0v0a.332.332,0,0,1-.086.087c.034-.082.148-.186.169-.281a.761.761,0,0,0-.106.157c-.012.018-.023.031-.035.047a.756.756,0,0,1,.1-.257l.048-.074a.407.407,0,0,0-.12.175,1.494,1.494,0,0,0-.063.191l-.005.014c0,.012,0,.005-.008.01l.018-.122a1.064,1.064,0,0,0-.039.164.648.648,0,0,1,.013-.161l-.022.04a1.085,1.085,0,0,1,.167-.344.686.686,0,0,0-.187.326c-.011.049-.012.105-.023.148v-.091s0,0,0,0l-.019.066a.677.677,0,0,1,.052-.27.138.138,0,0,0-.042.067.39.39,0,0,0-.026.082,1,1,0,0,0-.022.192.338.338,0,0,1-.01-.107c-.013.012,0,0-.013.016a.382.382,0,0,0-.026.108l-.009-.066-.016.077a1.434,1.434,0,0,1-.006-.149.284.284,0,0,0-.021.132.208.208,0,0,1-.014-.078l-.023.065c0,.022-.008.047-.013.065a.56.56,0,0,1,.013-.143.47.47,0,0,0-.045.171.584.584,0,0,1,.017-.223c-.043.027-.027.185-.04.226l-.017-.065a.044.044,0,0,0-.005.014l-.009.075-.021-.083a.487.487,0,0,0-.009.095.667.667,0,0,1-.021-.109c-.008.01,0,0-.009.017s-.01.07-.022.1l0-.074c-.006.008,0,0-.008.012Z" transform="translate(-1162.474 -749.776)" fill="#125aa3" fill-rule="evenodd"/>
        </g>
        <g id="Group_202" data-name="Group 202" transform="translate(4.519 2.694)">
          <path id="Path_283" data-name="Path 283" d="M1163.314,762.527a.1.1,0,0,1-.053-.016.1.1,0,0,1-.027-.132.515.515,0,0,1,.221-.148c.231-.11.494-.236.4-.673a13.667,13.667,0,0,0-1.515-2.149.1.1,0,0,1-.04-.13.1.1,0,0,1,.127-.042,11.694,11.694,0,0,1,1.616,2.281.736.736,0,0,1-.51.886.587.587,0,0,0-.142.08A.1.1,0,0,1,1163.314,762.527Z" transform="translate(-1162.291 -759.231)" fill="#fc9774"/>
        </g>
        <g id="Group_203" data-name="Group 203" transform="translate(4.804 6.677)">
          <path id="Path_284" data-name="Path 284" d="M1165.97,773.209c-.969.945-.591.882-1.962,1.264-.48.135-1.07.442-.444.429.877-.018,2.572-.846,2.474-1.667Z" transform="translate(-1163.293 -773.209)" fill="#fc9774" fill-rule="evenodd"/>
        </g>
      </g>
    </g>
    <g id="Group_235" data-name="Group 235" transform="translate(564.397 206.033)">
      <g id="Group_223" data-name="Group 223" transform="translate(0 0)">
        <g id="Group_206" data-name="Group 206" transform="translate(45.957 159.39)">
          <path id="Path_285" data-name="Path 285" d="M1448.388,889.88l-.327,5.764c-.114.729-.586,2.529-.586,3.2,0,2.493,11.087-2.109,8.607-3.222-.228-.1-1.756-1.419-1.832-2.27-.112-1.307-.054-3.383-.054-3.383Z" transform="translate(-1447.475 -889.88)" fill="#ffb27d" fill-rule="evenodd"/>
        </g>
        <g id="Group_207" data-name="Group 207" transform="translate(45.449 165.005)">
          <path id="Path_286" data-name="Path 286" d="M1454.776,909.687a38.514,38.514,0,0,1-3.935.95c-1.333.2-3.094-1.406-4.039-.976-1.035.482-1.108,5.974-1.108,5.974s13.653,1.035,17.149.036C1464.412,915.215,1460.1,911.461,1454.776,909.687Z" transform="translate(-1445.694 -909.587)" fill="#284191" fill-rule="evenodd"/>
        </g>
        <g id="Group_208" data-name="Group 208" transform="translate(67.338 159.39)">
          <path id="Path_287" data-name="Path 287" d="M1523.436,889.88l-.327,5.764c-.119.729-.591,2.529-.591,3.2,0,2.493,11.093-2.109,8.607-3.222-.223-.1-1.753-1.419-1.826-2.27-.114-1.307-.054-3.383-.054-3.383Z" transform="translate(-1522.518 -889.88)" fill="#ffb27d" fill-rule="evenodd"/>
        </g>
        <g id="Group_209" data-name="Group 209" transform="translate(66.836 165.005)">
          <path id="Path_288" data-name="Path 288" d="M1529.837,909.687a38.538,38.538,0,0,1-3.94.95c-1.331.2-3.09-1.406-4.034-.976-1.04.482-1.108,5.974-1.108,5.974s13.647,1.035,17.142.036C1539.473,915.215,1535.162,911.461,1529.837,909.687Z" transform="translate(-1520.755 -909.587)" fill="#284191" fill-rule="evenodd"/>
        </g>
        <g id="Group_210" data-name="Group 210" transform="translate(54.767)">
          <path id="Path_289" data-name="Path 289" d="M1501.27,339.049a6.293,6.293,0,0,0,.835,3,58,58,0,0,1-2.522,5.577s-7.471,15.228-7.9,16.043l-13.29,11.205,3.445,8.269s14.143-12.575,14.773-13.365,8.41-22.56,9.155-24.448c0,0,1.691-1.936,2.719-3.2a13.954,13.954,0,0,0,1.7-3.295,14.613,14.613,0,0,0,1.193-3.344c.384-1.655-.711-.248-.711-.248s-1.248,2.938-1.458,2.8c-.156-.112.612-1.678,1.058-3.5.114-.47.223-.926.327-1.358.259-1.11-.335-1.591-.789-.456a11.754,11.754,0,0,0-.524,1.567,9.467,9.467,0,0,1-.571,1.445,11.345,11.345,0,0,1-.6,1.48c-.034-.036-.22.087-.114-.419.13-.593.677-1.826.8-2.728.067-.506.272-1.567.272-1.567a10.15,10.15,0,0,0-.073-1.691c-.509-.172-.833.964-.838,1.667a7.792,7.792,0,0,0-.415,1.567s-.869,2.184-1.172,2.074a25.706,25.706,0,0,1,.415-2.889c.145-1.332.088-2.7-.161-2.764-.431-.123-.747,1.345-.747,1.345s-.171,1.038-.254,1.37c-.1.407-.472,1.58-.472,1.58s-1.5,4.011-1.78,3.875-.84-.778-.83-1.061.291-.79.3-1.062a.692.692,0,0,0-.545-.691c-.337-.036-.887,1.32-.887,1.32A4.688,4.688,0,0,0,1501.27,339.049Z" transform="translate(-1478.395 -330.464)" fill="#ffb27d" fill-rule="evenodd"/>
        </g>
        <g id="Group_211" data-name="Group 211" transform="translate(0 6.554)">
          <path id="Path_290" data-name="Path 290" d="M1296.96,360.915a6.2,6.2,0,0,1-.28,3.1,56.348,56.348,0,0,0,3.492,5.022s10.107,13.636,10.68,14.352l15.1,8.615-1.886,8.761s-16.192-9.811-16.953-10.476-12.356-20.659-13.432-22.387c0,0-2.021-1.593-3.255-2.641a14.069,14.069,0,0,1-2.27-2.925,15.158,15.158,0,0,1-1.782-3.073c-.68-1.555.659-.383.659-.383s1.759,2.665,1.935,2.492c.137-.135-.9-1.529-1.676-3.245-.2-.444-.387-.877-.561-1.284-.462-1.048.036-1.628.687-.591a11.846,11.846,0,0,1,.8,1.444,10.218,10.218,0,0,0,.825,1.32,11.475,11.475,0,0,0,.864,1.345c.023-.049.231.049.034-.431-.239-.568-.991-1.667-1.276-2.53-.161-.494-.555-1.493-.555-1.493a10.46,10.46,0,0,1-.236-1.679c.469-.272.993.789,1.128,1.481a7.817,7.817,0,0,1,.69,1.468s1.255,1.987,1.526,1.826a24.331,24.331,0,0,0-.929-2.765c-.386-1.283-.576-2.641-.342-2.751.4-.185.98,1.184.98,1.184s.353.987.5,1.307c.166.384.752,1.457.752,1.457s2.2,3.69,2.449,3.506a2,2,0,0,0,.628-1.2c-.062-.272-.431-.729-.493-1a.7.7,0,0,1,.421-.777c.322-.087,1.11,1.135,1.11,1.135A4.821,4.821,0,0,1,1296.96,360.915Z" transform="translate(-1286.178 -353.468)" fill="#ffb27d" fill-rule="evenodd"/>
        </g>
        <g id="Group_212" data-name="Group 212" transform="translate(50.084 84.962)">
          <path id="Path_291" data-name="Path 291" d="M1461.96,629.931s16.8,39.984,17.424,43.575c.452,2.641-.132,32.543-.132,32.543l7.946-.122,2.786-34.173L1479.53,628.66Z" transform="translate(-1461.96 -628.66)" fill="#3b5bb3" fill-rule="evenodd"/>
        </g>
        <g id="Group_213" data-name="Group 213" transform="translate(42.699 83.999)">
          <path id="Path_292" data-name="Path 292" d="M1436.04,629.388l7.656,44.4-5,29.719h9.046l7.112-31.951.974-35.012-3.664-11.268Z" transform="translate(-1436.04 -625.278)" fill="#4567c6" fill-rule="evenodd"/>
        </g>
        <g id="Group_214" data-name="Group 214" transform="translate(27.089 35.401)">
          <path id="Path_293" data-name="Path 293" d="M1418.8,454.713s1.987,8.947,3.383,10.341c0,0-7.1,4.739-6.773,6.627s7.865,37.024,7.865,37.024-15.515,8.083-25.165,6.786c0,0-7.264-40.046-9.142-41.922s-7.72-4.85-7.72-4.85,4.366-8.379,4.366-9.873c0,0,10.358,4.825,12.106,6.566s5.139-.457,5.956-.815C1403.68,464.6,1417.559,455.478,1418.8,454.713Z" transform="translate(-1381.252 -454.713)" fill="#61a0e0" fill-rule="evenodd"/>
        </g>
        <g id="Group_215" data-name="Group 215" transform="translate(42.013 41.462)">
          <path id="Path_294" data-name="Path 294" d="M1439.606,486.124a23.659,23.659,0,0,1-5.974-6.777,5.83,5.83,0,0,1,1.577-2.5c3.149,2.473,5.123,3.826,6.934,4.562a.283.283,0,0,0,.031.013c.01-.079.021-.16.034-.24a41.353,41.353,0,0,0,.648-5.2,5.473,5.473,0,0,1,1.823,2.049,15.808,15.808,0,0,1,1.691,6.63,20.13,20.13,0,0,0-1.832-1.708,11.522,11.522,0,0,0-2.366-1.531,11.272,11.272,0,0,0-1.5,2.227A18.593,18.593,0,0,0,1439.606,486.124Z" transform="translate(-1433.632 -475.985)" fill="#4f8ecc" fill-rule="evenodd"/>
        </g>
        <g id="Group_216" data-name="Group 216" transform="translate(40.633 26.387)">
          <path id="Path_295" data-name="Path 295" d="M1431.775,439.03a6.789,6.789,0,0,0,.776-2.267c-.223-.449-.464-1.021-.731-1.715q-4.958-2.564-1.806-4.457a6,6,0,0,1,1.945,2.119c.527-.085-.166-1.393.042-1.947-.892-2.11-.454-2.847-.809-4.188,1.24-2.418,4.913-3.542,9.647-3.5,1,4.217,3.753,14.11-1.46,14.519l.044.568a41.77,41.77,0,0,1-.648,5.2l-.065.226C1436.9,442.856,1434.927,441.5,1431.775,439.03Z" transform="translate(-1428.789 -423.075)" fill="#ffb27d" fill-rule="evenodd"/>
        </g>
        <g id="Group_217" data-name="Group 217" transform="translate(37.663 20.855)">
          <path id="Path_296" data-name="Path 296" d="M1433.2,409.572c-4.737-.043-8.218.7-9.458,3.119.355,1.341.1,2.228.991,4.337-.213.554.3,1.712-.223,1.8a6,6,0,0,0-1.945-2.119q-3.152,1.895,1.806,4.457c.267.694.508,1.266.731,1.715-5.715-2.349-9.089-9.527-4.783-13.732.088-6.361,9.845-1.921,14.034-5.488C1436.459,405.943,1434.6,409.347,1433.2,409.572Z" transform="translate(-1418.365 -403.659)" fill="#284191" fill-rule="evenodd"/>
        </g>
        <g id="Group_218" data-name="Group 218" transform="translate(47.659 40.907)">
          <path id="Path_297" data-name="Path 297" d="M1453.45,474.1a14.992,14.992,0,0,0,3.564-.064l.044.568q-.008.519-.054,1.043l-.031-.006A7.069,7.069,0,0,1,1453.45,474.1Z" transform="translate(-1453.45 -474.037)" fill="#f08261" fill-rule="evenodd"/>
        </g>
        <g id="Group_219" data-name="Group 219" transform="translate(36.83 26.889)">
          <path id="Path_298" data-name="Path 298" d="M1418.025,426.5a1.279,1.279,0,0,0-.135-1.656,2.553,2.553,0,0,0-2.449,1.114A3.873,3.873,0,0,1,1418.025,426.5Z" transform="translate(-1415.441 -424.839)" fill="#284191" fill-rule="evenodd"/>
        </g>
        <g id="Group_220" data-name="Group 220" transform="translate(38.823 24.481)">
          <path id="Path_299" data-name="Path 299" d="M1423.838,418.36c-.363.545-.747.708-1.146.49a2.854,2.854,0,0,1,.052-2.463A3.686,3.686,0,0,0,1423.838,418.36Z" transform="translate(-1422.438 -416.387)" fill="#284191" fill-rule="evenodd"/>
        </g>
        <g id="Group_221" data-name="Group 221" transform="translate(49.152 24.257)">
          <path id="Path_300" data-name="Path 300" d="M1458.719,418.243q-.233-2.028,1.227-2.643a4.47,4.47,0,0,1,3.349,3.326Q1462.3,418,1458.719,418.243Z" transform="translate(-1458.687 -415.6)" fill="#284191" fill-rule="evenodd"/>
        </g>
        <g id="Group_222" data-name="Group 222" transform="translate(45.411 21.859)">
          <path id="Path_301" data-name="Path 301" d="M1445.56,407.924c.288.542.127,1.331.96,1.39.887,2.392,2.363-1.215,2.192-2.132Q1448.25,407.645,1445.56,407.924Z" transform="translate(-1445.56 -407.182)" fill="#284191" fill-rule="evenodd"/>
        </g>
      </g>
      <g id="Group_234" data-name="Group 234" transform="translate(45.31 29.65)">
        <g id="Group_226" data-name="Group 226" transform="translate(2.27 1.351)">
          <g id="Group_224" data-name="Group 224" transform="translate(0.004 0)">
            <path id="Path_302" data-name="Path 302" d="M1453.7,439.269c-.28-.016-.508.261-.511.621a.627.627,0,0,0,.5.68c.28.018.508-.26.511-.619A.635.635,0,0,0,1453.7,439.269Z" transform="translate(-1453.187 -439.269)" fill="#bbdbfa" fill-rule="evenodd"/>
          </g>
          <g id="Group_225" data-name="Group 225">
            <path id="Path_303" data-name="Path 303" d="M1453.677,440.572a.635.635,0,0,1-.5-.682c.005-.359.233-.637.514-.62a.463.463,0,0,1,.309.156l-.039-.009c-.089-.005-.163.086-.166.2a.2.2,0,0,0,.163.22c.091.005.163-.084.166-.2l0-.025a.766.766,0,0,1,.07.339C1454.187,440.311,1453.959,440.588,1453.677,440.572Z" transform="translate(-1453.173 -439.268)" fill="#125aa3" fill-rule="evenodd"/>
          </g>
        </g>
        <g id="Group_227" data-name="Group 227" transform="translate(0 0.343)">
          <path id="Path_304" data-name="Path 304" d="M1448.836,436.248c.013.012.026.021.042.034a1.247,1.247,0,0,0-.13-.179.284.284,0,0,1,.077.065.561.561,0,0,1,.068.1.7.7,0,0,0-.026-.114.3.3,0,0,1,.047.123.457.457,0,0,0-.073-.242.384.384,0,0,1,.119.217.272.272,0,0,0,.008-.1.216.216,0,0,1,.029.11l.031-.052a.315.315,0,0,1-.016.064.48.48,0,0,0,.148-.314.436.436,0,0,1-.049.131.676.676,0,0,0,.006-.2,1.434,1.434,0,0,0-.016.16.476.476,0,0,1-.047.142.693.693,0,0,0,0-.309l0,.03a.885.885,0,0,1,0,.169c-.021-.1-.018-.189-.1-.257a.885.885,0,0,1,.059.11c-.047-.043-.07-.125-.145-.128a.769.769,0,0,1,.1.108.219.219,0,0,1,.06.161.393.393,0,0,0-.293-.289c.036.015.145.084.156.127v.008c-.006.049-.01-.043,0,.012a.324.324,0,0,1,.1.144c0,.011.01.028.013.038v.019l0,.012a.43.43,0,0,0-.174-.242.618.618,0,0,0-.238-.105s0,0,.005,0l.057.023a.5.5,0,0,1,.327.3c-.005-.006,0,0-.01-.014s-.005,0-.008-.009a.75.75,0,0,0-.109-.138.669.669,0,0,0-.153-.106.853.853,0,0,0-.363-.073,1.674,1.674,0,0,1,.254.049.5.5,0,0,1,.215.123c-.028-.007-.059-.025-.091-.035a1.774,1.774,0,0,0-.659-.137,1.39,1.39,0,0,1,.358.1l-.068-.011c.021.008.036.018.057.026l.057.023a.76.76,0,0,1,.3.228,1.092,1.092,0,0,0-.091-.066.936.936,0,0,0-.093-.062,1.332,1.332,0,0,0-.2-.105,1.827,1.827,0,0,0-.433-.126.128.128,0,0,1,.062.033,5.029,5.029,0,0,0-.654-.075c.07.018.182.019.259.036l.073.022a2.208,2.208,0,0,1-.233-.029,3.168,3.168,0,0,0-.731-.032,1.36,1.36,0,0,0-.545.119,1.329,1.329,0,0,0-.319.217c-.034.032-.049.056-.075.084a1.58,1.58,0,0,1,.389-.266,2.552,2.552,0,0,0-.6.523c.047-.034.109-.1.161-.148a2.265,2.265,0,0,1,.177-.142,1.566,1.566,0,0,1,.422-.245c-.031.029-.116.079-.158.108a5.922,5.922,0,0,0-.851.677c-.031.031-.091.1-.114.122.029-.014.14-.114.176-.144a2.167,2.167,0,0,1,.62-.385,5.771,5.771,0,0,0-.63.41l.374-.223a2.015,2.015,0,0,1,.433-.189,3.8,3.8,0,0,0-.809.449c.06-.026.143-.083.208-.117s.148-.074.226-.11a2.528,2.528,0,0,1,.493-.167,2.823,2.823,0,0,1,.672-.053c.065,0,.153.012.213.008a.715.715,0,0,0-.189-.022,2.993,2.993,0,0,0-.392,0,1.939,1.939,0,0,1-.2.019,3.139,3.139,0,0,1,.407-.07c-.013,0-.005,0-.018,0a.035.035,0,0,1-.016-.005,1.264,1.264,0,0,1,.322.017c.093.015.28.074.288.075a.645.645,0,0,1-.062-.04l.226.036-.28-.082c.054,0,.13.027.181.036.068.012.2.035.265.054a1.059,1.059,0,0,0,.278.027,1.15,1.15,0,0,0-.158-.044l-.15-.042a.649.649,0,0,1-.072-.023c-.073-.031-.146-.052-.218-.075a1.579,1.579,0,0,0-.643-.078,1.318,1.318,0,0,1,.667.058l.3.1c.029.008.047.018.075.026a1.869,1.869,0,0,0,.226.068l-.005-.016v0s0,0,0,0v0a.515.515,0,0,0,.143.04c-.078-.062-.259-.105-.327-.187a.98.98,0,0,1,.2.1c.021.013.042.019.062.03a.805.805,0,0,0-.233-.2l-.093-.045a.439.439,0,0,1,.223.108,1.233,1.233,0,0,1,.158.152.094.094,0,0,0,.013.01c.01.011.005.005.013.008l-.075-.11c.016.022.111.108.119.139a.539.539,0,0,0-.085-.149l.044.027a1.11,1.11,0,0,0-.348-.246.716.716,0,0,1,.363.221c.036.042.06.1.094.13l-.039-.088s0,0,0,0l.054.054a.608.608,0,0,0-.179-.235.156.156,0,0,1,.08.044.408.408,0,0,1,.065.066.892.892,0,0,1,.109.174.261.261,0,0,0-.031-.108c.021,0,0,0,.021.009a.287.287,0,0,1,.078.091l-.016-.067.049.065a.979.979,0,0,0-.055-.148.268.268,0,0,1,.083.118.191.191,0,0,0-.016-.082l.054.051c.016.018.029.039.044.056a.562.562,0,0,0-.078-.132c.06.033.083.085.13.142a.487.487,0,0,0-.116-.208c.065.005.112.166.145.2l-.008-.07c.005.005.005,0,.013.012l.044.068-.01-.091a.359.359,0,0,1,.052.088,1.043,1.043,0,0,0-.02-.117c.01.005,0,0,.016.012.031.023.044.062.07.087l-.034-.07a.074.074,0,0,1,.016.007Z" transform="translate(-1445.205 -435.731)" fill="#125aa3" fill-rule="evenodd"/>
        </g>
        <g id="Group_230" data-name="Group 230" transform="translate(6.743 0.888)">
          <g id="Group_228" data-name="Group 228" transform="translate(0.001)">
            <path id="Path_305" data-name="Path 305" d="M1469.345,437.644c.257.018.467.324.467.684s-.213.637-.472.617-.467-.326-.467-.684S1469.083,437.626,1469.345,437.644Z" transform="translate(-1468.873 -437.643)" fill="#bbdbfa" fill-rule="evenodd"/>
          </g>
          <g id="Group_229" data-name="Group 229" transform="translate(0 0)">
            <path id="Path_306" data-name="Path 306" d="M1469.338,438.947c.26.018.469-.26.472-.619s-.21-.665-.467-.683a.363.363,0,0,0-.291.117l.036,0c.083.007.15.105.15.222s-.067.2-.15.2a.2.2,0,0,1-.153-.222l0-.024a.782.782,0,0,0-.068.329C1468.871,438.62,1469.079,438.927,1469.338,438.947Z" transform="translate(-1468.871 -437.644)" fill="#125aa3" fill-rule="evenodd"/>
          </g>
        </g>
        <g id="Group_231" data-name="Group 231" transform="translate(5.857)">
          <path id="Path_307" data-name="Path 307" d="M1466.023,435.289a.255.255,0,0,0-.026.039,1.286,1.286,0,0,1,.07-.193.247.247,0,0,0-.047.074.591.591,0,0,0-.036.11.8.8,0,0,1,.005-.115.412.412,0,0,0-.021.127.614.614,0,0,1,.026-.249.477.477,0,0,0-.06.228.357.357,0,0,1-.018-.1.28.28,0,0,0-.008.113l-.028-.047c.005.03.013.038.018.061a.554.554,0,0,1-.137-.3.56.56,0,0,0,.049.125.981.981,0,0,1-.023-.2c.016.044.013.108.026.156a.623.623,0,0,0,.047.137.923.923,0,0,1-.031-.307l.005.027c.005.056.005.115.016.169a.481.481,0,0,1,.049-.267.657.657,0,0,0-.031.117c.029-.048.036-.132.088-.145a.881.881,0,0,0-.062.119.284.284,0,0,0-.023.167c.021-.183.088-.283.176-.322a.361.361,0,0,0-.1.144l0,.008c.008.048,0-.043,0,.01a.322.322,0,0,0-.057.156.192.192,0,0,0-.005.039v.019l0,.012a.5.5,0,0,1,.1-.261.435.435,0,0,1,.158-.134s0,0,0,0l-.039.031a.483.483,0,0,0-.2.34.12.12,0,0,0,.01-.015.016.016,0,0,1,0-.011.71.71,0,0,1,.065-.149.471.471,0,0,1,.093-.122.494.494,0,0,1,.249-.117.937.937,0,0,0-.171.079.376.376,0,0,0-.14.148.416.416,0,0,0,.06-.045,1.014,1.014,0,0,1,.451-.214.891.891,0,0,0-.244.136l.049-.017a.457.457,0,0,1-.039.033.482.482,0,0,0-.039.03.673.673,0,0,0-.187.261c.016-.025.036-.05.057-.077s.036-.048.057-.071a1.228,1.228,0,0,1,.13-.128,1.106,1.106,0,0,1,.3-.176.1.1,0,0,0-.042.04,2.721,2.721,0,0,1,.454-.153c-.047.027-.127.042-.179.069l-.049.028a1.382,1.382,0,0,0,.163-.054,1.65,1.65,0,0,1,.514-.121.711.711,0,0,1,.4.053.975.975,0,0,1,.252.178c.026.027.042.048.062.074-.057-.034-.187-.191-.306-.218a1.951,1.951,0,0,1,.48.447c-.034-.029-.088-.089-.127-.127s-.094-.082-.14-.122a1.06,1.06,0,0,0-.327-.192c.026.025.093.065.125.088a4.515,4.515,0,0,1,.674.571,1.355,1.355,0,0,0,.093.107,1.54,1.54,0,0,1-.14-.122,1.447,1.447,0,0,0-.48-.309,3.861,3.861,0,0,1,.49.332l-.288-.178a1.21,1.21,0,0,0-.329-.136,2.43,2.43,0,0,1,.623.348c-.047-.018-.112-.064-.161-.089s-.114-.057-.171-.082a1.366,1.366,0,0,0-.366-.109,1.436,1.436,0,0,0-.48.028c-.047.01-.109.029-.151.032a.407.407,0,0,1,.13-.046,1.6,1.6,0,0,1,.278-.049,1.235,1.235,0,0,0,.146,0,1.346,1.346,0,0,0-.3-.021c.01,0,.005,0,.013-.005s.005,0,.01-.009a.72.72,0,0,0-.226.057c-.062.026-.189.106-.2.109a.375.375,0,0,0,.042-.048l-.156.064.189-.114a1.114,1.114,0,0,0-.127.057,1.6,1.6,0,0,0-.179.086.6.6,0,0,1-.195.06.8.8,0,0,1,.106-.062l.1-.06a.333.333,0,0,0,.052-.031,1.339,1.339,0,0,1,.148-.1c.088-.057.348-.187.446-.154-.127-.048-.379.082-.464.139l-.208.135c-.018.011-.031.023-.049.035a1.336,1.336,0,0,1-.153.093l0-.017v-.007s0,0,0,0a.253.253,0,0,1-.1.056c.047-.07.171-.134.213-.223a.622.622,0,0,0-.13.119c-.016.014-.029.023-.042.036a.686.686,0,0,1,.145-.223l.06-.056c-.052.009-.109.082-.145.132a1.174,1.174,0,0,0-.1.17.047.047,0,0,0-.008.013c-.008.01,0,.005-.01.008l.042-.118a1.218,1.218,0,0,0-.07.152.72.72,0,0,1,.047-.158l-.031.034a.955.955,0,0,1,.223-.287c-.07.016-.192.166-.233.261-.021.047-.034.1-.052.14l.016-.092s0,0,0,0l-.031.06a.672.672,0,0,1,.1-.253c-.021.007-.036.03-.052.052a.366.366,0,0,0-.039.074,1.216,1.216,0,0,0-.06.187.46.46,0,0,1,.01-.112c-.013.008,0,0-.013.011a.333.333,0,0,0-.044.1l.005-.069-.031.07a1.318,1.318,0,0,1,.026-.153.287.287,0,0,0-.047.127.3.3,0,0,1,0-.083l-.034.057c-.01.019-.018.043-.026.06a.557.557,0,0,1,.042-.14.419.419,0,0,0-.078.157.632.632,0,0,1,.062-.219c-.047.013-.062.179-.083.214l-.005-.071c0,.005,0,0-.005.013l-.023.071,0-.089a.375.375,0,0,0-.026.092.855.855,0,0,1,0-.118.176.176,0,0,0-.01.014c-.018.027-.023.066-.039.095l.016-.075c-.008.005,0,0-.01.01Z" transform="translate(-1465.761 -434.527)" fill="#125aa3" fill-rule="evenodd"/>
        </g>
        <g id="Group_232" data-name="Group 232" transform="translate(5.357 2.045)">
          <path id="Path_308" data-name="Path 308" d="M1464.341,445.391a.115.115,0,0,1-.091-.046.109.109,0,0,1,.018-.15.59.59,0,0,1,.3-.079c.348-.04.618-.1.628-.525a14.76,14.76,0,0,0-1.144-2.687.112.112,0,1,1,.145-.171,13.065,13.065,0,0,1,1.227,2.867c-.021.65-.55.709-.83.742a.75.75,0,0,0-.181.033A.132.132,0,0,1,1464.341,445.391Z" transform="translate(-1464.007 -441.704)" fill="#fc9774"/>
        </g>
        <g id="Group_233" data-name="Group 233" transform="translate(2.842 7.063)">
          <path id="Path_309" data-name="Path 309" d="M1455.18,459.315q3.713,2.167,4.146.019A9.5,9.5,0,0,1,1455.18,459.315Z" transform="translate(-1455.18 -459.315)" fill="#fff" fill-rule="evenodd"/>
        </g>
      </g>
    </g>
    <g id="Group_274" data-name="Group 274" transform="translate(348.2 360.61)">
      <g id="Group_262" data-name="Group 262" transform="translate(0)">
        <g id="Group_236" data-name="Group 236" transform="translate(7.306 162.348)">
          <path id="Path_310" data-name="Path 310" d="M559.7,1450.66c-3.241-1.2-3.5-2.952-5.249-3.808s-1.961-2.4-.241-4.065c1.271,1.064,2.847,2.374,3.682,1.681.558-.462,2.447-.433,2.822.244,1.647,4.016,3.938,2.745,4.391,4.571C565.161,1451.717,561.193,1451.21,559.7,1450.66Z" transform="translate(-553.025 -1442.787)" fill="#284191" fill-rule="evenodd"/>
        </g>
        <g id="Group_237" data-name="Group 237" transform="translate(28.64 160.987)">
          <path id="Path_311" data-name="Path 311" d="M641.339,1443.248c-5.709-.1-9.977-.166-11.948-.82-2.127-.706-1.686-2.068-.466-4.244,5.381,2.6,3.947-.534,6.464-.137q2.654,2.241,7.856,3C644.714,1442.188,644.118,1443.2,641.339,1443.248Z" transform="translate(-627.903 -1438.011)" fill="#284191" fill-rule="evenodd"/>
        </g>
        <g id="Group_238" data-name="Group 238" transform="translate(0 24.208)">
          <path id="Path_312" data-name="Path 312" d="M566.608,959.671a36.891,36.891,0,0,0-7.676-1.577q-.617,9.852-9.8-.14c-5.9.521-7.049.6-10.322,1.715-7.078,7.095-7.729,15.287-11.428,24.647a24.178,24.178,0,0,1,8.72,1.751q.895-3.366,1.5-5.414-.169,18.51-.13,25.365c13.548,5.634,24.708,3.707,29.265-.685q.1-16.452.262-26.053a30.314,30.314,0,0,0,4.508,4.662c3.32-3.995,5.571-5.6,7.454-7.961A103.574,103.574,0,0,0,566.608,959.671Z" transform="translate(-527.382 -957.954)" fill="#61a0e0" fill-rule="evenodd"/>
        </g>
        <g id="Group_239" data-name="Group 239" transform="translate(46.154 31.687)">
          <path id="Path_313" data-name="Path 313" d="M709.746,990.461q-10.676,4.658-14.95,7.694l-1.266-1.966c-1.176,1.237-2.522,2.452-4.161,4.213a22.743,22.743,0,0,0,5.038,4.192q7.419-.832,16.4-11.793a7.165,7.165,0,0,0,4.112-2.122,3.792,3.792,0,0,0-1.357-2.833q3.724-2.545,2.965-3.645Q710.69,987.989,709.746,990.461Z" transform="translate(-689.369 -984.202)" fill="#ffb27d" fill-rule="evenodd"/>
        </g>
        <g id="Group_240" data-name="Group 240" transform="translate(6.112 71.587)">
          <path id="Path_314" data-name="Path 314" d="M582.078,1124.24c-.268,1.069.84,7.336,1.023,10.6,1.8,11.668.258,26.859-.712,46.39-1.735,6.882-4.3,16.055-4.13,28.963-3.022.97-4.117,1.071-6.844.342-1.908-13.474-.607-24.26,1.117-29.243-1.1-7.7-2.751-16.325-5.149-28.756-2.7,13.549-6.318,23.6-7.882,30.167-.31,7.315-1.72,17.189-2.457,29.619a12.078,12.078,0,0,1-5.969.615c-3.022-11.424-2.514-23.858-1.235-37.3,1.362-16.156-.883-34.4,1.365-40.727a48.68,48.68,0,0,0,1.607-9.99Q569.767,1130.85,582.078,1124.24Z" transform="translate(-548.832 -1124.24)" fill="#4f8ecc" fill-rule="evenodd"/>
        </g>
        <g id="Group_241" data-name="Group 241" transform="translate(10.083 38.217)">
          <path id="Path_315" data-name="Path 315" d="M564.863,1028.64c-.725.114-1.413.213-2.057.3q-.05,8.167-.027,12.234a8.5,8.5,0,0,0,2.243.734c-.005-.348-.109-1.157-.112-1.5q-.067-6.243-.046-11.77Zm33.124-10.907q-2.99-2.615-5.7-8.493a34.764,34.764,0,0,1,.242,5.479,29.718,29.718,0,0,0,4.28,4.381q.607-.734,1.173-1.367Zm-32.56-10.613q-1.317,4.452-2.519,8.69-.033,3.572-.058,6.719c.582-.057,1.262-.1,2.067-.117Q565.044,1013.723,565.427,1007.12Z" transform="translate(-562.772 -1007.12)" fill="#4f8ecc" fill-rule="evenodd"/>
        </g>
        <g id="Group_242" data-name="Group 242" transform="translate(8.826 157.788)">
          <path id="Path_316" data-name="Path 316" d="M579.576,1427.2a14.686,14.686,0,0,1,.058,3.16c4.5,2.054,3.738-.317,5.537-.374a8.589,8.589,0,0,1-.879-3.2,8.717,8.717,0,0,1-4.716.415Zm-20.98,2.165a9.454,9.454,0,0,1-.237,2.257c1.2,1,2.588,2.039,3.352,1.406a2.773,2.773,0,0,1,2.017-.231,24.367,24.367,0,0,1-.691-3.878A11.252,11.252,0,0,1,558.6,1429.364Z" transform="translate(-558.359 -1426.783)" fill="#ffb27d" fill-rule="evenodd"/>
        </g>
        <g id="Group_243" data-name="Group 243" transform="translate(6.112 82.192)">
          <path id="Path_317" data-name="Path 317" d="M583.1,1161.46q-11.924,11.666-31.9.073c-2.248,6.327,0,24.571-1.365,40.727-1.279,13.44-1.787,25.874,1.235,37.3.246.031.482.057.709.078a11.212,11.212,0,0,0,4.441-.446c.261-.075.533-.156.819-.246.737-12.431,2.147-22.3,2.457-29.619,1.564-6.563,5.18-16.617,7.882-30.167,2.4,12.431,4.046,21.054,5.149,28.756-1.724,4.983-3.025,15.77-1.117,29.243.5.132.939.239,1.349.316a8.734,8.734,0,0,0,4.716-.415c.246-.073.5-.156.78-.244-.166-12.908,2.395-22.081,4.13-28.963C583.36,1188.32,584.9,1173.127,583.1,1161.46Z" transform="translate(-548.833 -1161.46)" fill="#4567c6" fill-rule="evenodd"/>
        </g>
        <g id="Group_244" data-name="Group 244" transform="translate(6.463 146.853)">
          <path id="Path_318" data-name="Path 318" d="M571.435,1390.184a19.534,19.534,0,0,0,7.968.747c.052-.669.11-1.323.175-1.969a21.1,21.1,0,0,1-8.2-.558q.02.874.058,1.78Zm-21.194,2.316a18.568,18.568,0,0,0,8.382-.656c.06-.7.123-1.383.184-2.057a20.614,20.614,0,0,1-8.742.96c.052.586.111,1.173.176,1.754Zm7.973,4.374c.052-.687.1-1.37.157-2.042a20.86,20.86,0,0,1-7.73.674c.092.591.192,1.183.3,1.769a19.084,19.084,0,0,0,7.275-.4Zm21.047-3.809a20.95,20.95,0,0,1-7.666-.137q.064.883.148,1.79a19.216,19.216,0,0,0,7.433.322Q579.21,1394.035,579.261,1393.064Z" transform="translate(-550.065 -1388.404)" fill="#3b5bb3" fill-rule="evenodd"/>
        </g>
        <g id="Group_245" data-name="Group 245" transform="translate(28.892 29.641)">
          <path id="Path_319" data-name="Path 319" d="M629.337,977.024a.559.559,0,0,1,.5.589.514.514,0,0,1-.551.5.554.554,0,0,1-.5-.586A.52.52,0,0,1,629.337,977.024Z" transform="translate(-628.785 -977.022)" fill="#4f8ecc" fill-rule="evenodd"/>
        </g>
        <g id="Group_246" data-name="Group 246" transform="translate(19.97 22.891)">
          <path id="Path_320" data-name="Path 320" d="M603.195,964.635a27.27,27.27,0,0,1-5.723-8.711,6.728,6.728,0,0,1,2.206-2.594c3.2,3.336,5.23,5.2,7.179,6.338.011.005.022.013.034.018.024-.088.05-.176.077-.267a47.854,47.854,0,0,0,1.585-5.834,6.275,6.275,0,0,1,1.75,2.636,18.21,18.21,0,0,1,.854,7.844,23.42,23.42,0,0,0-1.812-2.247,13.294,13.294,0,0,0-2.453-2.132,12.985,12.985,0,0,0-2.078,2.3A21.53,21.53,0,0,0,603.195,964.635Z" transform="translate(-597.472 -953.33)" fill="#4f8ecc" fill-rule="evenodd"/>
        </g>
        <g id="Group_247" data-name="Group 247" transform="translate(19.901 5.838)">
          <path id="Path_321" data-name="Path 321" d="M599.536,910.552a7.836,7.836,0,0,0,1.256-2.464q-.271-.821-.558-2.075-5.245-3.735-1.339-5.383a6.926,6.926,0,0,1,1.877,2.734c.616-.013.036-1.616.367-2.215-.678-2.556-.057-3.326-.245-4.913,1.806-2.563,6.183-3.248,11.585-2.431.458,4.978,1.995,16.721-4.025,16.343l-.043.656a47.727,47.727,0,0,1-1.585,5.834l-.11.249C604.767,915.754,602.734,913.888,599.536,910.552Z" transform="translate(-597.228 -893.479)" fill="#ffb27d" fill-rule="evenodd"/>
        </g>
        <g id="Group_248" data-name="Group 248" transform="translate(17.17)">
          <path id="Path_322" data-name="Path 322" d="M605.349,879.555c-5.4-.817-9.5-.535-11.307,2.029.188,1.588-.254,2.558.424,5.113-.329.6.07,2-.546,2.016a6.928,6.928,0,0,0-1.877-2.734q-3.907,1.65,1.339,5.383.288,1.253.558,2.075c-6.146-3.611-8.835-12.356-3.238-16.457,1.136-7.25,11.557-.6,16.924-3.99C609.665,875.939,606.988,879.527,605.349,879.555Z" transform="translate(-587.646 -872.989)" fill="#284191" fill-rule="evenodd"/>
        </g>
        <g id="Group_249" data-name="Group 249" transform="translate(27.043 22.001)">
          <path id="Path_323" data-name="Path 323" d="M622.3,950.206a17.269,17.269,0,0,0,4.084.506l-.043.656c-.064.394-.141.789-.232,1.18a.181.181,0,0,1-.035-.01A8.169,8.169,0,0,1,622.3,950.206Z" transform="translate(-622.296 -950.206)" fill="#f08261" fill-rule="evenodd"/>
        </g>
        <g id="Group_250" data-name="Group 250" transform="translate(16.783 4.478)">
          <path id="Path_324" data-name="Path 324" d="M589.15,890.712a1.481,1.481,0,0,0,.117-1.915,2.944,2.944,0,0,0-2.979.874Q587.157,889.454,589.15,890.712Z" transform="translate(-586.287 -888.707)" fill="#284191" fill-rule="evenodd"/>
        </g>
        <g id="Group_251" data-name="Group 251" transform="translate(19.399 1.785)">
          <path id="Path_325" data-name="Path 325" d="M596.977,881.685q-.755.845-1.388.374a3.3,3.3,0,0,1,.459-2.8A4.242,4.242,0,0,0,596.977,881.685Z" transform="translate(-595.467 -879.255)" fill="#284191" fill-rule="evenodd"/>
        </g>
        <g id="Group_252" data-name="Group 252" transform="translate(31.069 3.362)">
          <path id="Path_326" data-name="Path 326" d="M636.426,887.607q.066-2.35,1.833-2.817a5.16,5.16,0,0,1,3.287,4.34Q640.549,887.916,636.426,887.607Z" transform="translate(-636.426 -884.79)" fill="#284191" fill-rule="evenodd"/>
        </g>
        <g id="Group_253" data-name="Group 253" transform="translate(27.459 0.322)">
          <path id="Path_327" data-name="Path 327" d="M623.757,874.454c.238.667-.074,1.538.87,1.743.624,2.877,2.9-1,2.849-2.078Q626.874,874.571,623.757,874.454Z" transform="translate(-623.757 -874.119)" fill="#284191" fill-rule="evenodd"/>
        </g>
        <g id="Group_254" data-name="Group 254" transform="translate(29.14 33.452)">
          <path id="Path_328" data-name="Path 328" d="M630.207,990.4a.557.557,0,0,1,.5.589.516.516,0,0,1-.55.506.558.558,0,0,1-.5-.589A.521.521,0,0,1,630.207,990.4Z" transform="translate(-629.656 -990.398)" fill="#4f8ecc" fill-rule="evenodd"/>
        </g>
        <g id="Group_255" data-name="Group 255" transform="translate(29.389 37.266)">
          <path id="Path_329" data-name="Path 329" d="M631.079,1003.786a.554.554,0,0,1,.5.586.516.516,0,0,1-.55.506.556.556,0,0,1-.5-.589A.516.516,0,0,1,631.079,1003.786Z" transform="translate(-630.528 -1003.784)" fill="#4f8ecc" fill-rule="evenodd"/>
        </g>
        <g id="Group_260" data-name="Group 260" transform="translate(7.091 36.563)">
          <g id="Group_256" data-name="Group 256" transform="translate(0 1.246)">
            <path id="Path_330" data-name="Path 330" d="M560.426,1005.74l18,3.657a2.613,2.613,0,0,1,2.036,3.074l-5.027,24.747a2.615,2.615,0,0,1-3.074,2.036l-18-3.658a2.616,2.616,0,0,1-2.036-3.074l5.027-24.748A2.615,2.615,0,0,1,560.426,1005.74Z" transform="translate(-552.271 -1005.688)" fill="#1e77d1" fill-rule="evenodd"/>
          </g>
          <g id="Group_257" data-name="Group 257" transform="translate(0.737 1.983)">
            <path id="Path_331" data-name="Path 331" d="M562.129,1008.314l18,3.658a1.873,1.873,0,0,1,1.46,2.2l-5.027,24.745a1.876,1.876,0,0,1-2.206,1.46l-18-3.658a1.875,1.875,0,0,1-1.46-2.2l5.027-24.747A1.874,1.874,0,0,1,562.129,1008.314Z" transform="translate(-554.859 -1008.276)" fill="#87bef5" fill-rule="evenodd"/>
          </g>
          <g id="Group_258" data-name="Group 258" transform="translate(2.186 2.406)">
            <path id="Path_332" data-name="Path 332" d="M565.349,1009.76,584,1013.7l-5.406,25.481-18.646-3.941Z" transform="translate(-559.943 -1009.76)" fill="#87bef5" fill-rule="evenodd"/>
          </g>
          <g id="Group_259" data-name="Group 259" transform="translate(11.642)">
            <path id="Path_333" data-name="Path 333" d="M594.4,1002.408l3.166.641.136-.667a1.338,1.338,0,0,1,1.571-1.04h0a1.338,1.338,0,0,1,1.041,1.572l-.136.667,3.166.643a.807.807,0,0,1,.628.95l-.3,1.5a.809.809,0,0,1-.949.628l-8.943-1.816a.81.81,0,0,1-.629-.949l.3-1.5a.807.807,0,0,1,.95-.628Zm4.715-.285a.589.589,0,1,0,.46.695A.589.589,0,0,0,599.117,1002.123Z" transform="translate(-593.131 -1001.315)" fill="#1e77d1" fill-rule="evenodd"/>
          </g>
        </g>
        <g id="Group_261" data-name="Group 261" transform="translate(0.708 50.649)">
          <path id="Path_334" data-name="Path 334" d="M530.532,1050.758a20.739,20.739,0,0,0-.385,8.586c1.61,2.454,8.107,1.082,24.832-1.852a4.453,4.453,0,0,0,3.723.892c1.75-.459,1.833-.939,3.8-2.879q.716-2.268-.293-3.463c-2.209,1.365-3.372,2.091-5.86,2.176.662-1.79.763-2.941.3-3.466a5.254,5.254,0,0,0-1.659,3.222c-13.187-.6-16.665-.519-18.419.446q.286-1.3.507-2.291A22.258,22.258,0,0,0,530.532,1050.758Z" transform="translate(-529.866 -1050.753)" fill="#ffb27d" fill-rule="evenodd"/>
        </g>
      </g>
      <g id="Group_273" data-name="Group 273" transform="translate(24.717 8.945)">
        <g id="Group_265" data-name="Group 265" transform="translate(2.769 1.349)">
          <g id="Group_263" data-name="Group 263" transform="translate(0.001 0.001)">
            <path id="Path_335" data-name="Path 335" d="M624.548,909.126c-.345-.044-.655.28-.69.727a.782.782,0,0,0,.562.89c.345.047.654-.277.689-.724A.787.787,0,0,0,624.548,909.126Z" transform="translate(-623.855 -909.122)" fill="#bbdbfa" fill-rule="evenodd"/>
          </g>
          <g id="Group_264" data-name="Group 264">
            <path id="Path_336" data-name="Path 336" d="M624.415,910.743a.789.789,0,0,1-.562-.892c.036-.446.345-.773.691-.727a.58.58,0,0,1,.371.223l-.048-.016c-.112-.016-.213.091-.223.236a.256.256,0,0,0,.182.288c.113.016.213-.091.224-.233v-.031a1.024,1.024,0,0,1,.056.425C625.072,910.463,624.762,910.787,624.415,910.743Z" transform="translate(-623.851 -909.119)" fill="#125aa3" fill-rule="evenodd"/>
          </g>
        </g>
        <g id="Group_266" data-name="Group 266">
          <path id="Path_337" data-name="Path 337" d="M618.688,905.2a.436.436,0,0,1,.048.047,1.3,1.3,0,0,0-.142-.233.371.371,0,0,1,.091.086.747.747,0,0,1,.073.135.768.768,0,0,0-.021-.143.379.379,0,0,1,.048.156.553.553,0,0,0-.07-.306.47.47,0,0,1,.129.28.3.3,0,0,0,.018-.13.237.237,0,0,1,.027.14l.044-.062c-.008.039-.018.049-.024.078a.6.6,0,0,0,.209-.379.568.568,0,0,1-.071.161.9.9,0,0,0,.024-.254,1.937,1.937,0,0,0-.034.2.548.548,0,0,1-.071.171.793.793,0,0,0,.026-.384l-.005.036a1,1,0,0,1-.014.208c-.017-.119,0-.233-.106-.327a.9.9,0,0,1,.064.143c-.054-.057-.075-.161-.166-.174a.973.973,0,0,1,.119.145c.032.052.08.14.058.2a.491.491,0,0,0-.337-.384c.042.023.171.119.182.174v.008c-.011.062-.009-.052,0,.016a.376.376,0,0,1,.083.109.418.418,0,0,1,.032.078.3.3,0,0,1,.01.049.129.129,0,0,1,0,.023l0,.016a.548.548,0,0,0-.193-.317.763.763,0,0,0-.291-.153.008.008,0,0,0,.006,0l.069.036a.636.636,0,0,1,.383.4c-.011-.01,0,0-.014-.018s0-.008-.008-.013a1.076,1.076,0,0,0-.123-.181.882.882,0,0,0-.179-.143,1.022,1.022,0,0,0-.444-.122,1.713,1.713,0,0,1,.306.083.621.621,0,0,1,.259.172.827.827,0,0,1-.107-.052,2.2,2.2,0,0,0-.808-.228,1.77,1.77,0,0,1,.437.15l-.084-.018c.023.01.043.023.068.036s.044.021.069.034a.93.93,0,0,1,.354.311,1.245,1.245,0,0,0-.106-.091,1.125,1.125,0,0,0-.112-.085,1.849,1.849,0,0,0-.235-.151,2.263,2.263,0,0,0-.525-.192.189.189,0,0,1,.075.047,6.331,6.331,0,0,0-.8-.153c.083.029.223.039.318.07l.087.031a2.332,2.332,0,0,1-.288-.054,3.949,3.949,0,0,0-.907-.107,1.538,1.538,0,0,0-1.1.343,1.3,1.3,0,0,0-.1.1,1.942,1.942,0,0,1,.509-.3,3.065,3.065,0,0,0-.792.6c.06-.039.145-.119.211-.171s.151-.109.231-.161a2.038,2.038,0,0,1,.547-.267,2.01,2.01,0,0,1-.208.119,7.4,7.4,0,0,0-1.115.768c-.04.034-.122.119-.152.14.036-.016.181-.13.23-.164a2.741,2.741,0,0,1,.807-.423,7.073,7.073,0,0,0-.821.451l.484-.244a2.374,2.374,0,0,1,.556-.195,4.568,4.568,0,0,0-1.045.482c.077-.026.187-.088.268-.125.1-.042.192-.08.291-.117a3.275,3.275,0,0,1,.627-.166,3.789,3.789,0,0,1,.839-.005,1.9,1.9,0,0,0,.263.029.886.886,0,0,0-.233-.044,3.426,3.426,0,0,0-.486-.039c-.065,0-.2.013-.252.008a3.473,3.473,0,0,1,.515-.052c-.017,0-.008,0-.022-.005s-.008,0-.019-.01a1.688,1.688,0,0,1,.4.052c.113.026.34.117.352.119a.537.537,0,0,1-.075-.057l.276.067-.339-.127c.066.008.157.044.223.06.082.023.248.062.322.093a1.315,1.315,0,0,0,.344.057,1.263,1.263,0,0,0-.191-.068l-.183-.065a.642.642,0,0,1-.092-.036,2.578,2.578,0,0,0-.263-.112,1.928,1.928,0,0,0-.792-.156,1.682,1.682,0,0,1,.825.132l.367.153c.034.01.058.026.091.036a2.532,2.532,0,0,0,.275.1l-.005-.018a.008.008,0,0,1,0-.005s0,0,0,0a.012.012,0,0,0,0-.005.542.542,0,0,0,.172.062c-.088-.083-.31-.15-.386-.259a1.252,1.252,0,0,1,.236.135c.024.018.05.029.074.044a.969.969,0,0,0-.273-.265l-.108-.065a.539.539,0,0,1,.263.153,1.734,1.734,0,0,1,.185.2c.007.008.005.008.013.013s.005.008.017.01l-.082-.143c.014.029.129.143.134.184a.649.649,0,0,0-.092-.192l.052.036a1.312,1.312,0,0,0-.412-.337.9.9,0,0,1,.435.306c.039.054.066.125.1.169l-.039-.111a.005.005,0,0,1,.005,0l.06.073a.734.734,0,0,0-.2-.309.247.247,0,0,1,.095.062.845.845,0,0,1,.078.088,1.051,1.051,0,0,1,.118.229.381.381,0,0,0-.03-.137c.025.007,0,0,.026.013a.367.367,0,0,1,.087.119l-.016-.086.058.086a1.314,1.314,0,0,0-.054-.189.324.324,0,0,1,.091.153.236.236,0,0,0-.013-.1l.065.07c.017.023.034.049.049.073a.617.617,0,0,0-.083-.171c.071.044.1.112.148.187a.656.656,0,0,0-.126-.267c.078.013.125.218.162.259l0-.088c.008.005,0,0,.014.016l.048.088-.005-.114a.429.429,0,0,1,.056.114.725.725,0,0,0-.016-.145c.016.005,0,0,.022.016.034.031.047.078.078.114l-.035-.093a.086.086,0,0,1,.018.013Z" transform="translate(-614.133 -904.384)" fill="#125aa3" fill-rule="evenodd"/>
        </g>
        <g id="Group_269" data-name="Group 269" transform="translate(8.361 1.168)">
          <g id="Group_267" data-name="Group 267" transform="translate(0.001 0.002)">
            <path id="Path_338" data-name="Path 338" d="M644.123,908.5c.32.044.552.444.52.89s-.319.77-.639.726a.789.789,0,0,1-.52-.892C643.515,908.774,643.8,908.45,644.123,908.5Z" transform="translate(-643.48 -908.492)" fill="#bbdbfa" fill-rule="evenodd"/>
          </g>
          <g id="Group_268" data-name="Group 268">
            <path id="Path_339" data-name="Path 339" d="M644,910.107c.321.044.607-.28.639-.726a.789.789,0,0,0-.52-.893.469.469,0,0,0-.368.119h.045a.258.258,0,0,1,.169.288c-.012.145-.105.252-.209.236a.258.258,0,0,1-.169-.291l0-.028a1.013,1.013,0,0,0-.113.4A.79.79,0,0,0,644,910.107Z" transform="translate(-643.476 -908.485)" fill="#125aa3" fill-rule="evenodd"/>
          </g>
        </g>
        <g id="Group_270" data-name="Group 270" transform="translate(7.351 0.108)">
          <path id="Path_340" data-name="Path 340" d="M640.237,905.573c-.012.016-.023.029-.035.047a1.734,1.734,0,0,1,.106-.234.354.354,0,0,0-.068.086.954.954,0,0,0-.053.135.964.964,0,0,1,.017-.143.513.513,0,0,0-.038.158.763.763,0,0,1,.056-.309.558.558,0,0,0-.1.28.415.415,0,0,1-.012-.13.31.31,0,0,0-.022.14l-.031-.062c.006.039.014.049.018.078a.671.671,0,0,1-.144-.379.736.736,0,0,0,.049.161,1.242,1.242,0,0,1-.014-.254,1.872,1.872,0,0,1,.021.195.652.652,0,0,0,.048.174,1.147,1.147,0,0,1-.012-.384l0,.036a1.262,1.262,0,0,0,.007.21.594.594,0,0,1,.082-.33,1.225,1.225,0,0,0-.048.143c.039-.057.057-.158.122-.171a1.123,1.123,0,0,0-.088.143.375.375,0,0,0-.045.2.467.467,0,0,1,.248-.382.445.445,0,0,0-.132.172v.01c.005.06.008-.054,0,.013a.365.365,0,0,0-.062.109.723.723,0,0,0-.025.078c0,.013-.006.036-.008.049a.078.078,0,0,0,0,.023v.016a.63.63,0,0,1,.145-.317.526.526,0,0,1,.21-.15s0,0-.005,0l-.049.034a.608.608,0,0,0-.281.4.081.081,0,0,0,.011-.018.056.056,0,0,0,.007-.013.969.969,0,0,1,.092-.179.635.635,0,0,1,.13-.142.621.621,0,0,1,.321-.125,1.132,1.132,0,0,0-.221.086.457.457,0,0,0-.188.169c.025-.01.052-.036.077-.049a1.273,1.273,0,0,1,.582-.226,1.036,1.036,0,0,0-.315.148l.061-.018c-.017.01-.031.026-.049.036s-.033.021-.049.034a.829.829,0,0,0-.259.309c.022-.031.052-.06.077-.088s.053-.057.082-.088a1.517,1.517,0,0,1,.171-.145,1.388,1.388,0,0,1,.38-.192.13.13,0,0,0-.054.047,3.239,3.239,0,0,1,.578-.15c-.061.029-.161.039-.23.067l-.064.034c.038,0,.158-.044.207-.054a2.013,2.013,0,0,1,.651-.1.873.873,0,0,1,.488.1,1.175,1.175,0,0,1,.294.244,1,1,0,0,1,.07.1c-.066-.047-.21-.254-.357-.3a2.491,2.491,0,0,1,.555.6c-.043-.036-.1-.119-.148-.171s-.107-.109-.162-.161a1.352,1.352,0,0,0-.388-.27,1.281,1.281,0,0,0,.148.122,5.461,5.461,0,0,1,.782.77c.03.031.087.119.108.14a2.163,2.163,0,0,1-.162-.163,1.8,1.8,0,0,0-.57-.425,4.593,4.593,0,0,1,.578.454l-.34-.244a1.4,1.4,0,0,0-.4-.2,3.015,3.015,0,0,1,.739.488c-.054-.026-.131-.091-.189-.127s-.136-.078-.206-.117a1.719,1.719,0,0,0-.446-.166,1.781,1.781,0,0,0-.6-.008,1.7,1.7,0,0,1-.188.026.54.54,0,0,1,.167-.044,1.949,1.949,0,0,1,.348-.036c.047,0,.143.016.18.008a1.635,1.635,0,0,0-.367-.052.032.032,0,0,0,.016-.005c.012-.005.005,0,.016-.011a.82.82,0,0,0-.285.05c-.083.029-.247.117-.255.117a.437.437,0,0,0,.056-.054l-.2.065.245-.125a1,1,0,0,0-.162.06,1.9,1.9,0,0,0-.232.091.766.766,0,0,1-.246.057.829.829,0,0,1,.137-.068l.132-.065a.462.462,0,0,0,.068-.036,1.8,1.8,0,0,1,.189-.112c.115-.062.45-.2.571-.151-.156-.073-.479.068-.593.13l-.266.151c-.025.01-.042.026-.066.036a1.5,1.5,0,0,1-.2.1l.005-.021v-.005s0-.005,0-.005a.365.365,0,0,1-.125.062c.065-.083.226-.15.281-.259a.939.939,0,0,0-.171.135c-.018.016-.036.026-.053.042a.861.861,0,0,1,.2-.262l.078-.065c-.065.005-.142.091-.192.15a1.839,1.839,0,0,0-.135.2.073.073,0,0,1-.011.016c-.009.013,0,.005-.013.01l.062-.145a1.37,1.37,0,0,0-.1.184.886.886,0,0,1,.069-.195l-.038.042a1.125,1.125,0,0,1,.3-.337c-.089.013-.256.19-.317.3-.029.054-.049.125-.077.169l.03-.114a.006.006,0,0,1,0,.005l-.044.073a.82.82,0,0,1,.149-.306.157.157,0,0,0-.07.062.483.483,0,0,0-.054.088,1.262,1.262,0,0,0-.091.223.5.5,0,0,1,.025-.135c-.018.005,0,0-.019.01a.411.411,0,0,0-.064.119l.012-.083-.043.083c.01-.077.029-.119.043-.187a.35.35,0,0,0-.067.153.291.291,0,0,1,.01-.1l-.048.07c-.013.021-.025.052-.036.073a.746.746,0,0,1,.062-.171.557.557,0,0,0-.108.187.787.787,0,0,1,.094-.267c-.054.013-.094.215-.119.259v-.088l-.009.013-.036.088.007-.111a.5.5,0,0,0-.043.111,1.131,1.131,0,0,1,.014-.145c-.012.008,0,0-.017.016s-.035.08-.057.114l.026-.091-.013.01Z" transform="translate(-639.935 -904.763)" fill="#125aa3" fill-rule="evenodd"/>
        </g>
        <g id="Group_271" data-name="Group 271" transform="translate(6.552 2.455)">
          <path id="Path_341" data-name="Path 341" d="M637.27,917.6a.14.14,0,0,1-.12-.065.136.136,0,0,1,.036-.187.776.776,0,0,1,.372-.07c.437-.018.776-.076.83-.6a18.088,18.088,0,0,0-1.186-3.44.139.139,0,1,1,.195-.2,16.146,16.146,0,0,1,1.268,3.668c-.083.8-.742.833-1.1.846a1.053,1.053,0,0,0-.23.026A.176.176,0,0,1,637.27,917.6Z" transform="translate(-637.128 -913)" fill="#fc9774"/>
        </g>
        <g id="Group_272" data-name="Group 272" transform="translate(3.025 8.451)">
          <path id="Path_342" data-name="Path 342" d="M624.75,934.046q4.417,3.023,5.145.392A11.782,11.782,0,0,1,624.75,934.046Z" transform="translate(-624.75 -934.046)" fill="#fff" fill-rule="evenodd"/>
        </g>
      </g>
    </g>
    <g id="Group_278" data-name="Group 278" transform="translate(630.165 156.454)">
      <g id="Group_275" data-name="Group 275" transform="translate(0.05 0.135)">
        <path id="Path_343" data-name="Path 343" d="M1555.015,189.11,1561.477,209l-16.922-12.291L1527.64,209h0l6.462-19.892-16.907-12.283-.014-.007h20.913l6.425-19.775.031-.095.006-.022.014.03.026.085,6.422,19.776h20.913Z" transform="translate(-1517.18 -156.929)" fill="#fc9774" fill-rule="evenodd"/>
      </g>
      <g id="Group_276" data-name="Group 276" transform="translate(27.029 0.396)">
        <path id="Path_344" data-name="Path 344" d="M1612.218,158.122l-.348,39.53.4-.287,16.89,12.265-.148-.532-6.282-19.334,16.631-12.081-4.69-.209h-15.939l-6.18-19.024-.228-.607Z" transform="translate(-1611.87 -157.844)" fill="#f57a44" fill-rule="evenodd"/>
      </g>
      <g id="Group_277" data-name="Group 277">
        <path id="Path_345" data-name="Path 345" d="M1545.927,159.332l5.917,15.781,16.317.726c.574.026,1.048.04,1.448.053,1.411.043,2.1.064,2.236.576.119.466-.392.857-1.39,1.616-.355.272-.794.6-1.375,1.068l-12.649,10.079,3.943,14.261c.228.839.441,1.555.617,2.149.5,1.7.752,2.547.283,2.887s-1.2-.162-2.649-1.16h0c-.509-.35-1.121-.77-1.863-1.261l-12.343-8.152-13.79,9.112c-.581.384-1.014.689-1.364.935h0c-.874.615-1.339.938-1.723.725-.469-.258-.291-.887.089-2.215.1-.357.218-.769.3-1.053l4.485-16.227-12.527-9.983-.005,0c-.633-.516-1.152-.911-1.559-1.222-.884-.674-1.367-1.041-1.31-1.477.078-.594.843-.616,2.516-.665l.96-.031,16.514-.736,5.886-15.7c.195-.515.355-.973.485-1.348.371-1.053.56-1.59,1.022-1.607.493-.017.69.541,1.1,1.672.131.376.29.828.436,1.206Zm5.174,16.378-6.021-16.062c-.208-.556-.335-.914-.444-1.217-.257-.715-.042-1.078-.213-1.073-.151.005.036.335-.2,1-.13.37-.288.821-.49,1.367l-5.992,15.985-.109.282-.3.013-16.815.749h-.005l-.96.03h0c-1.146.035-1.626-.292-1.647-.121-.01.083.348.172.96.642.433.328.976.742,1.583,1.24h0l12.758,10.164.233.187-.077.288L1528.8,205.7c-.143.515-.226.805-.3,1.06-.244.857-.685,1.087-.524,1.174.109.06.195-.271.765-.671h0c.379-.267.856-.6,1.385-.95l14.039-9.278.249-.165.252.165,12.589,8.317c.726.481,1.354.912,1.875,1.27,1.1.758,1.559,1.208,1.606,1.172s-.238-.61-.62-1.9c-.174-.6-.389-1.315-.623-2.163l-4.019-14.55-.083-.288.236-.187,12.882-10.265c.5-.4.991-.77,1.393-1.076.68-.517,1.089-.558,1.058-.669-.036-.143-.428.129-1.383.1-.423-.013-.919-.029-1.466-.052L1551.5,176l-.3-.013Z" transform="translate(-1517.006 -156.454)" fill="#f57a44" fill-rule="evenodd"/>
      </g>
    </g>
    <g id="Group_282" data-name="Group 282" transform="translate(551.432 175.526)">
      <g id="Group_279" data-name="Group 279" transform="translate(0.035 0.092)">
        <path id="Path_346" data-name="Path 346" d="M1266.668,245.719l4.419,13.6-11.574-8.4-11.565,8.4h0l4.422-13.6-11.562-8.4-.008-.005h14.3l4.391-13.519.023-.065v-.016l.011.021.017.058,4.394,13.52h14.3Z" transform="translate(-1240.8 -223.716)" fill="#fc9774" fill-rule="evenodd"/>
      </g>
      <g id="Group_280" data-name="Group 280" transform="translate(18.484 0.271)">
        <path id="Path_347" data-name="Path 347" d="M1305.784,224.532l-.234,27.029.265-.2,11.551,8.387-.1-.364-4.3-13.218,11.371-8.261-3.208-.142h-10.9l-4.225-13.006-.154-.415Z" transform="translate(-1305.55 -224.344)" fill="#f57a44" fill-rule="evenodd"/>
      </g>
      <g id="Group_281" data-name="Group 281" transform="translate(0 0)">
        <path id="Path_348" data-name="Path 348" d="M1260.447,225.36l4.047,10.789,11.154.5c.394.017.719.027.994.036.962.029,1.435.043,1.528.394.083.318-.27.585-.949,1.1-.244.185-.545.413-.942.729l-8.648,6.891,2.7,9.751c.158.575.3,1.064.423,1.47.345,1.161.516,1.742.194,1.974s-.82-.11-1.81-.793h0c-.348-.238-.765-.527-1.274-.861l-8.436-5.574L1249.988,258c-.394.261-.693.469-.932.638h0c-.6.42-.916.641-1.178.5-.322-.176-.2-.607.06-1.515.07-.244.15-.527.205-.72l3.063-11.1-8.564-6.825,0,0c-.432-.353-.786-.623-1.066-.835-.6-.462-.934-.712-.894-1.01.052-.406.576-.42,1.72-.455l.655-.021,11.292-.5,4.023-10.736c.133-.354.241-.665.335-.922.252-.721.381-1.087.695-1.1.337-.012.475.37.753,1.143.091.257.2.565.3.825Zm3.539,11.2-4.114-10.982c-.143-.38-.231-.625-.306-.831-.171-.489-.026-.738-.143-.734s.023.228-.137.684c-.088.253-.2.562-.337.935l-4.1,10.929-.073.192-.2.009-11.5.511h0l-.658.021h0c-.785.025-1.111-.2-1.127-.082-.008.057.237.118.658.438.294.225.667.507,1.082.847h0l8.721,6.95.161.128-.054.2-3.12,11.292c-.1.353-.153.551-.2.725-.169.586-.467.743-.358.8.073.04.135-.185.524-.459h0c.259-.183.586-.411.947-.65l9.6-6.343.171-.113.171.113,8.607,5.686c.5.328.926.624,1.282.868.752.519,1.066.826,1.1.8s-.166-.416-.425-1.3c-.119-.41-.265-.9-.425-1.48l-2.75-9.947-.052-.2.158-.129,8.807-7.018c.342-.272.68-.528.955-.737.464-.353.742-.381.724-.458-.026-.1-.293.088-.947.069-.288-.009-.628-.021-1-.036l-11.359-.506-.2-.009Z" transform="translate(-1240.675 -223.392)" fill="#f57a44" fill-rule="evenodd"/>
      </g>
    </g>
    <g id="Group_286" data-name="Group 286" transform="translate(636.247 249.65)">
      <g id="Group_283" data-name="Group 283" transform="translate(0.036 0.092)">
        <path id="Path_349" data-name="Path 349" d="M1564.348,505.873l4.419,13.6-11.571-8.4-11.565,8.4h0l4.416-13.6-11.559-8.4-.009-.005h14.3l4.391-13.52.026-.064,0-.016.009.021.017.058,4.394,13.52h14.3Z" transform="translate(-1538.48 -483.87)" fill="#fc9774" fill-rule="evenodd"/>
      </g>
      <g id="Group_284" data-name="Group 284" transform="translate(18.482 0.27)">
        <path id="Path_350" data-name="Path 350" d="M1603.459,484.683l-.239,27.028.271-.2,11.548,8.387-.1-.365-4.294-13.218,11.368-8.261-3.205-.143h-10.9l-4.226-13.007-.156-.415Z" transform="translate(-1603.22 -484.494)" fill="#f57a44" fill-rule="evenodd"/>
      </g>
      <g id="Group_285" data-name="Group 285" transform="translate(0)">
        <path id="Path_351" data-name="Path 351" d="M1558.126,485.515l4.047,10.789,11.154.5c.394.017.716.027.991.036.965.029,1.437.043,1.528.394.083.318-.267.585-.95,1.1-.244.185-.542.412-.942.729l-8.646,6.891,2.7,9.751c.156.575.3,1.064.42,1.47.342,1.161.516,1.742.195,1.974s-.817-.11-1.81-.793h0c-.345-.238-.763-.525-1.271-.861l-8.436-5.573-9.432,6.23c-.4.262-.7.471-.934.639h0c-.6.42-.913.641-1.178.5-.322-.176-.2-.607.063-1.515.067-.244.15-.527.2-.72l3.066-11.1-8.565-6.825-.006,0c-.43-.353-.783-.623-1.063-.835-.6-.46-.934-.712-.9-1.01.054-.406.576-.42,1.72-.455l.654-.021,11.295-.5,4.023-10.737c.132-.353.241-.665.332-.921.252-.721.382-1.087.7-1.1.337-.012.472.37.75,1.143.09.256.2.565.3.825Zm3.539,11.2-4.114-10.982c-.145-.38-.233-.625-.306-.833-.174-.487-.026-.737-.143-.733s.023.228-.137.683c-.088.253-.2.562-.337.935l-4.1,10.929-.073.192-.205.009-11.494.512h0l-.659.019h0c-.786.023-1.113-.2-1.128-.082-.008.057.236.118.656.439.3.225.67.507,1.085.847h0l8.721,6.95.161.129-.054.2-3.121,11.292c-.1.353-.156.551-.2.725-.169.586-.467.743-.358.8.073.04.132-.185.524-.459h0c.259-.183.586-.411.944-.648l9.6-6.344.172-.113.171.113,8.6,5.688c.5.327.926.623,1.284.866.752.519,1.063.826,1.1.8s-.166-.416-.426-1.3c-.119-.409-.267-.9-.425-1.48l-2.75-9.947-.054-.2.161-.129,8.807-7.018c.343-.272.68-.527.952-.737.464-.353.745-.381.724-.458-.026-.1-.293.088-.944.069-.291-.009-.628-.019-1-.036l-11.362-.506-.2-.009Z" transform="translate(-1538.354 -483.547)" fill="#f57a44" fill-rule="evenodd"/>
      </g>
    </g>
    <g id="Group_292" data-name="Group 292" transform="translate(688.224 334.127)">
      <g id="Group_287" data-name="Group 287">
        <path id="Path_352" data-name="Path 352" d="M1720.78,780.042l18.665,14.81-7.964,5.746Z" transform="translate(-1720.78 -780.042)" fill="#a4d0fc" fill-rule="evenodd"/>
      </g>
      <g id="Group_288" data-name="Group 288" transform="translate(0.633 0.236)">
        <path id="Path_353" data-name="Path 353" d="M1745.033,792.809,1723,780.871l28.66,10.607Z" transform="translate(-1723 -780.871)" fill="#a4d0fc" fill-rule="evenodd"/>
      </g>
      <g id="Group_289" data-name="Group 289">
        <path id="Path_354" data-name="Path 354" d="M1743.446,792.216l-3.368,9.593-.632-6.957-18.665-14.81.633.236Z" transform="translate(-1720.78 -780.042)" fill="#71afed" fill-rule="evenodd"/>
      </g>
      <g id="Group_290" data-name="Group 290" transform="translate(15.138 14.81)">
        <path id="Path_355" data-name="Path 355" d="M1778.07,838.977l-4.16-4.41,3.527-2.547Z" transform="translate(-1773.91 -832.02)" fill="#4f8ecc" fill-rule="evenodd"/>
      </g>
      <g id="Group_291" data-name="Group 291" transform="translate(22.258 18.785)">
        <path id="Path_356" data-name="Path 356" d="M1799.122,846.746a.4.4,0,0,1,.368-.721c.246.13.488.259.727.4a.405.405,0,0,1-.4.7c-.229-.133-.457-.257-.693-.379Zm63.771,11.766a.4.4,0,0,1,.646.488l-.5.648a.4.4,0,0,1-.633-.5l.482-.635Zm-2.04,2.444a.4.4,0,0,1,.589.555c-.189.2-.379.4-.573.591a.4.4,0,0,1-.568-.576c.188-.187.373-.376.553-.57Zm-2.311,2.186a.4.4,0,0,1,.516.622c-.21.174-.423.35-.638.517a.4.4,0,0,1-.5-.638c.207-.164.415-.33.617-.5Zm-2.562,1.888a.4.4,0,0,1,.439.68c-.231.15-.462.293-.7.436a.4.4,0,0,1-.415-.693c.227-.14.455-.278.674-.423Zm-2.772,1.554a.4.4,0,0,1,.348.731c-.246.119-.493.233-.745.345a.4.4,0,0,1-.324-.739c.243-.109.482-.22.721-.337Zm-2.943,1.209a.4.4,0,0,1,.254.763c-.259.088-.519.174-.778.254a.4.4,0,0,1-.233-.773c.253-.078.5-.161.757-.244Zm-3.072.846a.4.4,0,0,1,.169.791c-.267.057-.534.109-.8.161a.4.4,0,0,1-.146-.8c.262-.046.522-.1.781-.155Zm-3.148.5a.4.4,0,0,1,.083.8c-.272.029-.545.05-.82.07a.4.4,0,0,1-.052-.8l.789-.07Zm-3.18.15a.4.4,0,0,1-.008.807c-.272,0-.545-.005-.817-.013a.4.4,0,0,1,.026-.807c.263.005.531.013.8.013Zm-3.185-.179a.4.4,0,0,1-.088.8c-.272-.031-.545-.062-.815-.1a.4.4,0,0,1,.109-.8c.264.034.529.068.794.094Zm-3.151-.5a.4.4,0,0,1-.172.791c-.267-.057-.532-.119-.8-.182a.4.4,0,0,1,.189-.783c.263.06.522.12.781.174Zm-3.08-.82a.4.4,0,0,1-.254.768c-.259-.086-.516-.174-.773-.262a.4.4,0,0,1,.272-.763c.251.089.5.174.755.257Zm-2.972-1.149a.406.406,0,0,1-.34.737c-.249-.114-.492-.231-.739-.35a.386.386,0,0,1-.195-.506l-.132.026a.4.4,0,0,1-.148-.794c.259-.052.511-.109.768-.174a.4.4,0,0,1,.192.786l.594.274Zm-2.8-1.494a.4.4,0,0,1-.433.682c-.233-.145-.467-.293-.69-.449a.4.4,0,0,1,.457-.664c.219.148.442.291.667.431Zm-2.539-1.883a.4.4,0,0,1-.54.6c-.208-.182-.407-.374-.6-.565a.4.4,0,0,1,.566-.576c.188.184.379.363.576.539Zm-2.091-2.329a.4.4,0,0,1-.664.457c-.161-.228-.311-.467-.451-.708a.4.4,0,0,1,.7-.4c.13.223.27.446.415.653Zm-1.276-2.778a.4.4,0,0,1-.789.174c-.062-.28-.109-.555-.146-.84a.4.4,0,0,1,.8-.083,7.164,7.164,0,0,0,.13.75Zm.046-3a.4.4,0,0,1-.776-.231,7.367,7.367,0,0,1,.275-.807.4.4,0,0,1,.744.316,5.141,5.141,0,0,0-.243.721Zm1.407-2.734a.4.4,0,0,1-.649-.478c.161-.223.327-.441.5-.659a.4.4,0,0,1,.623.511c-.165.2-.324.412-.478.625Zm2.12-2.273a.4.4,0,0,1-.493-.638c.223-.176.446-.335.68-.493a.4.4,0,0,1,.444.677c-.218.145-.423.293-.631.454Zm2.713-1.4a.4.4,0,0,1-.205-.781,6.458,6.458,0,0,1,.838-.173.4.4,0,0,1,.109.8,5.976,5.976,0,0,0-.741.153Zm3.014-.085a.4.4,0,0,1,.169-.791,7.462,7.462,0,0,1,.823.213.405.405,0,0,1-.251.77,6.577,6.577,0,0,0-.74-.192Zm2.815,1.193a.4.4,0,0,1,.454-.669c.233.158.459.322.68.5a.4.4,0,0,1-.511.628c-.2-.16-.412-.314-.623-.456Zm2.26,2.109a.405.405,0,0,1,.641-.5c.171.223.332.452.488.685a.4.4,0,0,1-.674.441c-.144-.218-.3-.425-.454-.63Zm1.5,2.706a.4.4,0,0,1,.76-.27c.1.265.176.535.246.807a.4.4,0,0,1-.783.2,7.412,7.412,0,0,0-.223-.737Zm.479,3.017a.405.405,0,0,1,.809.039,7.867,7.867,0,0,1-.083.851.4.4,0,0,1-.8-.13,6.465,6.465,0,0,0,.073-.76Zm-.792,2.921a.4.4,0,0,1,.711.384,7.215,7.215,0,0,1-.449.729.4.4,0,0,1-.659-.467,5.556,5.556,0,0,0,.4-.646Zm-1.969,2.322a.4.4,0,0,1,.5.638c-.223.174-.441.337-.674.5a.4.4,0,0,1-.452-.672c.218-.145.42-.3.628-.462Zm-2.693,1.588a.4.4,0,0,1,.327.737c-.249.112-.5.221-.755.324a.4.4,0,0,1-.3-.75c.245-.1.487-.2.729-.311ZM1824.594,866c-.262,0-.524,0-.784-.021a.4.4,0,0,0-.054.807c.277.018.558.023.833.021a.4.4,0,0,0,.005-.807Zm-3.089-.4a.4.4,0,0,1-.215.778c-.262-.065-.544-.161-.8-.249a.405.405,0,0,1,.272-.763c.238.083.5.174.742.234Zm-2.872-1.219a.4.4,0,0,1-.4.7c-.239-.135-.482-.283-.71-.433a.4.4,0,0,1,.446-.674c.219.143.445.28.67.41Zm-2.573-1.826a.4.4,0,0,1-.524.615c-.208-.176-.412-.356-.617-.537a.4.4,0,0,1,.54-.6c.2.177.4.353.6.524Zm-2.314-2.194a.4.4,0,0,1-.584.558l-.558-.594a.4.4,0,0,1,.594-.55c.18.2.362.395.547.586Zm-2.14-2.392a.4.4,0,0,1-.615.524l-.524-.617a.405.405,0,0,1,.62-.521l.519.615Zm-2.063-2.48a.4.4,0,0,1-.623.514l-.514-.623a.4.4,0,0,1,.623-.514l.514.623Zm-2.074-2.488a.4.4,0,0,1-.615.527l-.524-.61a.4.4,0,0,1,.607-.532l.532.615Zm-2.18-2.42a.4.4,0,0,1-.581.56l-.56-.568a.4.4,0,0,1,.568-.574l.574.581Zm-2.385-2.244a.4.4,0,0,1-.517.62c-.2-.166-.41-.342-.617-.5a.4.4,0,0,1,.493-.638C1802.484,847.981,1802.7,848.154,1802.906,848.33Z" transform="translate(-1798.9 -845.974)" fill="#71afed" fill-rule="evenodd"/>
      </g>
    </g>
    <g id="Group_298" data-name="Group 298" transform="translate(303.499 287.178)">
      <g id="Group_293" data-name="Group 293">
        <path id="Path_357" data-name="Path 357" d="M370.495,615.263l22.231,9.114-6.1,7.8Z" transform="translate(-370.495 -615.263)" fill="#a4d0fc" fill-rule="evenodd"/>
      </g>
      <g id="Group_294" data-name="Group 294" transform="translate(0.68 0.049)">
        <path id="Path_358" data-name="Path 358" d="M397.569,620.826l-24.688-5.39,30.734,2.245Z" transform="translate(-372.881 -615.436)" fill="#a4d0fc" fill-rule="evenodd"/>
      </g>
      <g id="Group_295" data-name="Group 295">
        <path id="Path_359" data-name="Path 359" d="M395.863,620.7l-.577,10.235-2.559-6.56-22.231-9.114.68.049Z" transform="translate(-370.495 -615.263)" fill="#71afed" fill-rule="evenodd"/>
      </g>
      <g id="Group_296" data-name="Group 296" transform="translate(19.527 9.114)">
        <path id="Path_360" data-name="Path 360" d="M444.293,653.812l-5.263-3.106,2.7-3.454Z" transform="translate(-439.03 -647.252)" fill="#4f8ecc" fill-rule="evenodd"/>
      </g>
      <g id="Group_297" data-name="Group 297" transform="translate(30.034 13.852)">
        <path id="Path_361" data-name="Path 361" d="M476.379,664.944a.533.533,0,0,1,.11-1.061l1.065.115a.533.533,0,0,1-.119,1.06l-1.056-.114Zm36.45,42.385a.534.534,0,0,1,.386.9.542.542,0,0,1-.386.166l-.689,0a.533.533,0,0,1,.01-1.066l.678,0Zm-3.851-.1a.534.534,0,0,1-.061,1.066l-1.073-.068a.533.533,0,0,1,.079-1.063l1.055.065Zm-4.195-.4a.534.534,0,0,1-.159,1.056l.159-1.056Zm-4.074-.9a.534.534,0,0,1-.332,1.014c-.35-.112-.694-.236-1.036-.368a.532.532,0,0,1,.4-.988c.319.125.646.239.973.343Zm-3.616-1.821a.534.534,0,0,1-.671.83,7.411,7.411,0,0,1-.839-.791.533.533,0,0,1,.8-.705,6.436,6.436,0,0,0,.711.667Zm-1.995-3.231a.533.533,0,0,1-1.051.183,7.98,7.98,0,0,1-.126-1.137.533.533,0,0,1,1.066-.026,6.357,6.357,0,0,0,.11.98Zm.285-3.986a.533.533,0,0,1-1.027-.283c.093-.349.2-.711.315-1.051a.533.533,0,0,1,1.01.341c-.111.326-.209.66-.3.992Zm1.471-3.907a.533.533,0,0,1-.969-.445c.15-.326.3-.654.463-.975a.534.534,0,0,1,.958.47c-.157.314-.3.634-.452.951Zm1.9-3.782a.533.533,0,0,1-.942-.5l.5-.943a.533.533,0,0,1,.94.5c-.171.306-.336.63-.5.942Zm1.987-3.789a.533.533,0,0,1-.954-.476l.466-.949a.533.533,0,0,1,.962.459l-.473.966Zm1.73-3.981a.533.533,0,0,1-1.013-.332c.108-.329.2-.651.292-.987a.533.533,0,0,1,1.034.262c-.093.358-.2.7-.313,1.057Zm.535-4.427a.533.533,0,0,1-1.058.132,7.408,7.408,0,0,0-.18-.982.533.533,0,0,1,1.033-.265,8.581,8.581,0,0,1,.206,1.114Zm-1.639-4.175a.534.534,0,0,1-.862.629,9.242,9.242,0,0,0-.63-.789.534.534,0,0,1,.791-.716,9.948,9.948,0,0,1,.7.876Zm-3.22-3.04a.533.533,0,0,1-.594.885c-.288-.193-.578-.376-.876-.554a.533.533,0,0,1,.545-.917c.314.187.622.382.925.586Zm-3.852-2.044a.533.533,0,0,1-.412.983c-.322-.137-.646-.265-.973-.389a.533.533,0,0,1,.379-1c.337.128.674.26,1.006.4Zm-4.086-1.423a.534.534,0,0,1-.293,1.026l-1.017-.281a.533.533,0,0,1,.271-1.031l1.039.287Zm-4.182-1.022a.533.533,0,0,1-.214,1.044l-1.039-.206a.533.533,0,0,1,.2-1.047l1.053.209Zm-4.227-.751a.534.534,0,0,1-.158,1.056l-1.049-.154a.533.533,0,0,1,.148-1.056Z" transform="translate(-475.906 -663.879)" fill="#71afed" fill-rule="evenodd"/>
      </g>
    </g>
    <g id="Group_314" data-name="Group 314" transform="translate(276.805 432.373)">
      <g id="Group_299" data-name="Group 299" transform="translate(65.215 63.749)">
        <path id="Path_362" data-name="Path 362" d="M505.694,1348.6a17.088,17.088,0,0,0,.114,1.852A16.956,16.956,0,0,1,505.694,1348.6Z" transform="translate(-505.694 -1348.6)" fill="#ecece9" fill-rule="evenodd"/>
      </g>
      <g id="Group_300" data-name="Group 300" transform="translate(18.008)">
        <path id="Path_363" data-name="Path 363" d="M363.9,1131.95a7.837,7.837,0,0,0-2.105-1.759.456.456,0,0,0-.615.172c-.6,1.116-1.152,2.257-1.837,3.323a4.164,4.164,0,0,1-.834,1.022c-.018.013-.091.049-.123.065l-.014-.018a1.066,1.066,0,0,1-.119-.267c-.533-1.64.114-3.393-.306-5.043a5.64,5.64,0,0,0-1.406-2.423c-.684-.69-1.6-1.012-2.026-1.946a.36.36,0,0,0-.306-.218h-.084a.538.538,0,0,0-.393.221c-.986,1.419-2.366,2.882-2.628,4.656-.233,1.572.617,2.887,1.524,4.083a13.234,13.234,0,0,1,1.213,1.808c.188.353.512.994.171,1.341-.357.361-.9-.285-1.184-.576a21.452,21.452,0,0,1-1.569-1.943c-.83-1.095-2.144-2.758-3.706-2.457a1.445,1.445,0,0,0-1,1.463,5.462,5.462,0,0,0,.909,2.872c1.1,1.875,3.328,2.417,4.761,3.932.329.345,1.233,1.538.716,2.026-.181.171-.6.114-.817.08a3.4,3.4,0,0,1-1.525-.6c-1.612-1.245-2.2-3.414-3.893-4.594a3.764,3.764,0,0,0-2.779-.623,1.608,1.608,0,0,0-1.139,1.611,4.678,4.678,0,0,0,.793,2.5,5.73,5.73,0,0,0,2.056,2.028c1.71.97,3.868,1.092,5,2.879a1.178,1.178,0,0,1,.248.861c-.058.231-.385.306-.581.348a4.518,4.518,0,0,1-2.791-.54,10.049,10.049,0,0,1-2.319-2.137,6.12,6.12,0,0,0-2.646-1.756,1.919,1.919,0,0,0-2.2.521,2.812,2.812,0,0,0-.176,2.273,6.552,6.552,0,0,0,1.418,2.612,6.271,6.271,0,0,0,2.791,1.6c1.027.345,2.095.555,3.11.939a2.594,2.594,0,0,0,.772.192,5.16,5.16,0,0,1,.938.088c.259.067.087.34,0,.529-.209.451-.883.431-1.309.467-.4.031-.8.133-1.2.135a8.857,8.857,0,0,1-1.38-.153,6.241,6.241,0,0,0-2.752-.026,2.473,2.473,0,0,0-1.629,1.948c-.326,1.834,1.722,2.952,3.242,3.186a4.088,4.088,0,0,0,2.713-.55c1.056-.6,1.886-1.458,3.105-1.606a1.263,1.263,0,0,1-.528,1.243,7.15,7.15,0,0,1-2.14,1.043c-1.418.527-3.492,1-4.25,2.462a.458.458,0,0,0,.077.545,3.957,3.957,0,0,0,2.39,1.222,3.6,3.6,0,0,0,2.183-.724c1.136-.755,2.114-1.925,3.39-2.457a2.851,2.851,0,0,1-.827.822,3.263,3.263,0,0,0-1.141,1.953c-.267,1.172.139,2.441,1.515,2.449a4.323,4.323,0,0,0,2.13-.752,5.378,5.378,0,0,0,1.735-1.754,7.743,7.743,0,0,0,1.03-2.895c1.149,2.238,3.647,4.581,6,2.643,2.014-1.655-1.3-2.923-2.612-4.226.26.1.509.236.747.358.685.35,1.348.739,2.061,1.027,1.33.545,2.821.493,3.618-.877.686-1.18-.737-1.728-1.612-1.992-.712-.213-2.047-.444-2.563-1.162,1.757.192,3.713.41,5.229-.6a3.537,3.537,0,0,0,1.6-2.213c.067-.706-.7-.957-1.249-1.012-1.5-.145-2.96.514-4.463.467a5.937,5.937,0,0,1-1.073-.13,1.919,1.919,0,0,1-.392-.122.634.634,0,0,1-.127-.086.269.269,0,0,1,.042-.073,2.22,2.22,0,0,1,.477-.381,2.7,2.7,0,0,1,1.248-.449c.873-.094,1.746.016,2.618-.135a4.241,4.241,0,0,0,2.422-1.468,4.459,4.459,0,0,0,1.221-2.355,1.74,1.74,0,0,0-1.276-1.842c-2.28-.872-3.616,1.484-5.244,2.553a5.446,5.446,0,0,1-1.433.62c-.2.062-.79.265-.985.109-.115-.093.271-.713.345-.833a3.463,3.463,0,0,1,1.284-1.209c1.361-.778,3.021-1.019,4.093-2.252.7-.807,1.258-2.177.57-3.159-.73-1.045-2.358-.763-3.278-.156-1.156.762-1.739,2.314-3.13,2.682a2.34,2.34,0,0,1,.581-1.852,20.787,20.787,0,0,1,2.257-2.3,7.355,7.355,0,0,0,1.881-2.457A2.8,2.8,0,0,0,363.9,1131.95Z" transform="translate(-340.009 -1124.858)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_301" data-name="Group 301" transform="translate(41.53 25.681)">
        <path id="Path_364" data-name="Path 364" d="M457.309,1228.134a7.853,7.853,0,0,0,.078-2.742.449.449,0,0,0-.515-.376c-1.247.218-2.485.49-3.747.612a4.188,4.188,0,0,1-1.318-.021c-.022-.008-.095-.042-.128-.057,0-.008,0-.016.007-.026a1.073,1.073,0,0,1,.137-.256c.957-1.435,2.736-2.011,3.772-3.365a5.644,5.644,0,0,0,1.031-2.6c.121-.965-.195-1.886.275-2.8.171-.335-.183-.633-.488-.612-1.724.1-3.728-.075-5.284.815-1.38.791-1.886,2.272-2.262,3.725a13.453,13.453,0,0,1-.671,2.07c-.161.368-.463,1.017-.947.965-.507-.057-.335-.884-.279-1.287a20.559,20.559,0,0,1,.552-2.433c.346-1.331.841-3.393-.363-4.431a1.442,1.442,0,0,0-1.773.117,5.464,5.464,0,0,0-1.692,2.488c-.792,2.026.161,4.114-.141,6.174-.069.477-.446,1.922-1.151,1.816-.246-.034-.462-.4-.568-.586a3.435,3.435,0,0,1-.472-1.572c-.018-2.039,1.319-3.839,1.2-5.9a3.765,3.765,0,0,0-1.231-2.568,1.612,1.612,0,0,0-1.971.1,4.679,4.679,0,0,0-1.468,2.169,5.7,5.7,0,0,0-.321,2.869c.293,1.943,1.534,3.715.833,5.715a1.176,1.176,0,0,1-.523.726c-.218.1-.477-.117-.632-.241a4.524,4.524,0,0,1-1.3-2.527,9.969,9.969,0,0,1,.245-3.144,6.115,6.115,0,0,0-.26-3.165,1.917,1.917,0,0,0-1.773-1.408,2.82,2.82,0,0,0-1.894,1.269,6.548,6.548,0,0,0-1.174,2.729,6.3,6.3,0,0,0,.47,3.185c.364,1.017.861,1.987,1.188,3.025a2.494,2.494,0,0,0,.326.721,5.348,5.348,0,0,1,.512.794c.108.243-.213.277-.415.326-.486.114-.888-.425-1.178-.742-.274-.3-.6-.545-.851-.859a8.74,8.74,0,0,1-.734-1.18,6.241,6.241,0,0,0-1.684-2.177,2.47,2.47,0,0,0-2.538-.075c-1.641.879-1.252,3.177-.494,4.519a4.1,4.1,0,0,0,2.109,1.793c1.122.459,2.313.578,3.183,1.445a1.265,1.265,0,0,1-1.3.353,7.158,7.158,0,0,1-2.143-1.035c-1.289-.789-2.942-2.125-4.563-1.816a.457.457,0,0,0-.381.4,3.935,3.935,0,0,0,.521,2.633,3.587,3.587,0,0,0,1.918,1.268c1.294.425,2.818.469,4.026,1.141a2.829,2.829,0,0,1-1.157-.14,3.27,3.27,0,0,0-2.241.311c-1.087.519-1.831,1.621-.986,2.7a4.3,4.3,0,0,0,1.908,1.212,5.365,5.365,0,0,0,2.454.277,7.7,7.7,0,0,0,2.909-.983c-1.047,2.29-1.34,5.7,1.639,6.353,2.546.555,1.49-2.833,1.7-4.669.077.27.128.547.18.809.146.755.254,1.515.466,2.257.4,1.38,1.363,2.519,2.931,2.3,1.352-.19.9-1.647.566-2.5-.272-.693-.916-1.883-.674-2.732.938,1.5,1.978,3.173,3.712,3.733a3.558,3.558,0,0,0,2.726-.114c.6-.386.32-1.141.021-1.608-.812-1.271-2.234-2.008-3.127-3.217a5.8,5.8,0,0,1-.563-.923,2.1,2.1,0,0,1-.147-.382.728.728,0,0,1-.011-.156.314.314,0,0,1,.08-.01,2.133,2.133,0,0,1,.6.137,2.759,2.759,0,0,1,1.126.7c.612.633,1.067,1.383,1.725,1.974a4.233,4.233,0,0,0,2.651.994,4.452,4.452,0,0,0,2.6-.5,1.741,1.741,0,0,0,.659-2.143c-.725-2.332-3.4-1.92-5.25-2.537a5.775,5.775,0,0,1-1.376-.742c-.172-.119-.7-.459-.693-.708,0-.148.728-.231.866-.244a3.44,3.44,0,0,1,1.745.262c1.453.587,2.671,1.74,4.3,1.821,1.071.052,2.489-.361,2.837-1.51.368-1.219-.86-2.324-1.907-2.672-1.317-.436-2.892.068-4.044-.8a2.336,2.336,0,0,1,1.815-.69,20.881,20.881,0,0,1,3.206.345,7.3,7.3,0,0,0,3.092-.042A2.79,2.79,0,0,0,457.309,1228.134Z" transform="translate(-422.563 -1214.991)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_302" data-name="Group 302" transform="translate(0 12.857)">
        <path id="Path_365" data-name="Path 365" d="M291.982,1171.116a7.858,7.858,0,0,0-2.71-.423.453.453,0,0,0-.44.462c.061,1.266.174,2.529.135,3.795a4.164,4.164,0,0,1-.189,1.3c-.008.021-.053.088-.071.119a.05.05,0,0,1-.022-.008,1.108,1.108,0,0,1-.24-.169c-1.3-1.131-1.649-2.968-2.86-4.166a5.632,5.632,0,0,0-2.449-1.354c-.943-.241-1.894-.047-2.74-.625-.309-.212-.648.1-.669.407-.115,1.722-.545,3.689.141,5.344.61,1.468,2.016,2.156,3.407,2.713a13.38,13.38,0,0,1,1.97.929c.344.205.951.586.837,1.061-.119.5-.918.22-1.312.114a20.443,20.443,0,0,1-2.345-.858c-1.275-.509-3.257-1.261-4.441-.2a1.444,1.444,0,0,0-.108,1.772,5.458,5.458,0,0,0,2.257,1.995c1.907,1.04,4.1.361,6.105.918.462.13,1.85.688,1.656,1.375-.068.239-.458.4-.658.488a3.4,3.4,0,0,1-1.617.267c-2.024-.236-3.642-1.793-5.7-1.935a3.758,3.758,0,0,0-2.7.9,1.609,1.609,0,0,0-.149,1.966,4.674,4.674,0,0,0,1.965,1.73,5.7,5.7,0,0,0,2.807.683c1.964-.044,3.879-1.053,5.773-.1a1.173,1.173,0,0,1,.654.61c.07.228-.174.462-.319.6a4.512,4.512,0,0,1-2.671.976,10.075,10.075,0,0,1-3.088-.641,6.1,6.1,0,0,0-3.173-.143,1.911,1.911,0,0,0-1.62,1.58,2.8,2.8,0,0,0,1.017,2.036,6.528,6.528,0,0,0,2.56,1.513,6.293,6.293,0,0,0,3.217-.062c1.059-.233,2.082-.6,3.151-.8a2.606,2.606,0,0,0,.761-.231,4.958,4.958,0,0,1,.848-.407c.257-.077.249.244.271.452.052.5-.536.827-.882,1.074-.329.234-.617.527-.961.737a8.956,8.956,0,0,1-1.262.581,6.189,6.189,0,0,0-2.374,1.393,2.475,2.475,0,0,0-.394,2.509c.664,1.738,2.995,1.642,4.419,1.058a4.09,4.09,0,0,0,2.044-1.862c.6-1.056.866-2.221,1.837-2.975a1.266,1.266,0,0,1,.187,1.336,7.111,7.111,0,0,1-1.3,1.995c-.944,1.18-2.481,2.649-2.379,4.3a.46.46,0,0,0,.346.428,3.944,3.944,0,0,0,2.679-.184,3.576,3.576,0,0,0,1.5-1.743c.586-1.229.824-2.739,1.645-3.85a2.854,2.854,0,0,1-.288,1.131,3.269,3.269,0,0,0,.027,2.262c.375,1.144,1.375,2.021,2.558,1.318a4.312,4.312,0,0,0,1.44-1.738,5.342,5.342,0,0,0,.585-2.4,7.706,7.706,0,0,0-.606-3.011c2.139,1.328,5.484,2.049,6.509-.822.874-2.454-2.621-1.837-4.415-2.28.276-.044.559-.06.826-.075.766-.052,1.536-.062,2.3-.182,1.419-.218,2.672-1.033,2.65-2.615-.019-1.365-1.521-1.1-2.407-.877-.721.184-1.983.669-2.8.322,1.606-.737,3.4-1.557,4.173-3.206a3.545,3.545,0,0,0,.23-2.721c-.306-.638-1.091-.459-1.59-.226-1.362.648-2.275,1.964-3.588,2.7a5.812,5.812,0,0,1-.986.441,2.14,2.14,0,0,1-.4.1.748.748,0,0,1-.156-.01.3.3,0,0,1,0-.083,2.278,2.278,0,0,1,.211-.573,2.741,2.741,0,0,1,.839-1.03c.7-.524,1.506-.882,2.175-1.458a4.237,4.237,0,0,0,1.322-2.506,4.46,4.46,0,0,0-.165-2.646,1.744,1.744,0,0,0-2.043-.926c-2.4.428-2.337,3.134-3.183,4.89a5.748,5.748,0,0,1-.909,1.271c-.141.156-.542.631-.789.6-.148-.021-.135-.752-.132-.89a3.441,3.441,0,0,1,.479-1.7c.766-1.367,2.066-2.428,2.351-4.034.188-1.059-.042-2.517-1.139-3.007-1.163-.519-2.414.561-2.89,1.557-.6,1.248-.3,2.874-1.3,3.907a2.342,2.342,0,0,1-.455-1.886,20.933,20.933,0,0,1,.75-3.136,7.368,7.368,0,0,0,.35-3.071A2.8,2.8,0,0,0,291.982,1171.116Z" transform="translate(-276.805 -1169.982)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_303" data-name="Group 303" transform="translate(27.006 15.317)">
        <path id="Path_366" data-name="Path 366" d="M403.532,1189.706a7.875,7.875,0,0,0-.94-2.573.453.453,0,0,0-.617-.163c-1.078.664-2.128,1.375-3.257,1.953a4.288,4.288,0,0,1-1.232.467c-.023,0-.1-.005-.14-.005,0-.008,0-.016,0-.026a1.1,1.1,0,0,1,.032-.291c.361-1.686,1.8-2.879,2.265-4.519a5.626,5.626,0,0,0,0-2.8c-.244-.944-.876-1.681-.776-2.7.038-.371-.4-.516-.678-.389-1.564.734-3.493,1.307-4.611,2.711-.99,1.242-.913,2.807-.728,4.3a13.5,13.5,0,0,1,.141,2.171c-.014.4-.056,1.118-.524,1.245-.493.135-.638-.7-.735-1.089a21.175,21.175,0,0,1-.384-2.47c-.169-1.365-.47-3.461-1.973-3.984a1.452,1.452,0,0,0-1.6.763,5.466,5.466,0,0,0-.652,2.942c.011,2.174,1.665,3.761,2.147,5.79.11.467.295,1.951-.4,2.112-.244.057-.578-.2-.747-.34a3.409,3.409,0,0,1-1.018-1.284c-.769-1.888-.191-4.057-1.062-5.927a3.772,3.772,0,0,0-2.094-1.933,1.609,1.609,0,0,0-1.792.823,4.645,4.645,0,0,0-.564,2.555,5.686,5.686,0,0,0,.76,2.786c.99,1.7,2.8,2.887,2.882,5a1.176,1.176,0,0,1-.217.869c-.167.171-.488.067-.677.01a4.554,4.554,0,0,1-2.145-1.868,10.039,10.039,0,0,1-.933-3.014,6.125,6.125,0,0,0-1.407-2.846,1.927,1.927,0,0,0-2.167-.656,2.825,2.825,0,0,0-1.293,1.878,6.54,6.54,0,0,0-.083,2.973,6.263,6.263,0,0,0,1.609,2.783c.716.815,1.536,1.531,2.22,2.374a2.483,2.483,0,0,0,.57.555,5.091,5.091,0,0,1,.769.545c.191.187-.095.337-.266.457-.409.285-.982-.07-1.366-.254-.364-.173-.759-.285-1.109-.485a8.663,8.663,0,0,1-1.118-.823,6.258,6.258,0,0,0-2.369-1.406,2.47,2.47,0,0,0-2.385.872c-1.2,1.421.009,3.414,1.208,4.379a4.085,4.085,0,0,0,2.621.887c1.213.016,2.363-.317,3.492.169a1.277,1.277,0,0,1-1.08.812,7.181,7.181,0,0,1-2.374-.174c-1.489-.257-3.519-.892-4.911,0a.454.454,0,0,0-.208.508,3.93,3.93,0,0,0,1.455,2.254,3.561,3.561,0,0,0,2.252.469c1.36-.08,2.792-.6,4.164-.42a2.862,2.862,0,0,1-1.128.3,3.251,3.251,0,0,0-1.966,1.115c-.82.882-1.1,2.182.082,2.877a4.321,4.321,0,0,0,2.219.421,5.388,5.388,0,0,0,2.381-.648,7.726,7.726,0,0,0,2.343-1.984c-.127,2.511.859,5.79,3.868,5.3,2.572-.422.34-3.183-.14-4.968.171.22.322.459.467.687.415.646.8,1.313,1.266,1.922.878,1.136,2.2,1.839,3.571,1.056,1.186-.679.23-1.865-.4-2.532-.509-.545-1.546-1.414-1.634-2.293,1.424,1.048,3.006,2.22,4.827,2.1a3.537,3.537,0,0,0,2.49-1.113c.412-.578-.123-1.177-.573-1.5-1.224-.879-2.817-1.04-4.095-1.837a6.026,6.026,0,0,1-.864-.648,2.084,2.084,0,0,1-.276-.3.843.843,0,0,1-.068-.14.29.29,0,0,1,.073-.042,2.159,2.159,0,0,1,.6-.091,2.751,2.751,0,0,1,1.3.236c.8.358,1.5.892,2.331,1.2a4.229,4.229,0,0,0,2.832-.057,4.463,4.463,0,0,0,2.237-1.421,1.742,1.742,0,0,0-.179-2.236c-1.534-1.9-3.873-.529-5.817-.42a5.678,5.678,0,0,1-1.553-.182c-.2-.049-.815-.171-.9-.4-.053-.137.591-.48.715-.547a3.417,3.417,0,0,1,1.719-.4c1.567.008,3.125.631,4.67.1,1.014-.345,2.182-1.25,2.079-2.449-.109-1.271-1.657-1.842-2.759-1.777-1.383.08-2.663,1.129-4.052.75a2.336,2.336,0,0,1,1.432-1.312,20.991,20.991,0,0,1,3.108-.861,7.38,7.38,0,0,0,2.859-1.178A2.808,2.808,0,0,0,403.532,1189.706Z" transform="translate(-371.588 -1178.615)" fill="#a4d0fc" fill-rule="evenodd"/>
      </g>
      <g id="Group_304" data-name="Group 304" transform="translate(37.065 17.361)">
        <path id="Path_367" data-name="Path 367" d="M422.117,1186.118c-.521,2.267-.975,4.55-1.626,6.789a23.656,23.656,0,0,1-2.933,6.42c-2.706,4.13-6.153,7.712-8.941,11.785a.377.377,0,0,0,0,.457,12.2,12.2,0,0,0-1.073,3.274,36.575,36.575,0,0,0-.586,5.852c-.1,2.392-.077,4.771.034,7.16.131,2.809.317,5.621.2,8.436-.008.163-.017.327-.026.49-.032.581.87.578.9,0a77.979,77.979,0,0,0-.14-8.241c-.119-2.446-.174-4.88-.085-7.328a41.891,41.891,0,0,1,.485-5.658,8.736,8.736,0,0,1,2.047-4.864.415.415,0,0,0,.05-.56c2.606-3.541,5.593-6.8,8.007-10.48a24.143,24.143,0,0,0,2.891-6.355c.685-2.278,1.136-4.62,1.669-6.937C423.118,1185.788,422.247,1185.546,422.117,1186.118Z" transform="translate(-406.892 -1185.791)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_305" data-name="Group 305" transform="translate(27.185 44.062)">
        <path id="Path_368" data-name="Path 368" d="M382.592,1303.926c-.489-4.389-2.508-8.319-4.6-12.135-2.072-3.787-4.26-7.624-4.895-11.959-.084-.576-.955-.329-.87.239,1.268,8.651,8.489,15.121,9.464,23.855C381.753,1304.5,382.657,1304.5,382.592,1303.926Z" transform="translate(-372.219 -1279.504)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_306" data-name="Group 306" transform="translate(21.102 32.291)">
        <path id="Path_369" data-name="Path 369" d="M380.594,1253.813a26.244,26.244,0,0,0-10.242,7.627,15.766,15.766,0,0,0-2.768,6.1,65.42,65.42,0,0,0-1.052-6.778c-1.017-5.235-1.8-10.493-2.293-15.806q-.294-3.163-.438-6.332c-.027-.581-.93-.581-.9,0a160.891,160.891,0,0,0,1.856,17.508c.616,3.925,1.656,7.8,1.957,11.77a43.112,43.112,0,0,0-1.51-6.324,37.259,37.259,0,0,0-3.854-8.441.444.444,0,0,0-.117-.34c-.112-.125-.226-.246-.34-.368q-.611-.922-1.286-1.8a.382.382,0,0,0-.642.042,14.821,14.821,0,0,0-2.224-1.411c-1.856-.96-4.165-1.582-5.055-3.686-.227-.534-1-.073-.78.457.883,2.088,3.042,2.867,4.925,3.782a14.925,14.925,0,0,1,4.4,3.23,36.35,36.35,0,0,1,4.147,8.913,39.057,39.057,0,0,1,1.908,11.746.438.438,0,0,0,.005.063.425.425,0,0,0,.418.508.466.466,0,0,0,.887-.163c.329-4.27.817-8.75,3.551-12.234a25.722,25.722,0,0,1,9.9-7.287C381.583,1254.36,381.124,1253.583,380.594,1253.813Z" transform="translate(-350.866 -1238.192)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_307" data-name="Group 307" transform="translate(7.197 15.318)">
        <path id="Path_370" data-name="Path 370" d="M318.682,1180.89a7.832,7.832,0,0,0-2.655-.682.453.453,0,0,0-.484.418c-.061,1.261-.071,2.532-.232,3.79a4.183,4.183,0,0,1-.314,1.279c-.011.021-.061.083-.083.114-.006-.005-.013-.005-.021-.013a1,1,0,0,1-.223-.189c-1.186-1.25-1.354-3.113-2.443-4.426a5.651,5.651,0,0,0-2.308-1.582c-.916-.332-1.881-.229-2.665-.887-.287-.241-.656.036-.706.34-.282,1.7-.9,3.618-.376,5.333.466,1.523,1.8,2.343,3.13,3.03a13.409,13.409,0,0,1,1.872,1.115c.322.239.889.677.73,1.136-.167.482-.937.13-1.317-.013a20.259,20.259,0,0,1-2.252-1.079c-1.219-.633-3.119-1.572-4.4-.628a1.445,1.445,0,0,0-.279,1.753,5.5,5.5,0,0,0,2.053,2.2c1.8,1.219,4.046.752,5.988,1.5.447.171,1.775.861,1.518,1.526-.091.231-.5.361-.7.423a3.441,3.441,0,0,1-1.637.111c-1.99-.436-3.452-2.137-5.486-2.48a3.767,3.767,0,0,0-2.777.633,1.61,1.61,0,0,0-.338,1.946,4.661,4.661,0,0,0,1.789,1.909,5.671,5.671,0,0,0,2.726.949c1.96.145,3.964-.669,5.758.457a1.171,1.171,0,0,1,.591.669c.047.233-.218.441-.375.563a4.5,4.5,0,0,1-2.754.714,10.031,10.031,0,0,1-3.012-.937,6.083,6.083,0,0,0-3.144-.448,1.917,1.917,0,0,0-1.765,1.416,2.82,2.82,0,0,0,.816,2.127,6.539,6.539,0,0,0,2.4,1.749,6.233,6.233,0,0,0,3.206.249c1.078-.13,2.131-.4,3.213-.488a2.474,2.474,0,0,0,.78-.156,5.418,5.418,0,0,1,.886-.327c.262-.049.223.27.226.48,0,.5-.612.77-.982.983-.35.2-.665.464-1.029.638a8.644,8.644,0,0,1-1.311.457,6.271,6.271,0,0,0-2.5,1.157,2.478,2.478,0,0,0-.636,2.459c.494,1.8,2.822,1.925,4.3,1.484a4.1,4.1,0,0,0,2.215-1.66c.7-.991,1.076-2.127,2.114-2.781a1.268,1.268,0,0,1,.057,1.349,7.13,7.13,0,0,1-1.485,1.858c-1.055,1.084-2.725,2.4-2.782,4.047a.46.46,0,0,0,.3.462,3.925,3.925,0,0,0,2.682.073,3.559,3.559,0,0,0,1.662-1.587c.7-1.168,1.083-2.646,2.009-3.673a2.869,2.869,0,0,1-.4,1.1,3.273,3.273,0,0,0-.191,2.254c.262,1.175,1.172,2.145,2.418,1.561a4.338,4.338,0,0,0,1.6-1.593,5.383,5.383,0,0,0,.815-2.329,7.746,7.746,0,0,0-.313-3.056c2,1.525,5.259,2.568,6.557-.192,1.109-2.358-2.432-2.08-4.174-2.7.279-.013.562,0,.83,0,.768.026,1.533.091,2.3.046,1.435-.08,2.759-.771,2.891-2.347.113-1.362-1.407-1.245-2.311-1.108-.735.114-2.04.477-2.813.052,1.669-.581,3.531-1.225,4.464-2.792a3.544,3.544,0,0,0,.49-2.682c-.242-.667-1.041-.566-1.562-.379-1.419.514-2.454,1.735-3.831,2.34a6.2,6.2,0,0,1-1.025.343,1.97,1.97,0,0,1-.4.057.678.678,0,0,1-.153-.023.242.242,0,0,1,.008-.08,2.273,2.273,0,0,1,.266-.555,2.751,2.751,0,0,1,.934-.939c.75-.457,1.585-.734,2.306-1.245a4.221,4.221,0,0,0,1.557-2.363,4.458,4.458,0,0,0,.092-2.651,1.743,1.743,0,0,0-1.944-1.118c-2.434.192-2.628,2.892-3.638,4.558a5.594,5.594,0,0,1-1.03,1.175c-.154.143-.6.578-.842.521-.144-.033-.062-.76-.045-.9a3.451,3.451,0,0,1,.641-1.645c.894-1.287,2.289-2.218,2.73-3.787.288-1.032.2-2.506-.843-3.1-1.109-.63-2.457.327-3.027,1.271-.716,1.183-.577,2.833-1.674,3.762a2.331,2.331,0,0,1-.271-1.92,20.976,20.976,0,0,1,1.049-3.051,7.382,7.382,0,0,0,.645-3.025A2.8,2.8,0,0,0,318.682,1180.89Z" transform="translate(-302.066 -1178.619)" fill="#c3dffa" fill-rule="evenodd"/>
      </g>
      <g id="Group_308" data-name="Group 308" transform="translate(21.965 69.84)">
        <path id="Path_371" data-name="Path 371" d="M379.739,1395.156H360.27L353.9,1369.98H385.67Z" transform="translate(-353.897 -1369.98)" fill="#c3dffa" fill-rule="evenodd"/>
      </g>
      <g id="Group_309" data-name="Group 309" transform="translate(19.842 66.567)">
        <rect id="Rectangle_117" data-name="Rectangle 117" width="35.578" height="3.274" fill="#8bbae9"/>
      </g>
      <g id="Group_310" data-name="Group 310" transform="translate(21.965 69.84)">
        <path id="Path_372" data-name="Path 372" d="M369.783,1369.98H353.9l6.373,25.176h9.514Z" transform="translate(-353.897 -1369.98)" fill="#a4d0fc" fill-rule="evenodd"/>
      </g>
      <g id="Group_311" data-name="Group 311" transform="translate(19.842 66.567)">
        <rect id="Rectangle_118" data-name="Rectangle 118" width="18.009" height="3.274" fill="#61a0e0"/>
      </g>
      <g id="Group_312" data-name="Group 312" transform="translate(21.965 91.996)">
        <path id="Path_373" data-name="Path 373" d="M379.739,1451.139H360.27l-6.373-3.4H385.67Z" transform="translate(-353.897 -1447.74)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_313" data-name="Group 313" transform="translate(21.965 91.996)">
        <path id="Path_374" data-name="Path 374" d="M369.783,1447.74H353.9l6.373,3.4h9.514Z" transform="translate(-353.897 -1447.74)" fill="#61a0e0" fill-rule="evenodd"/>
      </g>
    </g>
    <g id="Group_337" data-name="Group 337" transform="translate(767.247 426.018)">
      <g id="Group_315" data-name="Group 315" transform="translate(24.979 32.525)">
        <path id="Path_375" data-name="Path 375" d="M2088.279,1217.947l-2.479,2.744.467-2.544,2.063-1.436Z" transform="translate(-2085.8 -1216.71)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_316" data-name="Group 316" transform="translate(25.455 51.923)">
        <path id="Path_376" data-name="Path 376" d="M2089.955,1286.027l-2.485,2.744.47-2.544,2.066-1.436Z" transform="translate(-2087.47 -1284.79)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_317" data-name="Group 317" transform="translate(27.46 39.941)">
        <path id="Path_377" data-name="Path 377" d="M2117,1242.875a13.6,13.6,0,0,1-13.6,13.6h-8.9l22.5-13.736Z" transform="translate(-2094.506 -1242.738)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_318" data-name="Group 318" transform="translate(27.459 39.942)">
        <path id="Path_378" data-name="Path 378" d="M2108.1,1242.74h8.9l-22.5,13.736v-.137A13.6,13.6,0,0,1,2108.1,1242.74Z" transform="translate(-2094.504 -1242.74)" fill="#c3dffa" fill-rule="evenodd"/>
      </g>
      <g id="Group_319" data-name="Group 319" transform="translate(27.458 21.455)">
        <path id="Path_379" data-name="Path 379" d="M2114.665,1177.978a12.185,12.185,0,0,1-12.187,12.185H2094.5l20.164-12.309v.124Z" transform="translate(-2094.501 -1177.854)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_320" data-name="Group 320" transform="translate(27.457 21.453)">
        <path id="Path_380" data-name="Path 380" d="M2106.683,1177.85h7.977l-20.164,12.309v-.125A12.19,12.19,0,0,1,2106.683,1177.85Z" transform="translate(-2094.496 -1177.85)" fill="#c3dffa" fill-rule="evenodd"/>
      </g>
      <g id="Group_321" data-name="Group 321" transform="translate(23.919 18.906)">
        <rect id="Rectangle_119" data-name="Rectangle 119" width="1.183" height="48.743" fill="#c3dffa"/>
      </g>
      <g id="Group_322" data-name="Group 322" transform="translate(25.102 18.906)">
        <rect id="Rectangle_120" data-name="Rectangle 120" width="1.183" height="48.743" fill="#8bbae9"/>
      </g>
      <g id="Group_323" data-name="Group 323" transform="translate(2.336 66.864)">
        <path id="Path_381" data-name="Path 381" d="M2029.1,1369.264h-15.759l-7.006-32.034H2029.1Z" transform="translate(-2006.33 -1337.23)" fill="#a4d0fc" fill-rule="evenodd"/>
      </g>
      <g id="Group_324" data-name="Group 324" transform="translate(25.102 66.864)">
        <path id="Path_382" data-name="Path 382" d="M2086.23,1369.264h15.762L2109,1337.23H2086.23Z" transform="translate(-2086.23 -1337.23)" fill="#c3dffa" fill-rule="evenodd"/>
      </g>
      <g id="Group_325" data-name="Group 325" transform="translate(0.001 62.676)">
        <path id="Path_383" data-name="Path 383" d="M1999.857,1322.53h23.381v4.189h-23.381a1.8,1.8,0,0,1-1.722-1.868v-.457A1.8,1.8,0,0,1,1999.857,1322.53Z" transform="translate(-1998.135 -1322.53)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_326" data-name="Group 326" transform="translate(25.102 62.676)">
        <path id="Path_384" data-name="Path 384" d="M2109.615,1322.53h-23.383v4.189h23.383a1.8,1.8,0,0,0,1.72-1.868v-.457A1.8,1.8,0,0,0,2109.615,1322.53Z" transform="translate(-2086.232 -1322.53)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_327" data-name="Group 327" transform="translate(9.34 98.897)">
        <path id="Path_385" data-name="Path 385" d="M2046.672,1452.509h-13.258a2.68,2.68,0,0,1-2.5-2.828v-.026h15.761Z" transform="translate(-2030.911 -1449.655)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_328" data-name="Group 328" transform="translate(25.101 98.897)">
        <path id="Path_386" data-name="Path 386" d="M2086.23,1452.509h13.258a2.68,2.68,0,0,0,2.5-2.828v-.026H2086.23Z" transform="translate(-2086.23 -1449.655)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_329" data-name="Group 329" transform="translate(22.446 32.525)">
        <path id="Path_387" data-name="Path 387" d="M2076.961,1217.947l2.482,2.744-.467-2.544-2.066-1.436Z" transform="translate(-2076.91 -1216.71)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_330" data-name="Group 330" transform="translate(21.97 51.923)">
        <path id="Path_388" data-name="Path 388" d="M2075.291,1286.027l2.479,2.744-.467-2.544-2.063-1.436Z" transform="translate(-2075.24 -1284.79)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_331" data-name="Group 331" transform="translate(0 39.941)">
        <path id="Path_389" data-name="Path 389" d="M1998.13,1242.875a13.6,13.6,0,0,0,13.6,13.6h8.9l-22.5-13.736Z" transform="translate(-1998.13 -1242.738)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_332" data-name="Group 332" transform="translate(0.001 39.942)">
        <path id="Path_390" data-name="Path 390" d="M2007.033,1242.74h-8.9l22.5,13.736v-.137A13.6,13.6,0,0,0,2007.033,1242.74Z" transform="translate(-1998.133 -1242.74)" fill="#c3dffa" fill-rule="evenodd"/>
      </g>
      <g id="Group_333" data-name="Group 333" transform="translate(2.336 21.455)">
        <path id="Path_391" data-name="Path 391" d="M2006.329,1177.978a12.184,12.184,0,0,0,12.187,12.185h7.974l-20.161-12.309v.124Z" transform="translate(-2006.329 -1177.854)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
      <g id="Group_334" data-name="Group 334" transform="translate(2.337 21.453)">
        <path id="Path_392" data-name="Path 392" d="M2014.307,1177.85h-7.974l20.161,12.309v-.125A12.188,12.188,0,0,0,2014.307,1177.85Z" transform="translate(-2006.333 -1177.85)" fill="#c3dffa" fill-rule="evenodd"/>
      </g>
      <g id="Group_335" data-name="Group 335" transform="translate(13.829)">
        <path id="Path_393" data-name="Path 393" d="M2049.157,1102.653a12.187,12.187,0,0,0,2.314,17.079l6.345,4.83-8.586-22.008Z" transform="translate(-2046.666 -1102.554)" fill="#c3dffa" fill-rule="evenodd"/>
      </g>
      <g id="Group_336" data-name="Group 336" transform="translate(16.392 0.001)">
        <path id="Path_394" data-name="Path 394" d="M2062.009,1107.391l-6.348-4.833,8.587,22.008.075-.094A12.192,12.192,0,0,0,2062.009,1107.391Z" transform="translate(-2055.661 -1102.558)" fill="#8bbae9" fill-rule="evenodd"/>
      </g>
    </g>
    <g id="Group_338" data-name="Group 338" transform="translate(675.027 266.699)">
      <path id="Path_395" data-name="Path 395" d="M1688.367,560.823c-.866,11.848-2.612,13.483-13.907,14.395,11.295.913,13.041,2.547,13.907,14.4.861-11.848,2.61-13.483,13.9-14.4-11.295-.912-13.044-2.546-13.9-14.395Zm45.915,31.248c-.459,6.326-1.393,7.2-7.424,7.685,6.031.488,6.965,1.359,7.424,7.685.462-6.326,1.4-7.2,7.427-7.685-6.031-.488-6.965-1.359-7.427-7.685Zm-20.11-48.685c-.467,6.406-1.411,7.291-7.518,7.784,6.107.493,7.051,1.379,7.518,7.785.467-6.406,1.414-7.292,7.523-7.785C1715.588,550.678,1714.64,549.793,1714.172,543.387Z" transform="translate(-1674.46 -543.387)" fill="#fff" fill-rule="evenodd"/>
    </g>
    <g id="Group_347" data-name="Group 347" transform="translate(708.688 249.818)">
      <g id="Group_339" data-name="Group 339" transform="translate(0 4.923)">
        <path id="Path_396" data-name="Path 396" d="M1798.277,507.186l45.34-5.717a6.506,6.506,0,0,1,7.245,5.624l7.857,62.319a6.5,6.5,0,0,1-5.624,7.247l-45.334,5.717a6.506,6.506,0,0,1-7.248-5.624l-7.86-62.319A6.505,6.505,0,0,1,1798.277,507.186Z" transform="translate(-1792.602 -501.418)" fill="#99c5f2" fill-rule="evenodd"/>
      </g>
      <g id="Group_340" data-name="Group 340" transform="translate(1.833 6.756)">
        <path id="Path_397" data-name="Path 397" d="M1803.1,513.6l45.337-5.717a4.669,4.669,0,0,1,5.2,4.034l7.86,62.32a4.669,4.669,0,0,1-4.036,5.2l-45.337,5.717a4.667,4.667,0,0,1-5.2-4.034L1799.07,518.8A4.667,4.667,0,0,1,1803.1,513.6Z" transform="translate(-1799.034 -507.85)" fill="#bbdbfa" fill-rule="evenodd"/>
      </g>
      <g id="Group_341" data-name="Group 341" transform="translate(2.746 6.93)">
        <path id="Path_398" data-name="Path 398" d="M1802.24,517.674l44.53-9.213,13.129,63.455-44.533,9.213Z" transform="translate(-1802.24 -508.461)" fill="#fff" fill-rule="evenodd"/>
      </g>
      <g id="Group_342" data-name="Group 342" transform="translate(5.854 11.443)">
        <rect id="Rectangle_121" data-name="Rectangle 121" width="40.787" height="5.262" transform="translate(41.009 5.153) rotate(168.311)" fill="#99c5f2"/>
      </g>
      <g id="Group_343" data-name="Group 343" transform="translate(9.063 27.758)">
        <path id="Path_399" data-name="Path 399" d="M1824.41,582.611l5.074-1.049,1.048,5.073-5.071,1.049-1.051-5.073Zm8.519,41.188,5.072-1.051,1.053,5.074-5.074,1.051-1.051-5.074Zm-1.7-8.238,5.074-1.049,1.048,5.073-5.074,1.049-1.048-5.073Zm-1.7-8.238,5.071-1.051,1.051,5.074-5.072,1.051-1.051-5.074Zm-1.7-8.237,5.074-1.049,1.048,5.073-5.074,1.049-1.048-5.073Zm-1.7-8.237,5.074-1.049,1.051,5.073-5.074,1.05Z" transform="translate(-1824.41 -581.562)" fill="#99c5f2" fill-rule="evenodd"/>
      </g>
      <g id="Group_344" data-name="Group 344" transform="translate(17.471 21.402)">
        <path id="Path_400" data-name="Path 400" d="M1854.607,564.97l27.542-5.7a.868.868,0,0,1,1.022.671h0a.868.868,0,0,1-.672,1.019l-27.541,5.7a.864.864,0,0,1-1.02-.671h0a.868.868,0,0,1,.669-1.021Zm8.522,41.189,27.541-5.7a.864.864,0,0,1,1.019.671h0a.867.867,0,0,1-.669,1.021l-27.541,5.7a.864.864,0,0,1-1.019-.671h0a.864.864,0,0,1,.669-1.019Zm.624,3.012a.866.866,0,0,0-.669,1.021h0a.866.866,0,0,0,1.019.671l16.543-3.422a.864.864,0,0,0,.669-1.021h0a.865.865,0,0,0-1.019-.671l-16.543,3.422Zm-2.328-11.251,27.541-5.7a.865.865,0,0,1,1.02.671h0a.866.866,0,0,1-.672,1.021l-27.539,5.7a.868.868,0,0,1-1.022-.669h0a.871.871,0,0,1,.672-1.022Zm.627,3.013a.869.869,0,0,0-.674,1.021h0a.87.87,0,0,0,1.022.671l16.54-3.422a.867.867,0,0,0,.672-1.021h0a.865.865,0,0,0-1.019-.671l-16.54,3.422Zm-2.331-11.249,27.539-5.7a.865.865,0,0,1,1.022.671h0a.867.867,0,0,1-.67,1.021l-27.541,5.7a.865.865,0,0,1-1.02-.672h0a.863.863,0,0,1,.67-1.02Zm.621,3.012a.865.865,0,0,0-.669,1.019h0a.866.866,0,0,0,1.019.671l16.543-3.422a.865.865,0,0,0,.669-1.021h0a.865.865,0,0,0-1.019-.671l-16.543,3.422Zm-2.325-11.249,27.542-5.7a.864.864,0,0,1,1.019.671h0a.867.867,0,0,1-.669,1.021l-27.542,5.7a.868.868,0,0,1-1.022-.671h0a.867.867,0,0,1,.672-1.02Zm.624,3.012a.865.865,0,0,0-.672,1.021h0a.865.865,0,0,0,1.02.671l16.542-3.423a.865.865,0,0,0,.669-1.019h0a.863.863,0,0,0-1.019-.671l-16.54,3.421Zm-2.328-11.249,27.541-5.7a.865.865,0,0,1,1.019.671h0a.863.863,0,0,1-.669,1.019l-27.542,5.7a.865.865,0,0,1-1.019-.671h0a.862.862,0,0,1,.669-1.019Zm.621,3.012a.866.866,0,0,0-.67,1.021h0a.868.868,0,0,0,1.022.671l16.54-3.422a.866.866,0,0,0,.669-1.021h0a.865.865,0,0,0-1.02-.669l-16.542,3.421Zm-1.7-8.238a.866.866,0,0,0-.672,1.021h0a.866.866,0,0,0,1.019.671l16.543-3.423a.868.868,0,0,0,.672-1.019h0a.868.868,0,0,0-1.022-.671Z" transform="translate(-1853.921 -559.255)" fill="#99c5f2" fill-rule="evenodd"/>
      </g>
      <g id="Group_345" data-name="Group 345" transform="translate(14.971)">
        <path id="Path_401" data-name="Path 401" d="M1846.9,490.552l7.972-1.005-.21-1.68a3.323,3.323,0,0,1,2.872-3.7h0a3.322,3.322,0,0,1,3.7,2.873l.212,1.68,7.974-1.005a2.006,2.006,0,0,1,2.236,1.737l.477,3.786a2.007,2.007,0,0,1-1.735,2.237l-22.524,2.841a2.01,2.01,0,0,1-2.236-1.737l-.477-3.785a2.01,2.01,0,0,1,1.738-2.239Zm10.878-4.426a1.467,1.467,0,1,0,1.639,1.272A1.471,1.471,0,0,0,1857.777,486.127Z" transform="translate(-1845.146 -484.138)" fill="#99c5f2" fill-rule="evenodd"/>
      </g>
      <g id="Group_346" data-name="Group 346" transform="translate(9.748 26.317)">
        <path id="Path_402" data-name="Path 402" d="M1826.948,580.061a.493.493,0,0,1,.721-.673l1.52,1.619,1.891-4.21a.493.493,0,0,1,.9.4l-2.184,4.865a.5.5,0,0,1-.122.178.494.494,0,0,1-.7-.022l-2.029-2.159Zm1.707,8.237a.493.493,0,1,1,.718-.674l1.523,1.62,1.888-4.211a.494.494,0,0,1,.651-.246.489.489,0,0,1,.246.651L1831.5,590.3a.511.511,0,0,1-.119.179.494.494,0,0,1-.7-.023l-2.026-2.159Zm6.815,32.95a.493.493,0,0,1,.721-.673l1.52,1.62,1.891-4.211a.492.492,0,1,1,.9.4l-2.184,4.864a.491.491,0,0,1-.82.156l-2.026-2.158Zm-1.7-8.236a.494.494,0,0,1,.721-.674l1.52,1.619,1.891-4.21a.492.492,0,1,1,.9.4l-2.182,4.862a.475.475,0,0,1-.125.179.49.49,0,0,1-.695-.023l-2.029-2.157Zm-1.7-8.239a.493.493,0,1,1,.721-.673l1.52,1.619,1.891-4.21a.492.492,0,1,1,.9.4l-2.184,4.864a.489.489,0,0,1-.817.156l-2.028-2.158Zm-1.7-8.238a.493.493,0,0,1,.721-.673l1.52,1.619,1.891-4.21a.493.493,0,0,1,.9.4l-2.184,4.864a.494.494,0,0,1-.82.156Z" transform="translate(-1826.815 -576.505)" fill="#61a0e0" fill-rule="evenodd"/>
      </g>
    </g>
    <g id="Group_355" data-name="Group 355" transform="translate(279.788 328.949)">
      <g id="Group_348" data-name="Group 348" transform="translate(0 3.895)">
        <path id="Path_403" data-name="Path 403" d="M290.418,775.542l59.821,2.415a2.222,2.222,0,0,1,2.165,2.163l.934,39.939c.029,1.188-1.908,2.713-3.1,2.75l-60.8,1.922a2.147,2.147,0,0,1-2.165-2.166V778.78C287.275,777.589,289.228,775.494,290.418,775.542Z" transform="translate(-287.275 -775.541)" fill="#61a0e0" fill-rule="evenodd"/>
      </g>
      <g id="Group_349" data-name="Group 349" transform="translate(1.232 3.887)">
        <path id="Path_404" data-name="Path 404" d="M293.764,775.512l60.8,1.342a2.189,2.189,0,0,1,2.165,2.165v40.523a2.2,2.2,0,0,1-2.165,2.166l-60.8,1.92a2.149,2.149,0,0,1-2.165-2.163V777.676A2.154,2.154,0,0,1,293.764,775.512Z" transform="translate(-291.599 -775.511)" fill="#d2e5f7" fill-rule="evenodd"/>
      </g>
      <g id="Group_350" data-name="Group 350" transform="translate(1.143 3.798)">
        <path id="Path_405" data-name="Path 405" d="M293.543,775.2l60.8,1.344a2.322,2.322,0,0,1,1.6.677h0a2.221,2.221,0,0,1,.656,1.576v40.523a2.211,2.211,0,0,1-.654,1.57h0a2.374,2.374,0,0,1-1.6.685l-60.8,1.919c-.027,0-.049,0-.068,0a2.151,2.151,0,0,1-1.526-.648,2.281,2.281,0,0,1-.662-1.536h0c0-.029,0-.054,0-.07V777.452h0V777.4h0a2.281,2.281,0,0,1,.662-1.554,2.176,2.176,0,0,1,1.546-.651h.048Zm60.794,1.52-60.8-1.343h-.043a2,2,0,0,0-1.42.6,2.1,2.1,0,0,0-.611,1.431v.047h0v43.788c0,.026,0,.049,0,.065a2.106,2.106,0,0,0,.611,1.416,1.966,1.966,0,0,0,1.4.594h.062l60.8-1.92a2.193,2.193,0,0,0,1.478-.635h0a2.028,2.028,0,0,0,.6-1.443V778.795a2.041,2.041,0,0,0-.6-1.449h0A2.149,2.149,0,0,0,354.337,776.718Z" transform="translate(-291.286 -775.198)" fill="#1e77d1" fill-rule="evenodd"/>
      </g>
      <g id="Group_351" data-name="Group 351" transform="translate(6.353 17.811)">
        <path id="Path_406" data-name="Path 406" d="M310,825.075c2.026-.026,4.065-.05,6.1-.078a.423.423,0,0,1,.435.4q.008,3.093.017,6.185a.437.437,0,0,1-.44.415c-2.039.042-4.082.078-6.114.119a.413.413,0,0,1-.429-.4v-6.226a.42.42,0,0,1,.429-.415Zm0,11.134q3.057-.078,6.125-.145a.415.415,0,0,1,.441.394q.008,3.093.01,6.185a.442.442,0,0,1-.434.42q-3.072.094-6.141.184a.4.4,0,0,1-.429-.4v-6.223a.425.425,0,0,1,.429-.415Zm49.142-1.162c2.038-.047,4.083-.093,6.126-.145a.415.415,0,0,1,.446.379c.035,1.961.078,3.917.113,5.873a.43.43,0,0,1-.436.4q-3.072.1-6.141.187a.413.413,0,0,1-.441-.379c-.03-1.972-.07-3.943-.1-5.917a.425.425,0,0,1,.435-.4Zm-9.828.233q3.057-.078,6.125-.143a.405.405,0,0,1,.44.379q.047,2.973.1,5.938a.43.43,0,0,1-.436.4c-2.048.06-4.1.125-6.141.184a.41.41,0,0,1-.441-.381c-.025-1.987-.053-3.985-.079-5.977a.427.427,0,0,1,.436-.4Zm-9.829.233q3.057-.074,6.126-.145a.413.413,0,0,1,.44.384c.019,2,.048,4,.074,6a.434.434,0,0,1-.436.41q-3.074.093-6.141.187a.41.41,0,0,1-.435-.384q-.031-3.019-.057-6.044a.425.425,0,0,1,.429-.4Zm-9.829.228,6.126-.143a.409.409,0,0,1,.435.389q.031,3.031.056,6.057a.434.434,0,0,1-.428.413q-3.076.1-6.143.189a.408.408,0,0,1-.44-.389q-.015-3.047-.04-6.1a.435.435,0,0,1,.435-.415Zm-9.828.233c2.036-.047,4.082-.1,6.125-.145a.412.412,0,0,1,.44.394q.015,3.063.034,6.117a.437.437,0,0,1-.435.42q-3.074.094-6.141.187a.415.415,0,0,1-.441-.394c-.005-2.049-.012-4.109-.017-6.164a.436.436,0,0,1,.436-.415ZM310,847.332q3.074-.1,6.147-.208a.406.406,0,0,1,.441.389q.008,3.093.012,6.184a.437.437,0,0,1-.435.42l-6.16.257a.4.4,0,0,1-.434-.392v-6.226a.434.434,0,0,1,.429-.425Zm49.312-1.7q3.064-.109,6.148-.213a.412.412,0,0,1,.446.374q.053,2.934.107,5.876a.421.421,0,0,1-.429.4q-3.082.129-6.158.254a.407.407,0,0,1-.451-.368q-.053-2.961-.1-5.922a.428.428,0,0,1,.434-.4Zm-9.863.34,6.148-.21a.407.407,0,0,1,.44.379c.031,1.977.061,3.959.091,5.935a.423.423,0,0,1-.429.407q-3.082.129-6.158.254a.409.409,0,0,1-.451-.371q-.039-3-.074-5.982a.432.432,0,0,1,.434-.413Zm-9.863.34q3.064-.109,6.143-.21a.412.412,0,0,1,.446.381c.025,2,.043,4,.067,6a.425.425,0,0,1-.429.41l-6.158.259a.413.413,0,0,1-.447-.374q-.029-3.031-.056-6.047a.439.439,0,0,1,.435-.417Zm-9.856.343,6.141-.21a.409.409,0,0,1,.441.381q.023,3.031.05,6.062a.432.432,0,0,1-.429.413l-6.16.257a.406.406,0,0,1-.445-.381c-.01-2.034-.03-4.067-.04-6.1a.441.441,0,0,1,.441-.417Zm-9.868.34q3.072-.1,6.148-.21a.407.407,0,0,1,.44.386q.016,3.063.034,6.125a.437.437,0,0,1-.435.418q-3.082.129-6.16.255a.408.408,0,0,1-.445-.384q-.008-3.086-.017-6.166a.443.443,0,0,1,.435-.423Zm39.1-22.534,6.1-.075a.426.426,0,0,1,.453.384q.053,2.938.106,5.876a.422.422,0,0,1-.435.4c-2.038.039-4.082.078-6.114.119a.418.418,0,0,1-.446-.384q-.045-2.958-.089-5.92a.417.417,0,0,1,.423-.4Zm-9.794.124q3.047-.039,6.1-.075a.421.421,0,0,1,.445.386q.047,2.969.091,5.938a.421.421,0,0,1-.435.4c-2.038.042-4.082.077-6.114.119a.415.415,0,0,1-.445-.384q-.039-3-.074-5.985a.419.419,0,0,1,.429-.4Zm-9.8.122,6.1-.075a.419.419,0,0,1,.446.392c.026,2,.043,4,.067,6a.424.424,0,0,1-.435.4c-2.038.039-4.082.078-6.114.119a.411.411,0,0,1-.435-.389q-.021-3.027-.056-6.047a.417.417,0,0,1,.423-.4Zm-9.789.125,6.1-.075a.415.415,0,0,1,.435.394q.023,3.031.051,6.06a.425.425,0,0,1-.429.41l-6.113.119a.416.416,0,0,1-.441-.394c-.009-2.034-.029-4.067-.04-6.1a.428.428,0,0,1,.436-.412Zm-9.794.122,6.1-.078a.421.421,0,0,1,.441.4q.016,3.066.034,6.122a.436.436,0,0,1-.44.415l-6.114.117a.415.415,0,0,1-.441-.394q-.008-3.086-.017-6.169A.431.431,0,0,1,319.8,824.95Z" transform="translate(-309.572 -824.382)" fill="#fff" fill-rule="evenodd"/>
      </g>
      <g id="Group_352" data-name="Group 352" transform="translate(1.232 3.887)">
        <path id="Path_407" data-name="Path 407" d="M293.764,775.512l60.8,1.342a2.189,2.189,0,0,1,2.165,2.165V787.1h-.011l-64.01.75a2.12,2.12,0,0,1-1.108-.3v-9.881A2.154,2.154,0,0,1,293.764,775.512Z" transform="translate(-291.599 -775.511)" fill="#bbdbfa" fill-rule="evenodd"/>
      </g>
      <g id="Group_353" data-name="Group 353" transform="translate(1.143 3.797)">
        <path id="Path_408" data-name="Path 408" d="M293.542,775.2l60.8,1.344a2.328,2.328,0,0,1,1.6.677,2.221,2.221,0,0,1,.656,1.576v8.173h-.1l-64.012.752a2.252,2.252,0,0,1-.6-.078,2.113,2.113,0,0,1-.558-.233l-.044-.026v-9.93h0V777.4h0a2.281,2.281,0,0,1,.662-1.554,2.176,2.176,0,0,1,1.546-.651h.048Zm60.794,1.52-60.8-1.343h-.044a2,2,0,0,0-1.419.6,2.1,2.1,0,0,0-.611,1.431v.047h0v9.829a2.07,2.07,0,0,0,1.018.26l63.933-.75v-8a2.041,2.041,0,0,0-.6-1.449A2.145,2.145,0,0,0,354.336,776.718Z" transform="translate(-291.285 -775.197)" fill="#1e77d1" fill-rule="evenodd"/>
      </g>
      <g id="Group_354" data-name="Group 354" transform="translate(13.601 0)">
        <path id="Path_409" data-name="Path 409" d="M373.1,766.831a9.151,9.151,0,0,1,.7-2.828,2.3,2.3,0,0,1,2.027-1.572,2.472,2.472,0,0,1,2.079,1.688,9.051,9.051,0,0,1,0,6.9,2.47,2.47,0,0,1-2.079,1.686.645.645,0,1,1,0-1.291c.313,0,.639-.362.914-.945a7.824,7.824,0,0,0,0-5.8c-.275-.582-.6-.944-.914-.944-.35,0-.637.353-.862.829a7.527,7.527,0,0,0-.568,2.307l-1.293-.028Zm-38.093-.812a8.828,8.828,0,0,1,.669-2.579,2.3,2.3,0,0,1,2.027-1.572,2.474,2.474,0,0,1,2.081,1.686,9.056,9.056,0,0,1,0,6.9,2.474,2.474,0,0,1-2.081,1.686.646.646,0,0,1,0-1.292c.314,0,.641-.361.914-.944a7.825,7.825,0,0,0,0-5.8c-.274-.583-.6-.944-.914-.944-.35,0-.637.351-.862.829a7.222,7.222,0,0,0-.541,2.057l-1.293-.027Zm12.68.269a9.082,9.082,0,0,1,.7-2.834,2.3,2.3,0,0,1,2.027-1.571,2.472,2.472,0,0,1,2.079,1.687,9.053,9.053,0,0,1,0,6.9,2.472,2.472,0,0,1-2.079,1.686.645.645,0,1,1,0-1.291c.313,0,.639-.362.914-.946a7.828,7.828,0,0,0,0-5.8c-.275-.584-.6-.944-.914-.944-.35,0-.637.353-.862.829a7.514,7.514,0,0,0-.57,2.313l-1.292-.028Zm12.707.272a9.079,9.079,0,0,1,.7-2.832,2.3,2.3,0,0,1,2.027-1.571,2.472,2.472,0,0,1,2.081,1.686,9.052,9.052,0,0,1,0,6.9,2.475,2.475,0,0,1-2.081,1.687.646.646,0,0,1,0-1.292c.314,0,.641-.362.916-.945a7.844,7.844,0,0,0,0-5.8c-.275-.583-.6-.945-.916-.945-.35,0-.637.353-.862.83a7.56,7.56,0,0,0-.568,2.31Z" transform="translate(-335.01 -761.869)" fill="#61a0e0" fill-rule="evenodd"/>
      </g>
    </g>
    <g id="Group_356" data-name="Group 356" transform="translate(536.231 274.557)">
      <path id="Path_410" data-name="Path 410" d="M1201.7,573.663l-2.723,1.584a1.937,1.937,0,0,0-.7,2.64l.6,1.034c.708,1.219.4,1.771.179,1.968-.606.542-1.167,1.131-1.73,1.716-.166.174-.691.762-2.075-.043l-.951-.551a1.936,1.936,0,0,0-2.64.7l-1.582,2.724a1.937,1.937,0,0,0,.7,2.64l.94.546c1.3.755,1.264,1.141,1.1,1.692-.226.756-.468,1.512-.633,2.293-.148.7-.081,1.195-1.846,1.195h-1.083a1.934,1.934,0,0,0-1.93,1.931v3.149a1.933,1.933,0,0,0,1.93,1.93h1.083c1.765,0,1.7.5,1.846,1.194.165.782.407,1.538.633,2.294.165.55.2.937-1.1,1.691l-.94.546a1.936,1.936,0,0,0-.7,2.639l1.582,2.723a1.937,1.937,0,0,0,2.64.7l.951-.552c1.384-.8,1.909-.215,2.075-.043.563.586,1.125,1.175,1.73,1.717.22.2.529.748-.179,1.968l-.6,1.033a1.937,1.937,0,0,0,.7,2.639l2.723,1.584a1.936,1.936,0,0,0,2.64-.7l.6-1.031c.72-1.237,1.429-1.189,1.727-1.093.869.277,1.671.455,2.565.689.327.084.887.323.887,1.8v1.106a1.935,1.935,0,0,0,1.93,1.93h3.152a1.935,1.935,0,0,0,1.93-1.93V620.6c0-1.473.56-1.712.887-1.8.9-.233,1.7-.411,2.565-.689.3-.1,1.009-.144,1.728,1.093l.6,1.031a1.933,1.933,0,0,0,2.638.7l2.721-1.584a1.936,1.936,0,0,0,.7-2.639l-.6-1.033c-.708-1.219-.4-1.772-.182-1.968.607-.542,1.168-1.131,1.733-1.717.166-.172.69-.761,2.075.043l.949.552a1.939,1.939,0,0,0,2.641-.7l1.583-2.723a1.935,1.935,0,0,0-.7-2.639l-.942-.546c-1.3-.755-1.266-1.141-1.1-1.691.228-.756.469-1.512.633-2.294.148-.7.083-1.194,1.847-1.194h1.082a1.935,1.935,0,0,0,1.932-1.93v-3.149a1.936,1.936,0,0,0-1.932-1.931h-1.082c-1.764,0-1.7-.5-1.847-1.195-.164-.781-.405-1.537-.633-2.293-.166-.55-.2-.937,1.1-1.692l.942-.546a1.935,1.935,0,0,0,.7-2.64l-1.583-2.724a1.938,1.938,0,0,0-2.641-.7l-.949.551c-1.385.805-1.909.217-2.075.043-.565-.585-1.126-1.174-1.733-1.716-.218-.2-.527-.748.182-1.968l.6-1.034a1.936,1.936,0,0,0-.7-2.64l-2.721-1.584a1.935,1.935,0,0,0-2.638.7l-.6,1.031c-.718,1.236-1.427,1.188-1.728,1.093-.866-.278-1.668-.457-2.565-.69-.327-.084-.887-.322-.887-1.8v-1.1a1.935,1.935,0,0,0-1.93-1.93h-3.152a1.935,1.935,0,0,0-1.93,1.93V574c0,1.476-.56,1.714-.887,1.8-.895.233-1.7.413-2.565.69-.3.095-1.008.142-1.727-1.093l-.6-1.031a1.938,1.938,0,0,0-2.64-.7Zm11.924,19.479a4.159,4.159,0,1,1-4.161,4.16,4.16,4.16,0,0,1,4.161-4.16Zm-13.038,4.16a.89.89,0,1,1,1.779,0,11.257,11.257,0,0,0,11.258,11.257.89.89,0,0,1,0,1.78,13.037,13.037,0,0,1-13.038-13.037Zm25.9-2.1a.887.887,0,1,1-1.751.279,11.269,11.269,0,0,0-9.183-9.273.889.889,0,1,1,.3-1.752,13.048,13.048,0,0,1,10.634,10.746Zm-12.864-13.348a15.448,15.448,0,1,1-15.448,15.448A15.447,15.447,0,0,1,1213.62,581.854Z" transform="translate(-1187.323 -570.964)" fill="#fff" fill-rule="evenodd"/>
    </g>
  </g>
</svg>
