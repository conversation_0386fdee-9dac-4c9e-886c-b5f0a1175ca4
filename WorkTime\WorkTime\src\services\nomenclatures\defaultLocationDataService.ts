import { ICitiesType } from "../../models/DTOs/enums/ICitiesType";
import { IDistrictType } from "../../models/DTOs/enums/IDistrictType";
import { IEnumType } from "../../models/DTOs/enums/IEnumType";
import { IMunicipalityType } from "../../models/DTOs/enums/IMunicipalityType";
import { authenticatedGet } from "../connectionService";

export const initCountries = async (): Promise<IEnumType[]> => {
  try {
    const response = await authenticatedGet<IEnumType[]>(`countries`);
    return response;
  } catch (error) {
    console.error("Error fetching countries:", error);
    return [];
  }
};

export const initDistricts = async (): Promise<IDistrictType[]> => {
  try {
    const response = await authenticatedGet<IDistrictType[]>(
      `default-districts-data`
    );
    return response;
  } catch (error) {
    console.error("Error fetching districts:", error);
    return [];
  }
};

export const initMunicipalities = async (): Promise<IMunicipalityType[]> => {
  try {
    const response = await authenticatedGet<IMunicipalityType[]>(
      `default-municipalities-data?`
    );
    return response;
  } catch (error) {
    console.error("Error fetching municipalities:", error);
    return [];
  }
};

export const initCities = async (): Promise<ICitiesType[]> => {
  try {
    const response = await authenticatedGet<ICitiesType[]>(
      `default-cities-data`
    );
    return response;
  } catch (error) {
    console.error("Error fetching cities", error);
    return [];
  }
};
