import { useAppDispatch } from "../../app/hooks";
import { ChangeEvent, MouseEvent, useState } from "react";
import { OnboardingDocumentDTO } from "../../models/DTOs/OnboardingDocumentDTO";
import { onDocumentSaved } from "./onboardingDocumentsActions";
import Checkbox from "../../components/Inputs/Checkbox";
import Textbox from "../../components/Inputs/Textbox";
import Form from "../../components/Form/Form";
import Button from "../../components/Inputs/Button";
import MainWindowContainer from "../../components/MainWindowContainer";

const AddOnboardingDocument = () => {
  const [document, setDocument] = useState({} as OnboardingDocumentDTO);
  const dispatch = useAppDispatch();

  const handleDocumentChange = (e: ChangeEvent<HTMLInputElement>) => {
    setDocument({ ...document, [e.currentTarget.name]: e.currentTarget.value });
  };
  const handleCheckboxChange = (checked: boolean) => {
    setDocument({ ...document, isFileRequired: checked });
  };

  const onDocumentSaving = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    document &&
      dispatch(
        onDocumentSaved({
          ...document,
        })
      );
  };

  return (
    <MainWindowContainer>
      <Form>
        <Textbox
          name="name"
          handleChange={handleDocumentChange}
          label="Name"
          placeholder="Fill document name"
          value={document.name!}
          data-testid="textbox-name"
        />
        <Textbox
          name="description"
          handleChange={handleDocumentChange}
          label="Description"
          placeholder="Fill document description"
          value={document.description!}
          data-testid="textbox-description"
        />
        <Checkbox
          name="isFileRequired"
          label="Attach document"
          handleChange={handleCheckboxChange}
          data-testid="checkbox-isFileRequired"
        />
        <Button
          label="Save"
          onClick={onDocumentSaving}
          data-testid="button-save"
        />
      </Form>
    </MainWindowContainer>
  );
};

export default AddOnboardingDocument;
