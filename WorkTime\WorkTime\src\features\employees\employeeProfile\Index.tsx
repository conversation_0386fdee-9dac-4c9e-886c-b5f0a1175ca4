import { MouseEvent } from "react";
import { useNavigate } from "react-router";
import Button from "../../../components/Inputs/Button";
import Translator from "../../../services/language/Translator";

const Index = () => {
  const navigate = useNavigate();

  const handleProfile = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    navigate("/edit-profile");
  };

  return (
    <Button onClick={handleProfile} label={""} data-testid="profile-button">
      <Translator getString="strProfile" />
    </Button>
  );
};

export default Index;
