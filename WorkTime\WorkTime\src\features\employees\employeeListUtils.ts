import { BaseEmployeeView } from "./EmployeeViewInterfaces";

interface FilterOptions {
  personalData: string;
  position: string;
  departments: string[];
  categories: string[];
  typeOfAppointment: string[];
}

interface DepartmentOption {
  identifier: string;
  name: string;
  childStructureLevels: DepartmentOption[];
}

interface CategoryOption {
  identifier: string | number;
  name: string;
  description: string;
}

export const filterEmployees = <T extends BaseEmployeeView>(
  employees: T[],
  filters: FilterOptions,
    departmentsOptions: DepartmentOption[],
  categoriesOptions: CategoryOption[],
  typesOfAppointmentOptions: CategoryOption[]
): T[] => {
  const searchWords = filters.personalData
    .toLowerCase()
    .split(/\s+/)
    .filter((word) => word.length > 0);

  return employees.filter((employee) => {
    const personalDataMatch =
      !filters.personalData || matchesPersonalDataFilter(employee, searchWords);

    const positionMatch =
      !filters.position ||
      (employee.position &&
        employee.position
          ?.trim()
          .toLowerCase()
          .includes(filters.position.trim().toLowerCase()));

    const departmentMatch =
      filters.departments.length === 0 ||
      filters.departments.includes(
        findDepartmentById(
          departmentsOptions,
          employee.departmentId
        )?.identifier?.toString() || ""
      );

    const typeOfAppointmentMatch =
      filters.typeOfAppointment.length === 0 ||
      filters.typeOfAppointment.includes(
        typesOfAppointmentOptions
          .find(
            (option) =>
              option.identifier.toString() ===
              employee.typeOfAppointment?.identifier?.toString()
          )
          ?.identifier?.toString() || ""
      );

    const categoryMatch =
      filters.categories.length === 0 ||
      filters.categories.includes(
        categoriesOptions
          .find(
            (option) =>
              option.identifier.toString() ===
              employee.category?.identifier?.toString()
          )
          ?.identifier?.toString() || ""
      );

    return (
      personalDataMatch &&
      positionMatch &&
      departmentMatch &&
      typeOfAppointmentMatch &&
      categoryMatch
    );
  });
};

export const matchesPersonalDataFilter = <T extends BaseEmployeeView>(
  employee: T,
  searchWords: string[]
): boolean => {
  if (searchWords.length == 0) return false;

  if (searchWords[0].startsWith("#")) {
    const searchNumber = searchWords[0].replace("#", "");

    return employee.number.includes(searchNumber);
  }

  const employeeFields = [employee.fullName, employee.egn, employee.email].map(
    (field) => field?.toLowerCase() || ""
  );

  return searchWords.every((word) =>
    employeeFields.some((field) => field.includes(word))
  );
};

export const sortEmployees = <T extends BaseEmployeeView>(
  employees: T[],
  sort: { [key: string]: number }
): T[] => {
  let sortKey = Object.keys(sort)[0];
  const sortDirection = sort[sortKey];

  if (!sortKey) return employees;

  if (sortKey === "personalData") {
    sortKey = "fullName";
  }

  return [...employees].sort((a, b) => {
    if (sortKey === "egn") {
      let dateA = parseEgnToDate((a as any)[sortKey]);
      let dateB = parseEgnToDate((b as any)[sortKey]);

      if (!dateA) dateA = new Date(1900, 0, 1);
      if (!dateB) dateB = new Date(1900, 0, 1);

      return sortDirection * (dateA.getTime() - dateB.getTime());
    }

    const valA = ((a as any)[sortKey] ?? "").toString();
    const valB = ((b as any)[sortKey] ?? "").toString();

    return sortDirection * valA.localeCompare(valB);
  });
};

const parseEgnToDate = (egn: string): Date | null => {
  if (!egn || egn.length !== 10) return null;

  const yearPart = parseInt(egn.substring(0, 2), 10);
  const monthPart = parseInt(egn.substring(2, 4), 10);
  const dayPart = parseInt(egn.substring(4, 6), 10);

  let year: number;
  let month: number;

  if (monthPart >= 1 && monthPart <= 12) {
    year = 1900 + yearPart;
    month = monthPart;
  } else if (monthPart >= 21 && monthPart <= 32) {
    year = 1800 + yearPart;
    month = monthPart - 20;
  } else if (monthPart >= 41 && monthPart <= 52) {
    year = 2000 + yearPart;
    month = monthPart - 40;
  } else {
    return null;
  }

  return new Date(year, month - 1, dayPart);
};

export const findDepartmentById = (
  options: DepartmentOption[],
  id: string
): DepartmentOption | undefined => {
  for (const option of options) {
    if (option.identifier === id) return option;
    if (option.childStructureLevels && option.childStructureLevels.length > 0) {
      const found = findDepartmentById(option.childStructureLevels, id);
      if (found) return found;
    }
  }
  return undefined;
};
