import { StructureLevelDTO } from "../../models/DTOs/companyStructure/StructureLevelDTO";

export const mapDepartmentToNestedOption = (
  departments: StructureLevelDTO
): any => {

  return {
    id: departments.id,
    label: departments.name,
    value: departments.id,
    identifier: departments.id,
    name: departments.name,
    childStructureLevels: (departments.childStructureLevels || []).map(
      mapDepartmentToNestedOption
    ),
  };
};
