import { styled } from "styled-components";
import Label from "../../components/Inputs/Label";
import GridView, { GridViewDiv } from "../../components/GridView";
import Avatar from "../../components/Table/Avatar";
import { IEmployeesGridViewEntity } from "../../models/Interfaces/IEmployeesGridViewEntity";

const StyledAvatar = styled(Avatar)`
  margin-top: 2.5rem;
`;

const NameLabel = styled(Label)`
  font: Segoe UI;
  color: var(--first-label-color-gridView);
  opacity: 1;
  font-size: 1rem;
  text-align: center;
  position: absolute;
  width: 100%;
  bottom: 3.5rem;
  cursor: pointer;
  word-wrap: break-word;
  overflow-wrap: break-word;
`;

const EmailLabel = styled(Label)`
  font: Segoe UI;
  color: var(--second-label-color-gridView);
  opacity: 1;
  font-size: 0.8rem;
  text-align: center;
  cursor: pointer;
  word-wrap: break-word;
  overflow-wrap: break-word;
  position: absolute;
  bottom: 1rem;
  width: 100%;
`;

interface EmployeesGridElementProps {
  data: IEmployeesGridViewEntity[];
  imgSize: number;
  handleClick: (id: string) => void;
}

const EmployeesGridElements = ({
  data,
  handleClick,
  imgSize,
}: EmployeesGridElementProps) => {
  return (
    <GridView
      data-testid="employees-grid"
      data={data}
      renderElement={(employee) => {
        return (
          <GridViewDiv
            data-testid={`employee-grid-item-${employee.id}`}
            key={employee.id}
            onClick={() => {
              handleClick(employee.id);
            }}
          >
            <StyledAvatar
              data-testid={`employee-avatar-${employee.id}`}
              name={employee.name}
              photo=""
              size={imgSize}
            />
            <NameLabel data-testid={`employee-name-${employee.id}`}>
              {employee.name}
            </NameLabel>
            <EmailLabel data-testid={`employee-email-${employee.id}`}>
              {employee.email}
            </EmailLabel>
          </GridViewDiv>
        );
      }}
    />
  );
};

export default EmployeesGridElements;
