import { ChangeEvent, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import Container from "../../components/Container";
import Searchbar from "../../components/Inputs/Searchbar";
import MainWindowContainer from "../../components/MainWindowContainer";
import { useCompany } from "../companies/CompanyContext";
import {
  onEmployeePayrollsLoaded,
  selectEmployeePayrolls,
} from "../payroll/employeePayrollActions";
import EmployeesGridElements from "./EmployeesGridElements";

const MainContainer = styled(MainWindowContainer)`
  display: contents;
  margin: 1rem;
  height: 45rem;
  overflow-y: auto;
`;

const EmployeesContainer = styled(Container)`
  display: grid;
  align-items: center;
  width: clamp(65%, 90rem, 90%);
  max-height: 50vh;
  overflow-y: auto;
`;

const SearchbarContainer = styled(Container)`
  position: relative;
  box-sizing: border-box;
  justify-content: center;
  width: clamp(35%, 55rem, 70%);
  margin: 0 auto;
`;

const EmployeesGridView = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const employees = useAppSelector(selectEmployeePayrolls).employeePayrolls;
  const [filteredEmployees, setFilteredEmployees] = useState<
    { id: string; name: string; email: string }[]
  >([]);
  const [textInput, setTextInput] = useState("");
  const { company } = useCompany();
  useEffect(() => {
    dispatch(onEmployeePayrollsLoaded(company.id));
  }, [dispatch]);

  useEffect(() => {
    setFilteredEmployees(mappedEmployees);
  }, [employees]);

  const onRowClick = (id: string) => {
    navigate(`${id}`);
  };

  const employeesFilter = (searchText: string) => {
    setFilteredEmployees(
      mappedEmployees.filter((employee) =>
        employee?.name
          .toLocaleLowerCase()
          .includes(searchText?.toLocaleLowerCase())
      )
    );
  };

  const mappedEmployees = employees.map((employee) => ({
    id: employee.employeeGuid,
    name: [employee.firstName, employee.secondName, employee.lastName]
      .filter((name) => name != null)
      .join(" "),
    email: employee.email,
  }));

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const searchText = e.target.value;
    setTextInput(searchText);
    employeesFilter(searchText);
  };

  return (
    <MainContainer data-testid="employees-main-container">
      <SearchbarContainer data-testid="employees-searchbar-container">
        <Searchbar
          data-testid="employees-searchbar"
          handleChange={handleChange}
          value={textInput}
          type="text"
          label="strGetInput"
          placeholder={""}
        ></Searchbar>
      </SearchbarContainer>
      <EmployeesContainer data-testid="employees-grid-container">
        <EmployeesGridElements
          data-testid="employees-grid-elements"
          data={filteredEmployees.sort((a, b) => a.name.localeCompare(b.name))}
          handleClick={onRowClick}
          imgSize={3}
        />
      </EmployeesContainer>
    </MainContainer>
  );
};

export default EmployeesGridView;
