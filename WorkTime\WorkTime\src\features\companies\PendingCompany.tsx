import styled from "styled-components";
import { CompanyDTO } from "../../models/DTOs/companies/CompanyDTO";
import joinImage from "../../assets/images/menu/join.png";
import Button from "../../components/Inputs/Button";
import Container from "../../components/Container";

const Company = styled.label`
  color: var(--company-name-label-color);
  background-color: var(--pendingCompany-background-color);
  flex-grow: 1;
  float: left;
  border: none;
  font-size: 1rem;
`;

const CompanyWrapper = styled(Container)`
  background-color: var(--pendingCompany-background-color);
  display: flex;
  box-sizing: border-box;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  padding: 0.5em;
  border: 0.5px solid var(--pendingCompany-background-color);
  border-radius: 3em;
  margin-bottom: 0.5em;
  margin-right: 0.5em;
  margin-top: 0;
`;

const Image = styled.img`
  width: 1.5em;
  display: "flex";
  height: 1.5em;
  cursor: pointer;
  float: center;
  margin-right: 0.5em;
`;

const JoinButton = styled(Button)`
  cursor: pointer;
  padding: 0.5em;
  font: 0.8rem/1rem Segoe UI;
  width: 25%;
`;

interface Props {
  company: CompanyDTO;
  joinPendingCompany: (company: CompanyDTO) => void;
  label: string;
}
const PendingCompany = ({ company, joinPendingCompany, label }: Props) => {
  return (
    <CompanyWrapper
      key={company.id}
      data-testid={`company-wrapper-${company.id}`}
    >
      <Image src={joinImage} data-testid={`company-image-${company.id}`} />
      <Company data-testid={`company-name-${company.id}`}>
        {company.name}
      </Company>
      <JoinButton
        label={label}
        onClick={() => joinPendingCompany(company)}
        data-testid={`join-button-${company.id}`}
      />
    </CompanyWrapper>
  );
};

export default PendingCompany;
