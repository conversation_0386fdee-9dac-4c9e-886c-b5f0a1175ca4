import styled from "styled-components";
import { useLocation } from "react-router-dom";
import Menu from "./Menu";
import NavLinkButton from "../components/NavLinkButton";
import { useEffect, useState } from "react";
import { useAuth } from "./authentication/AuthContext";
import workers_active from "../assets/images/menu/menu_workers_active.svg";
import workers_inactive_hover from "../assets/images/menu/menu_workers_inactive_hover.svg";
import vacations_active from "../assets/images/menu/menu_vacations_active.svg";
import vacations_inactive_hover from "../assets/images/menu/menu_vacations_ inactive_hover.svg";
import import_trz_active from "../assets/images/menu/menu_import_trz_active.svg";
import import_trz_inactive_hover from "../assets/images/menu/menu_import_trz_inactive_hover.svg";

import { useAppDispatch, useAppSelector } from "../app/hooks";
import {
  onPendingEmployeesLoaded,
  selectEmployees,
} from "./employees/employeesActions";
import { useCompany } from "./companies/CompanyContext";
import { useUserEmployee } from "./UserEmployeeContext";
import { DefaultPermissions } from "../constants/permissions";
import {
  onEmployeePayrollsLoaded,
  selectEmployeePayrolls,
} from "./payroll/employeePayrollActions";

const NavContainer = styled.div`
  flex: 0 1 auto;
  width: 100%;
  box-sizing: border-box;
  display: inline-flex;
  padding: 0.7rem;
  background-color: var(--app-background-color);
`;

interface Props {
  isMenuOpen: boolean;
  menuRef: React.RefObject<HTMLDivElement>;
  onClick: () => void;
}

const MainWindow = ({ isMenuOpen, menuRef, onClick }: Props) => {
  const location = useLocation();
  const dispatch = useAppDispatch();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const { user } = useAuth();
  const { company } = useCompany();
  const pendingEmployees = useAppSelector(selectEmployees).pendingEmployees;
  const employeePayrolls = useAppSelector(
    selectEmployeePayrolls
  ).employeePayrolls;
  const { userEmployee } = useUserEmployee();

  useEffect(() => {
    setIsAuthenticated(!!user.email);
  }, [user]);

  useEffect(() => {
    if (
      !userEmployee.permissions.includes(DefaultPermissions.Employees.Write) &&
      employeePayrolls.length === 0
    )
      dispatch(onEmployeePayrollsLoaded(company.id));
  }, [dispatch, company.id]);

  useEffect(() => {
    if (company?.id) {
      dispatch(onPendingEmployeesLoaded(company.id));
    }
  }, [dispatch, company?.id]);

  useEffect(() => {}, [employeePayrolls]);

  const shouldShowMenu =
    isAuthenticated && location.pathname !== "/" && user.hasSignedIn;

  return (
    <>
      {shouldShowMenu && (
        <NavContainer data-testid="nav-container">
          {/* <NavLinkButton
            to="/company-structure"
            imageSrc={Structure}
            label="Structure"
            currentpath={location.pathname}
          /> */}
          {(userEmployee.permissions.includes(
            DefaultPermissions.Employees.Write
          ) ||
            employeePayrolls.length > 1) && (
            <NavLinkButton
              to={`/${company?.id}/employees`}
              imageSrc={workers_active}
              hoverImageSrc={workers_inactive_hover}
              label={
                userEmployee.permissions.includes(
                  DefaultPermissions.Employees.Write
                )
                  ? "Employees"
                  : "Contracts"
              }
              currentpath={location.pathname}
              data-testid="employees-nav-button"
            />
          )}
          {!userEmployee.permissions.includes(
            DefaultPermissions.Employees.Write
          ) &&
            employeePayrolls.length <= 1 && (
              <NavLinkButton
                to={`/${company?.id}/employees/${userEmployee.employeeId}/${
                  employeePayrolls[0]?.payrollId ?? ""
                }`}
                imageSrc={workers_active}
                hoverImageSrc={workers_inactive_hover}
                label="Person data"
                currentpath={location.pathname}
                data-testid="employees-nav-button"
              />
            )}
          {userEmployee.permissions.includes(
            DefaultPermissions.Attendances.Read
          ) && (
            <NavLinkButton
              to={`/${company?.id}/attendance`}
              imageSrc={vacations_active}
              hoverImageSrc={vacations_inactive_hover}
              label="Events"
              currentpath={location.pathname}
              data-testid="attendance-nav-button"
            />
          )}
          {userEmployee.permissions.includes(DefaultPermissions.Import.Read) &&
            pendingEmployees.length > 0 && (
              <NavLinkButton
                to={`/${company?.id}/pending-employees`}
                imageSrc={import_trz_active}
                hoverImageSrc={import_trz_inactive_hover}
                label="Import"
                currentpath={location.pathname}
                data-testid="pending-employees-nav-button"
              />
            )}
        </NavContainer>
      )}
      <Menu
        isOpen={isMenuOpen}
        ref={menuRef}
        onToggleMenu={onClick}
        data-testid="menu"
      />
    </>
  );
};

export default MainWindow;
