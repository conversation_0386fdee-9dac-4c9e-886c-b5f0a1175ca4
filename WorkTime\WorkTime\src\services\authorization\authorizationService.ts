import { LOCAL_STORAGE_WORKTIME_ROLE_NAME } from "../../constants/local-storage-constants";
import { PermissionsMap } from "../../features/authorization/PermissionsContext";
import { CompanyDTO } from "../../models/DTOs/companies/CompanyDTO";
import { RoleDTO } from "../../models/DTOs/RoleDTO";
import { get } from "../connectionService";
import { authenticatedGet, authenticatedPost } from "../connectionService";

const getUserPermissions = (
  permissions: PermissionsMap | undefined,
  company: CompanyDTO
): string[] => {
  return permissions
    ? permissions[company.userRegistrationCompanyId ?? -1] ?? []
    : [];
};

export const isUserPermitted = (
  requiredPermissions: string[],
  permissions: PermissionsMap | undefined,
  company: CompanyDTO
): boolean => {
  const companyPermissions = getUserPermissions(permissions, company);
  return requiredPermissions.some((permission) =>
    companyPermissions.includes(permission)
  );
};

export const getRoles = async (): Promise<RoleDTO[]> => {
  const response = await get<RoleDTO[]>("sso/roles/general-roles");
  return response;
};

export const getWorkTimeRole = async(): Promise<RoleDTO | undefined> => {
  const role = await authenticatedGet<RoleDTO | undefined>(`sso/users/user-worktime-role`);
  
  localStorage.setItem(LOCAL_STORAGE_WORKTIME_ROLE_NAME, role?.name ?? "");

  return role;  
}

export const setWorkTimeRole = async (roleId: string) => {
  return await authenticatedPost(`sso/users/worktime-role`, { roleId });
};
