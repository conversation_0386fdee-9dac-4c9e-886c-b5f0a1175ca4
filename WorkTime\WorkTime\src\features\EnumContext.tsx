import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";
import { IEnumType } from "../models/DTOs/enums/IEnumType";
import {
  initContractReasons,
  initIncomeTypes,
  initTerminationReason,
} from "../services/nomenclatures/contractsService";
import { initCountries } from "../services/nomenclatures/defaultLocationDataService";
import {
  initPayrollCategories,
  initTypesOfAppointment,
} from "../services/nomenclatures/nomenclatureService";

interface IEnumContext {
  contractReasons: IEnumType[];
  terminationReason: IEnumType[];
  countries: IEnumType[];
  incomeTypes: IEnumType[];
  appointmentTypes: IEnumType[];
  payrollCategories: IEnumType[];
}

interface EnumProviderProps {
  children: ReactNode;
}

const EnumContext = createContext<IEnumContext>({
  contractReasons: [],
  terminationReason: [],
  countries: [],
  incomeTypes: [],
  appointmentTypes: [],
  payrollCategories: [],
});

export const EnumProvider: React.FC<EnumProviderProps> = ({ children }) => {
  const [enums, setEnums] = useState<IEnumContext>({
    contractReasons: [],
    terminationReason: [],
    countries: [],
    incomeTypes: [],
    appointmentTypes: [],
    payrollCategories: [],
    //more enums
  });

  useEffect(() => {
    initContractReasons().then((contractReasons) => {
      setEnums((enums) => ({ ...enums, contractReasons }));
    });
    initTerminationReason().then((terminationReason) => {
      setEnums((enums) => ({ ...enums, terminationReason }));
    });
    initCountries().then((countries) => {
      setEnums((enums) => ({ ...enums, countries }));
    });
    initIncomeTypes().then((incomeTypes) => {
      setEnums((enums) => ({ ...enums, incomeTypes }));
    });
    initTypesOfAppointment().then((appointmentTypes) => {
      setEnums((enums) => ({ ...enums, appointmentTypes }));
    });
    initPayrollCategories().then((payrollCategories) => {
      setEnums((enums) => ({ ...enums, payrollCategories }));
    });
  }, []);

  return <EnumContext.Provider value={enums}>{children}</EnumContext.Provider>;
};

export const useEnums = () => useContext(EnumContext);
