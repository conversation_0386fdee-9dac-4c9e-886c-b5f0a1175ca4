import { useNavigate, useSearchParams } from "react-router-dom";
import MainWindowContainer from "../../components/MainWindowContainer";
import { ChangeEvent, MouseEvent, useEffect, useState } from "react";
import { useAuth } from "./AuthContext";
import PasswordBox from "../../components/Inputs/PasswordBox";
import Button from "../../components/Inputs/Button";
import { PasswordStrengthType } from "../../models/Enums/PasswortStrengthType";
import styled from "styled-components";
import Container from "../../components/Container";
import { passwordStrengthCheck } from "../../services/authentication/passwordService";
import { authenticatedPost } from "../../services/connectionService";
import { LOCAL_STORAGE_HAS_SIGNED_IN } from "../../constants/local-storage-constants";
import { ChangePasswordRequest } from "../../models/Requests/ChangePasswordRequest";
import { translate } from "../../services/language/Translator";
import { toast } from "react-toastify";
import { useMenu } from "../MenuContext";
import { PasswordStrengthIndicator } from "./PasswordStrengthIndicator";

const PasswordStrengthIndicatorContainer = styled.div`
  color: var(--password-strength-indicator-color);
  margin-top: 0.5rem;
  text-align: center;
  min-height: 1.5em;
`;

const MainContainer = styled(MainWindowContainer)`
  margin: 0 auto;
  width: 100%;
`;

const StyledButton = styled(Button)`
  width: 95%;
  position: fixed;
  bottom: 0;
  margin: 0.625rem;
  left: 0;
`;

const ChangePassword = () => {
  const [searchParams] = useSearchParams();
  const { user, setUser } = useAuth();
  const [password, setPassword] = useState("");
  const { closeMenu } = useMenu();
  const [oldPassword, setOldPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showStrengthHint, setShowStrengthHint] = useState(false);
  const [enabled, setEnabled] = useState(false);
  const navigate = useNavigate();
  const [passwordStrength, setPasswordStrength] = useState(
    PasswordStrengthType.Empty
  );

  useEffect(() => {
    setButtonEnabled();
  }, [password, confirmPassword, oldPassword, passwordStrength]);

  const handlePasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    setPassword(e.currentTarget.value);

    const currentPasswordStrength = passwordStrengthCheck(
      e.currentTarget.value
    );
    setPasswordStrength(currentPasswordStrength);
  };

  const handleoldPasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    setOldPassword(e.currentTarget.value);
  };

  const handleConfirmPasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    setConfirmPassword(e.currentTarget.value);
  };

  const setButtonEnabled = () => {
    setEnabled(
      password === confirmPassword &&
        password !== "" &&
        passwordStrength >= PasswordStrengthType.Strong &&
        oldPassword.length > 7
    );
  };

  const handleChangePasswordClick = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    const request = {
      oldPassword: oldPassword,
      password: password,
    } as ChangePasswordRequest;

    authenticatedPost("sso/change-password", request)
      .then(() => {
        localStorage.setItem(LOCAL_STORAGE_HAS_SIGNED_IN, "true");
        setUser({ ...user, hasSignedIn: true });
        navigate(searchParams.get("returnAfterPasswordChanged") ?? "/");
        toast.success(translate("Password changed successfully"), {
          position: "top-right",
          autoClose: 10000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
        setOldPassword("");
        setPassword("");
        setConfirmPassword("");
        closeMenu();
      })
      .catch((error) => {
        console.log(error);
        const errorMessage =
          error === 404
            ? translate("Incorrect password")
            : translate("BadRequestError");

        toast.error(errorMessage, {
          position: "top-right",
          autoClose: 10000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      });
  };

  const confirmPasswordValidation = {
    isValid:
      confirmPassword === undefined ||
      confirmPassword === "" ||
      password === undefined ||
      password === "" ||
      confirmPassword === password,
    alertMessage: translate("Passwords does not match"),
  };

  return (
    <MainContainer data-testid="create-employee-container">
      <PasswordBox
        name="oldPassword"
        handleChange={handleoldPasswordChange}
        label="Old Password"
        type="password"
        value={oldPassword}
        data-testid="password-box"
      />
      <PasswordBox
        name="password"
        handleChange={handlePasswordChange}
        label="New password"
        type="password"
        value={password}
        data-testid="password-box"
      />
      <PasswordBox
        name="confirm-password"
        handleChange={handleConfirmPasswordChange}
        label="Repeat password"
        type="password"
        value={confirmPassword}
        data-testid="confirm-password-box"
        validation={confirmPasswordValidation}
      />
      <div
        onMouseEnter={() => setShowStrengthHint(true)}
        onMouseLeave={() => setShowStrengthHint(false)}
      >
        <PasswordStrengthIndicator
          passwordStrength={passwordStrength}
          setShowStrengthHint={() => {}}
        />
        <PasswordStrengthIndicatorContainer
          style={{ visibility: showStrengthHint ? "visible" : "hidden" }}
        >
          {translate("strPasswordStrenghtIndicator")}
        </PasswordStrengthIndicatorContainer>
      </div>

      <StyledButton
        label="Save"
        disabled={!enabled}
        onClick={handleChangePasswordClick}
        data-testid="change-password-button"
      />
    </MainContainer>
  );
};

export default ChangePassword;
