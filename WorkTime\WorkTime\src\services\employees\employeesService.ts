import { EditEmployeeDTO } from "../../models/DTOs/editEmployee/EditEmployeeDTO";
import { NewEmployeeDTO } from "../../models/DTOs/newEmployee/NewEmployeeDTO";
import { EmployeePayrollDTO } from "../../models/DTOs/payrolls/EmployeePayrollDTO";
import { authenticatedPost } from "../connectionService";
import { authenticatedPut as authenticatedPutWorktime } from "../worktimeConnectionService";
import { authenticatedGet } from "../worktimeConnectionService";

export const addNewEmployeePayroll = async (newEmployeeDTO: NewEmployeeDTO) => {
  return await authenticatedPost<EmployeePayrollDTO>(
    `employee-payroll`,
    newEmployeeDTO
  );
};
export const editEmployee = async (editEmployeeDTO: EditEmployeeDTO) => {
  return await authenticatedPutWorktime<EditEmployeeDTO>(
    `employee`,
    editEmployeeDTO
  );
};

export const doesEmployeeExistInCompany = async (
  email: string,
  egn: string,
  companyId: string
) => {
  return await authenticatedGet(
    `employee-exists?email=${email}&egn=${egn}&companyId=${companyId}`
  );
};

export function isEGNValid(egn: string): boolean {
  if (!egn || egn.length !== 10 || !/^[0-9]+$/.test(egn)) return false;
  return true;
}

export function getBirthDateFromEGN(egn: string): Date | null {
  if (!isEGNValid(egn)) return null;
  const year = parseInt(egn.substring(0, 2), 10);
  let month = parseInt(egn.substring(2, 4), 10);
  const day = parseInt(egn.substring(4, 6), 10);
  let fullYear = 1900 + year;
  let jsMonth = month;
  if (month > 40) {
    jsMonth = month - 40;
    fullYear = 2000 + year;
  } else if (month > 20) {
    jsMonth = month - 20;
    fullYear = 1800 + year;
  } else {
    jsMonth = month;
    fullYear = 1900 + year;
  }

  const parsedDate = new Date(fullYear, jsMonth - 1, day);
  if (!isNaN(parsedDate.getTime())) {
    return parsedDate;
  }
  return null;
}
