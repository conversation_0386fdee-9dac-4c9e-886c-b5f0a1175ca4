import { styled } from "styled-components";
import Container from "../../components/Container";
import Button from "../../components/Inputs/Button";
import Label from "../../components/Inputs/Label";
import Image from "../../components/Image";

interface LabelProps {
  boldness?: number;
  fontSize?: number;
  color?: string;
  justifyContent?: string;
  align?: string;
  onClick?: () => void;
}

interface InsideContainerProps {
  variant?: "AbsencesContainer" | "TeamContainer";
}

export const OuterContainer = styled(Container)`
  display: block;
  height: 100%;
  box-sizing: border-box;
`;
export const ToggleContainer = styled(Container)`
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1em;
  align-items: center;
  gap: 0.8rem;
`;

export const ToggleLabel = styled.span<{ showMyAbsences: boolean }>`
  color: ${({ showMyAbsences }) =>
    showMyAbsences
      ? "var(--datepicker-toggle-text-active-color)"
      : "var(--datepicker-toggle-text-inactive-color)"};
  font-size: 1.1rem;
`;

export const ToggleSwitch = styled.label`
  position: relative;
  display: inline-block;
  width: 3.2rem;
  height: 30px;
  cursor: pointer;
`;

export const ToggleInput = styled.input`
  opacity: 0;
  width: 0;
  height: 0;

  &:checked + span {
    background-color: #ffffff;
    border: 1px solid #e6e9f0;
  }

  &:checked + span:before {
    transform: translateX(1.4rem);
    background-color: #45b6f8;
  }
`;

export const ToggleSlider = styled.span`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ffffff;
  border: 1px solid #e6e9f0;
  transition: 0.4s;
  border-radius: 2rem;

  &:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 2px;
    bottom: 3px;
    background-color: #bdc4d6;
    transition: 0.4s;
    border-radius: 50%;
  }
`;

export const TopContainer = styled(Container)`
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-bottom: 1em;
`;

export const Icon = styled(Image)<{ marginRight?: number }>`
  margin-right: ${(props) => props.marginRight || 0}rem;
  margin-top: 1rem;
  margin-bottom: 1rem;
`;

export const CardContainer = styled(Container)<InsideContainerProps>`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  justify-content: start;
  margin-bottom: 1em;
  height: 10rem;
`;

export const MainCardContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 16rem;
  width: 12rem;
  border-radius: 1.8rem;
  background-color: var(--attendancies-right-view-card-container-background);
`;

export const ButtonContainer = styled(Container)`
  display: flex;
  justify-content: center;
  margin-bottom: 1em;
`;

export const StyledButton = styled(Button)`
  width: 15em;
  height: 2.8em;
  transition: 0.3s ease-in-out;

  &:hover {
    background-color: var(--attendancies-right-view-button-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
`;

export const StyledLabel = styled(Label)<LabelProps>`
  text-align: ${(props) => props.align || "center"};
  color: ${(props) => props.color || "inherit"};
  font-weight: ${(props) => props.boldness || "normal"};
  font-size: ${(props) => (props.fontSize ? `${props.fontSize}px` : "inherit")};
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;
