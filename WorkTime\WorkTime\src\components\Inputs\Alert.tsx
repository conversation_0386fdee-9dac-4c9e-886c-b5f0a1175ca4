import Translator from "../../services/language/Translator";
import styled from "styled-components";

interface AlertProps {
  type: "error" | "warning" | "success";
  color?: string;
  message: string;
}

const AlertDiv = styled.div`
  color: var(--text-box-alert-message-color);
`;

const Alert = ({ message, type, color }: AlertProps) => {
  return (
    <AlertDiv data-testid={`alert-${type}`} style={{ color: color }}>
      <Translator data-testid="alert-message" getString={message} />
    </AlertDiv>
  );
};

export default Alert;
