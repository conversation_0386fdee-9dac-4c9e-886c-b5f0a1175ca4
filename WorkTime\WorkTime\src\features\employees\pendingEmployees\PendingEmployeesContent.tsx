import { useEffect, useState } from "react";
import { styled } from "styled-components";
import { useAppDispatch, useAppSelector } from "../../../app/hooks";
import Container from "../../../components/Container";
import Button from "../../../components/Inputs/Button";
import RadioButton from "../../../components/Inputs/RadioButton";
import MainWindowContainer from "../../../components/MainWindowContainer";
import { Direction } from "../../../components/Table/Direction";
import Table, { ColumnDefinitionType } from "../../../components/Table/Table";
import {
  onStructureLevelsLoaded,
  selectStructureLevels,
} from "../../company-structure/companyStructureActions";
import { useEnums } from "../../EnumContext";
import {
  filterEmployees,
  findDepartmentById,
  sortEmployees,
} from "../employeeListUtils";
import {
  onPendingEmployeesLoaded,
  onTRZEmployeesImported,
  selectEmployees,
} from "../employeesActions";
import { PendingEmployeeView } from "../EmployeeViewInterfaces";
import TableHeaderExtended from "../TableHeaderExtended";
import EmployeeMarker from "./EmployeeMarker";
import { useEmployeeSelection } from "./EmployeeSelectionContext";
import { useNavigate } from "react-router-dom";
import { useCompany } from "../../companies/CompanyContext";
import { mapDepartmentToNestedOption } from "../../../services/departments/departmentService";

const ImportButtonContainer = styled(Container)`
  width: 100%;
  min-width: 60rem;
  display: inline-block;
  justify-content: flex-end;
`;

const ImportButton = styled(Button)`
  height: 3.3125rem;
  width: 11.375rem;
  cursor: pointer;
  float: right;
  padding: 0.5rem 1.5rem 0.5rem 1.5rem;
  margin: 0rem 5rem;
`;

const PendingEmployeesContent = () => {
  const dispatch = useAppDispatch();
  const { payrollCategories, appointmentTypes } = useEnums();
  const pendingEmployees = useAppSelector(selectEmployees).pendingEmployees;
  const {
    selectedEmployeePayrollIds: selectedEmployeePayrollIds,
    selectAllEmployees,
    isAllEmployeesSelected,
    clearSelection,
  } = useEmployeeSelection();
  const { company } = useCompany();
  const structureLevels = useAppSelector(selectStructureLevels).structureLevels;
  const [sort, setSort] = useState<{ [key: string]: number }>({});
  const [departmentsOptions, setDepartmentsOptions] = useState<any[]>([]);
  const [mappedEmployees, setMappedEmployees] = useState<PendingEmployeeView[]>(
    []
  );
  const navigate = useNavigate();
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [filteredEmployees, setFilteredEmployees] = useState<
    PendingEmployeeView[]
  >([]);

  const [filters, setFilters] = useState({
    personalData: "",
    position: "",
    departments: [] as string[],
    categories: [] as string[],
    typeOfAppointment: [] as string[],
  });

  const categoriesOptions = payrollCategories.map((category) => ({
    identifier: category.identifier.toString(),
    name: category.name,
    description: category.name,
  }));

  const typesOfAppointmentOptions = appointmentTypes.map((type) => ({
    identifier: type.identifier.toString(),
    name: type.name,
    description: type.name,
  }));

  const rowColumns: ColumnDefinitionType<
    PendingEmployeeView,
    keyof PendingEmployeeView
  >[] = [
    { key: "marker", value: "" },
    { key: "fullName", value: "strName" },
    { key: "egn", value: "strEgn" },
    { key: "position", value: "strPosition" },
    { key: "department", value: "strDepartment" },
    { key: "typeOfAppointment", value: "strTypeOfAppointment" },
    { key: "number", value: "№" },
  ];

  useEffect(() => {
    dispatch(onPendingEmployeesLoaded(company.id));
    dispatch(onStructureLevelsLoaded(company.id));
  }, [dispatch, company.id]);

  useEffect(() => {
    if (structureLevels !== undefined) {
      setDepartmentsOptions(structureLevels.map(mapDepartmentToNestedOption));
    }
  }, [structureLevels]);

  useEffect(() => {
    const mapped = mapToEmployeeListView(pendingEmployees);
    setMappedEmployees(mapped);
    onSortChange("fullName", Direction.Ascending);
  }, [departmentsOptions, pendingEmployees]);

  useEffect(() => {
    const filteredEmployees = filterEmployees(
      mappedEmployees,
      filters,
      departmentsOptions,
      categoriesOptions,
      typesOfAppointmentOptions
    );
    const sortedEmployees = sortEmployees(filteredEmployees, sort);
    setFilteredEmployees(sortedEmployees);
  }, [filters, sort, mappedEmployees]);

  const onTextFilterChange = (columnKey: string, searchText: string) => {
    setFilters((prev) => ({
      ...prev,
      [columnKey]: searchText,
    }));
  };

  const onFilterChange = (
    filterType: "departments" | "typeOfAppointment" | "categories",
    selected: string[]
  ) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: selected,
    }));
  };

  const onSortChange = (columnKey: string, direction: Direction) => {
    const directionValue = direction === Direction.Ascending ? 1 : -1;
    setSort({ [columnKey]: directionValue });
  };

  const handleSelectAll = (isChecked: boolean) => {
    const employeePayrollIds = filteredEmployees
      .map((emp) => emp.payrollId)
      .filter((id): id is string => id !== undefined);
    selectAllEmployees(employeePayrollIds, isChecked);
  };

  useEffect(() => {
    const currentEmployeePayrollIds = filteredEmployees
      .map((emp) => emp.payrollId)
      .filter((id): id is string => id !== undefined);

    if (currentEmployeePayrollIds.length === 0) {
      setIsAllSelected(false);
    } else {
      const allSelected = isAllEmployeesSelected(currentEmployeePayrollIds);
      setIsAllSelected(allSelected);
    }
  }, [filteredEmployees, selectedEmployeePayrollIds, isAllEmployeesSelected]);

  const mapToEmployeeListView = (employees: any[]): PendingEmployeeView[] => {
    const employeeListView = employees.map((employeePayroll) => {
      const departmentInfo = findDepartmentById(
        departmentsOptions,
        employeePayroll.structureId
      );

      return {
        id: employeePayroll.employeeGuid,
        workTimeId: employeePayroll.employeeGuid,
        payrollId: employeePayroll.payrollId,
        fullName: [
          employeePayroll.firstName,
          employeePayroll.secondName,
          employeePayroll.lastName,
        ]
          .filter((name) => name != null)
          .join(" "),
        egn: employeePayroll.egn,
        email: employeePayroll.email,
        position: employeePayroll.positionName,
        departmentId: departmentInfo?.identifier || "",
        department: departmentInfo?.name || "",
        contractType: employeePayroll.contractNumber,
        category: employeePayroll.category,
        typeOfAppointment: employeePayroll.typeOfAppointment,
        number: employeePayroll.contractNumber || "",
        isChecked: false,
      };
    });

    return employeeListView;
  };

  const handleImportEmployees = async () => {
    await dispatch(
      onTRZEmployeesImported(selectedEmployeePayrollIds, company.id)
    );

    if (isAllSelected) {
      navigate(`/${company.id}/employees`);
    }

    clearSelection();
  };

  const employeesWithRadioButtons = filteredEmployees.map((employee) => ({
    ...employee,
    marker: <EmployeeMarker employeePayrollId={employee.payrollId || ""} />,
  }));

  return (
    <MainWindowContainer data-testid="employees-list-container">
      <ImportButtonContainer>
        <ImportButton
          label="Import"
          onClick={handleImportEmployees}
          disabled={
            selectedEmployeePayrollIds.length === 0 ||
            selectedEmployeePayrollIds === undefined
          }
          data-testid="import-all-button"
        />
      </ImportButtonContainer>

      <Table
        data-testid="employees-table"
        data={employeesWithRadioButtons}
        columns={rowColumns}
        onSortChange={
          onSortChange as (columnKey: string, direction: Direction) => void
        }
        headerComponent={
          <TableHeaderExtended
            data-testid="table-header-extended"
            radioButton={
              <RadioButton
                isChecked={isAllSelected}
                handleChange={handleSelectAll}
              />
            }
            departmentOptions={departmentsOptions}
            categoryOptions={categoriesOptions}
            typeOfAppointmentOptions={typesOfAppointmentOptions}
            selectedDepartments={filters.departments}
            selectedCategories={filters.categories}
            selectedTypeOfAppointment={filters.typeOfAppointment}
            onDepartmentChange={(selected) =>
              onFilterChange("departments", selected)
            }
            onTypeOfAppointmentChange={(selected) =>
              onFilterChange("typeOfAppointment", selected)
            }
            onCategoryChange={(selected) =>
              onFilterChange("categories", selected)
            }
            onTextFilterChange={onTextFilterChange}
          />
        }
      />
    </MainWindowContainer>
  );
};

export default PendingEmployeesContent;
