import { createContext, useContext, useEffect, useState } from "react";
import { initUser } from "../../services/authentication/authenticationService";

export interface User {
  userId: string | undefined;
  firstName: string | undefined;
  secondName: string | undefined;
  lastName: string | undefined;
  email: string | undefined;
  hasSignedIn: boolean;
  workTimeRoleName?: string;
}

const initialUserState: User = {
  userId: undefined,
  firstName: undefined,
  secondName: undefined,
  lastName: undefined,
  email: undefined,
  hasSignedIn: false,
  workTimeRoleName: undefined,
};

interface AuthContextType {
  user: User;
  setUser: (user: User) => void;
  resetUser: () => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User>(initialUserState);
  const [isLoading, setIsLoading] = useState(false);

  const resetUser = () => {
    setUser(initialUserState);
  };

  useEffect(() => {
    const initializeUser = async () => {
      setIsLoading(true);
      const userData = await initUser();
      setUser(userData);
      setIsLoading(false);
    };

    initializeUser();
  }, [setUser]);

  return (
    <AuthContext.Provider
      value={{ user, setUser, isLoading, resetUser, setIsLoading }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within a AuthProvider");
  }
  return context;
};
