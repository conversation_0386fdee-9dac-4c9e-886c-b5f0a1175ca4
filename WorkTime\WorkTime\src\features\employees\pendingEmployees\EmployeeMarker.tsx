import React, { useEffect, useState } from "react";
import RadioButton from "../../../components/Inputs/RadioButton";
import { useEmployeeSelection } from "./EmployeeSelectionContext";

interface EmployeeMarkerProps {
  employeePayrollId: string;
}

const EmployeeMarker: React.FC<EmployeeMarkerProps> = ({
  employeePayrollId,
}) => {
  const { selectedEmployeePayrollIds, selectEmployee } = useEmployeeSelection();
  const [isSelected, setIsSelected] = useState(false);

  useEffect(() => {
    setIsSelected(selectedEmployeePayrollIds.includes(employeePayrollId));
  }, [selectedEmployeePayrollIds]);

  const handleToggle = (isChecked: boolean) => {
    selectEmployee(employeePayrollId, isChecked);
  };

  return (
    <RadioButton
      data-testid={`employee-avatar-${employeePayrollId}`}
      isChecked={isSelected}
      handleChange={handleToggle}
    />
  );
};

export default EmployeeMarker;
