import { Genders } from "../../../constants/enum";
import { AddressPurpose } from "../address/AddressDTO";
import { IdentityCardDataDTO } from "./IdentityCardDataDTO";
import { NewAddressDTO } from "./NewAddressDTO";
import { PersonalDataDTO } from "./PersonalDataDTO";

interface FormValidationState {
  isValid: boolean;
  data: any;
}

export interface NewEmployeeFormData {
  personalData: FormValidationState;
  identityCardData: FormValidationState;
  addressData: FormValidationState;
  payrollData?: FormValidationState;
}

export const initialNewEmployeeFormData: NewEmployeeFormData = {
  personalData: {
    isValid: false,
    data: {
      firstName: "",
      secondName: "",
      lastName: "",
      egn: "",
      birthDate: undefined,
      contractType: undefined,
      birthPlace: "",
      iban: "",
      email: "",
    } as PersonalDataDTO,
  },
  identityCardData: {
    isValid: false,
    data: {
      idNumber: "",
      issuedOn: undefined,
      issuedBy: "",
      citizenship: "",
      gender: Genders.None,
      idAddress: {
        city: null,
        postalCode: "",
        region: "",
        municipality: null,
        district: null,
        street: "",
        block: "",
        apartment: "",
        phone: "",
        workPhone: "",
        email: "",
        workEmail: "",
        country: undefined,
        purpose: AddressPurpose.IdentityCard,
        description: "",
        neighborhood: "",
        cityName: "",
        districtName: "",
        municipalityName: "",
      },
    } as IdentityCardDataDTO,
  },
  addressData: {
    isValid: false,
    data: {
      city: null,
      postalCode: "",
      region: "",
      municipality: null,
      district: null,
      street: "",
      block: "",
      apartment: "",
      phone: "",
      workPhone: "",
      email: "",
      workEmail: "",
      country: undefined,
      purpose: AddressPurpose.ForContact,
      description: "",
      neighborhood: "",
      cityName: "",
    } as NewAddressDTO,
  },
  payrollData: { isValid: false, data: {} },
};
