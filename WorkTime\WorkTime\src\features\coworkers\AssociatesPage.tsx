import { useEffect } from "react";
import { useMenu } from "../MenuContext";
import { useNavigate } from "react-router-dom";
import { DefaultPermissions } from "../../constants/permissions";
import { useUserEmployee } from "../UserEmployeeContext";
import { LOCAL_STORAGE_COMPANY_ID } from "../../constants/local-storage-constants";

const AssociatesPage = () => {
  const { changeView, toggleMenu } = useMenu();
  const navigate = useNavigate();
  const { userEmployee } = useUserEmployee();
  const companyId = localStorage.getItem(LOCAL_STORAGE_COMPANY_ID) ?? "";

  useEffect(() => {
    toggleMenu();
    changeView("coworkers", "associates");

    if (userEmployee.permissions.includes(DefaultPermissions.Employees.Write))
      navigate(`/${companyId}/employees`);
    else
      navigate(
        `/${companyId}/employees/${userEmployee.employeeId}/${
          userEmployee.payrolls[0]?.payrollId ?? ""
        }`
      );
  }, []);

  return <></>;
};

export default AssociatesPage;
