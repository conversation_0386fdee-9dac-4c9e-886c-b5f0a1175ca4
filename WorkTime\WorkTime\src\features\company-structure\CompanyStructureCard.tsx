import microImage from "../../assets/images/company-structure/micro.png";
import styled from "styled-components";

const CompanyCard = styled.div`
  box-sizing: border-box;
  background-color: var(--auth-button-background-color);
  color: var(--company-name-label);
  width: 10rem;
  height: 9.8rem;
  margin: 0;
  text-decoration: none;
  justify-content: center;
  align-items: center;
  display: inline-block;
  border-radius: 1.5em;
`;

const CompanyImage = styled.img`
  box-sizing: border-box;
  width: 50%;
  margin-top: 2rem;
  object-fit: cover;
  border-radius: 1rem;
`;

const CompanyText = styled.p`
  box-sizing: border-box;
  margin: 0;
  text-align: center;
  text-transform: uppercase;
`;

interface Props {
  companyName: string;
}

const CompanyStructureCard = ({ companyName }: Props) => {
  return (
    <CompanyCard data-testid="company-card">
      <CompanyImage src={microImage} alt="" data-testid="company-image" />
      <CompanyText data-testid="company-text">{companyName}</CompanyText>
    </CompanyCard>
  );
};

export default CompanyStructureCard;
