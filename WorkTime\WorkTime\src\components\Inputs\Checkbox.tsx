import styled from "styled-components";
import Translator from "../../services/language/Translator";
import React from "react";
import CheckMark from "../../assets/images/controls/check-mark.png";

interface CheckboxProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  label: string;
  isChecked?: boolean;
  handleChange: (checked: boolean) => void;
  name: string;
  disabled?: boolean;
  labelStyles?: React.CSSProperties;
}

interface StyledCheckboxProps {
  isChecked?: boolean;
}

interface LabelProps {
  customStyles?: React.CSSProperties;
}
const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  padding: 0.5rem 0 0.5rem 0;

  &:hover {
    cursor: pointer;
  }
`;

const HiddenCheckbox = styled.input.attrs({ type: "checkbox" })`
  border: 0;
  clip: rect(0 0 0 0);
  height: 0.075rem;
  margin: -0.075rem;
  overflow: hidden;
  padding: 0;
  position: absolute;
  white-space: nowrap;
  width: 0.075rem;
`;

const StyledCheckbox = styled.div<StyledCheckboxProps>`
  display: inline-block;
  width: 0.95rem;
  height: 0.95rem;
  background: ${(props) =>
    props.isChecked
      ? `var(--check-box-background-color) url(${CheckMark}) center/contain no-repeat`
      : "transparent"};
  border: 1px solid var(--check-box-border-color);
  border-radius: 0.25rem;
`;

const CheckboxLabel = styled.label<LabelProps>`
  margin-left: 0.5rem;
  &:hover {
    cursor: pointer;
  }

  user-select: none;
`;

const Checkbox = (props: CheckboxProps) => {
  const { label, isChecked, handleChange, name, disabled, labelStyles } = props;

  return (
    <CheckboxContainer {...props} data-testid={`checkbox-container-${name}`}>
      <HiddenCheckbox
        id={name}
        name={name}
        checked={isChecked}
        disabled={disabled}
        onChange={(e) => handleChange(e.target.checked)}
        data-testid={`checkbox-input-${name}`}
      />
      <StyledCheckbox
        isChecked={isChecked}
        onClick={() => handleChange(!isChecked)}
        data-testid={`checkbox-styled-${name}`}
      />
      <CheckboxLabel
        customStyles={labelStyles}
        data-testid={`checkbox-label-${name}`}
      >
        <Translator getString={label} />
      </CheckboxLabel>
    </CheckboxContainer>
  );
};

export default Checkbox;
