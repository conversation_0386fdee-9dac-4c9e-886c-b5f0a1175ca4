import { TRZLengthOfServiceDTO } from "./TRZLengthOfServiceDTO";
import { TRZPayroll } from "./TRZPayrollDTO";

export interface TRZEmployee {
    workTimeId: string;  
    firstName?: string;
    lastName?: string;
    egn?: string;
    photo?: Uint8Array; 
    employeeId?: number;
    idNumber?: string;
    idIssuedFrom?: string;
    idIssueDate?: Date;
    birthDate?: Date;
    birthPlace?: string;
    email?: string;
    isForeigner?: boolean;
    gender?: boolean;
    phone?: string;
    workPhone?: string;
    isDeleted: boolean;
    number?: string;
    education?: number;
    password?: string;
    //egnType?: NomenclatureDTO; 
    payrolls?: TRZPayroll[]; 
    lengthsOfService?: TRZLengthOfServiceDTO[];
}