import * as signalR from "@microsoft/signalr";
import { WORKTIME_API_PATH } from "../../constants/api-constants";

export class SignalRService {
  private connection: signalR.HubConnection | null = null;

  constructor(
    private getToken: () => string,
    private getRefreshToken: () => string
  ) {}

  public async connect(onEvent: (eventName: string, data: any) => void) {
    this.connection = new signalR.HubConnectionBuilder()
      .withUrl(
        `${WORKTIME_API_PATH}hubs/notifications?access_token=${this.getToken()}&refresh_token=${this.getRefreshToken()}`,
        {
          transport: signalR.HttpTransportType.WebSockets,
          skipNegotiation: false,
          withCredentials: true,
        }
      )
      .withAutomaticReconnect([0, 2000, 5000, 10000, 20000]) // Retry intervals in milliseconds
      .configureLogging(signalR.LogLevel.Information)
      .build();

    this.connection.onclose((error) => {
      console.error("SignalR disconnected", error);
    });

    this.connection.onreconnected((connectionId) => {
      console.log("SignalR reconnected with connection ID:", connectionId);
    });

    this.connection.onreconnecting((error) => {
      console.log("SignalR reconnecting...", error);
    });

    // Generic message handler
    this.connection.on("ReceiveMessage", (eventName: string, data: any) => {
      onEvent(eventName, data);
    });

    try {
      await this.connection.start();
      console.log("SignalR connected");
    } catch (error) {
      console.error("SignalR connection error:", error);
      // Attempt to reconnect if the initial connection fails
      setTimeout(() => this.connect(onEvent), 5000);
    }
  }

  public disconnect() {
    if (this.connection) {
      this.connection.stop();
    }
  }
}
