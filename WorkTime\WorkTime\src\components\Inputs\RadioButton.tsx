import { styled } from "styled-components";
import Container from "../Container";
import React, { useState, useEffect, useCallback } from "react";
import Image from "../Image";
import checkActive from "../../assets/images/combobox/check-active.svg";
import checkHover from "../../assets/images/combobox/check-hover.svg";
import checkInactive from "../../assets/images/combobox/check-inactive.svg";

const RadioContainer = styled(Container)`
  display: inline-block;
  vertical-align: middle;
  margin: 0rem 1rem;
  position: relative;
  user-select: none;
`;

const CheckmarkImage = styled.img`
  position: relative;
  background-repeat: no-repeat;
  background-position: center;
  width: 1.2rem;
  height: 1.2rem;
  transition: all 0.2s ease;
  pointer-events: none;
  user-select: none;
`;

const RadioCircle = styled(Container)<{ checked: boolean }>`
  position: relative;
  width: 1.2rem;
  height: 1.2rem;
  cursor: pointer;
  border-radius: 50%;
  background-color: var(--combobox-checkbox-background-color);
  transition: all 0.2s ease;
  overflow: visible;
`;

interface RadioButtonProps
  extends React.DetailedHTMLProps<
    React.InputHTMLAttributes<HTMLInputElement>,
    HTMLInputElement
  > {
  isChecked: boolean;
  handleChange: (checked: boolean) => void;
  disabled?: boolean;
}

const RadioButton = ({
  isChecked,
  handleChange,
  disabled,
}: RadioButtonProps) => {
  const [checked, setChecked] = useState(isChecked);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    setChecked(isChecked);
  }, [isChecked]);

  const getCheckmarkImage = () => {
    if (checked) {
      return checkActive;
    } else if (isHovered) {
      return checkHover;
    } else {
      return checkInactive;
    }
  };

  const handleClick = () => {
    if (disabled) return;
    const newValue = !checked;
    setChecked(newValue);
    handleChange(newValue);
  };

  return (
    <RadioContainer>
      <RadioCircle
        checked={checked}
        data-testid={`radio-${checked}`}
        style={{ cursor: disabled ? "not-allowed" : "pointer" }}
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <CheckmarkImage
          src={getCheckmarkImage()}
          data-testid={`checkmark-${checked}`}
        />
      </RadioCircle>
    </RadioContainer>
  );
};

export default RadioButton;
