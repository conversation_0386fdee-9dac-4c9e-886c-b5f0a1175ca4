import React from "react";
import styled from "styled-components";
import Translator from "../../services/language/Translator";
import { ViewMode } from "../../models/Enums/ViewMode";
import Container from "../Container";
import Label from "../Inputs/Label";
import { daysOfWeekShort } from "../CalendarComponent/constants/Names";

const StyledLabel = styled(Label)<{ variant: string }>`
  margin-left: 0.1em;
  color: ${(props) =>
    props.variant === "currentMonth"
      ? "var(--datepicker-view-buttons-font-color)"
      : "var(--datepicker-view-buttons-font-color-not-current-month)"};
`;

interface ContainerDayProps {
  variant?: "currentMonth" | "notCurrentMonth";
  selected?: boolean;
}

const ContainerNameOfDay = styled(Container)`
  display: grid;
  justify-content: center;
  align-items: center;
  padding: 0.1em;
  font-size: 0.85em;
  color: var(--datepicker-view-buttons-font-color);
  width: 1.6em;
  height: 1.5em;
  grid-template-columns: repeat(7, 1fr);
  cursor: pointer;
`;

const ContainerDay = styled(Container)<ContainerDayProps>`
  display: grid;
  position: relative;
  height: 1.7em;
  font-size: 1em;

  justify-content: left;
  cursor: pointer;

  &::after,
  &::before {
    content: "";
    position: absolute;
    background-color: ${(props) =>
      props.variant === "currentMonth"
        ? "var(--datepicker-view-buttons-color)"
        : "var(--datepicker-view-background-color)"};
    z-index: 999;
    pointer-events: none;
  }

  &::after {
    width: 50vw;
    height: 0.05em;
  }

  &::before {
    left: -0.05em;
    width: 0.05em;
    height: 50vh;
  }

  background-color: ${(props) =>
    props.variant === "currentMonth"
      ? "var(--datepicker-view-background-color)"
      : "var(--datepicker-view-buttons-color)"};

  background-color: ${({ selected }) =>
    selected ? "var(--datepicker-selected-day-color)" : ""};

  &:hover {
    background-color: ${({ selected }) =>
      selected
        ? "var(--datepicker-selected-day-color)"
        : "var(--datepicker-view-buttons-hover-color)"};
  }
`;

const ContainerDays = styled.div`
  display: grid;
  grid-auto-rows: minmax(min-content, 1fr);
  grid-template-columns: repeat(7, 1fr);
  height: minmax(min-content, 1fr);
  background-color: var(--datepicker-view-border);
  overflow: hidden;
`;

const MainContainer = styled.div<{ active: ViewMode }>`
  display: none;
  border-radius: 0em 0em 1.8em 1.8em;
  background-color: var(--datepicker-view-background-color);
  padding: 0em 0.7em 0.7em 0.7em;
  width: 14.5em;
  ${({ active }) =>
    active === ViewMode.DatesView &&
    `
    display: block;
  `}
`;

interface DateElementProps {
  selectedDay: number;
  selectedMonth: number;
  selectedYear: number;
  handleLastMonthDateClick?: (day: number) => void;
  handleDateClick: (day: number, index?: number) => void;
  handleNextMonthDateClick?: (day: number) => void;
  active: ViewMode;
}

const DatesElement: React.FC<DateElementProps> = ({
  selectedDay,
  selectedMonth,
  selectedYear,
  handleDateClick,
  active,
}) => {
  const daysInMonth = (selectedMonth: number, year: number) => {
    return new Date(year, selectedMonth + 1, 0).getDate();
  };

  const firstDayOfMonth = new Date(selectedYear, selectedMonth, 1).getDay();
  const numDaysCurrentMonth = daysInMonth(selectedMonth, selectedYear);

  const adjustDayIndex = (dayIndex: number) => (dayIndex === 0 ? 7 : dayIndex);
  const prevMonthDisplayDays = adjustDayIndex(firstDayOfMonth) - 1;

  const prevMonthDays = Array.from(
    { length: prevMonthDisplayDays },
    (_, i) =>
      daysInMonth(selectedMonth - 1, selectedYear) -
      prevMonthDisplayDays +
      i +
      1
  );

  const currentMonthDays = Array.from(
    { length: numDaysCurrentMonth },
    (_, i) => i + 1
  );

  const totalDays = prevMonthDisplayDays + numDaysCurrentMonth;
  const nextMonthDisplayDays = totalDays % 7 === 0 ? 0 : 7 - (totalDays % 7);

  const nextMonthDays = Array.from(
    { length: nextMonthDisplayDays },
    (_, i) => i + 1
  );
  const isSelected = (index: number) => {
    return selectedDay == index;
  };

  return (
    <MainContainer active={active} data-testid="date-element-container">
      <ContainerDays data-testid="date-element-days-grid">
        {daysOfWeekShort.map((day) => (
          <ContainerNameOfDay data-testid={`day-header-${day}`}>
            <Translator getString={day}></Translator>
          </ContainerNameOfDay>
        ))}
        {prevMonthDays.map((day, index) => (
          <ContainerDay
            variant="notCurrentMonth"
            key={`prev-day-${index}`}
            onClick={() => handleDateClick(day, -1)}
            data-testid={`prev-month-day-${day}`}
          >
            <StyledLabel
              children={day.toString()}
              variant="notCurrentMonth"
              data-testid={`prev-month-label-${day}`}
            ></StyledLabel>
          </ContainerDay>
        ))}
        {currentMonthDays.map((day, index) => (
          <ContainerDay
            variant="currentMonth"
            key={`current-day-${index}`}
            selected={isSelected(day)}
            onClick={() => handleDateClick(day)}
            data-testid={`current-month-day-${day}`}
          >
            <StyledLabel
              children={day.toString()}
              variant="currentMonth"
              data-testid={`current-month-label-${day}`}
            ></StyledLabel>
          </ContainerDay>
        ))}
        {nextMonthDays.map((day, index) => (
          <ContainerDay
            variant="notCurrentMonth"
            key={`next-day-${index}`}
            onClick={() => handleDateClick(day, +1)}
            data-testid={`next-month-day-${day}`}
          >
            <StyledLabel
              children={day.toString()}
              variant="notCurrentMonth"
              data-testid={`next-month-label-${day}`}
            ></StyledLabel>
          </ContainerDay>
        ))}
      </ContainerDays>
    </MainContainer>
  );
};

export default DatesElement;
