import { CompanyDTO } from "../../../models/DTOs/companies/CompanyDTO";
import styled from "styled-components";
import Label from "../../../components/Inputs/Label";
import Container from "../../../components/Container";
import Button from "../../../components/Inputs/Button";
import { translate } from "../../../services/language/Translator";
import { onAcceptCompany, onDeclineCompany } from "../companiesActions";
import { authenticatedPut } from "../../../services/connectionService";
import { useAppDispatch } from "../../../app/hooks";

const Title = styled(Label)`
  font: Segoe UI;
  color: var(--listview-text);
  opacity: 1;
  font-size: 1.5rem;
  text-align: left;
  margin-bottom: 1rem;
`;

const CompanyName = styled(Label)`
  font: Segoe UI;
  color: var(--listview-text);
  opacity: 1;
  font-size: 1.2rem;
  text-align: left;
  vertical-align: middle;
  overflow: hidden;
`;

const CompanyContainer = styled(Container)`
  display: grid;
  padding: 1rem;
  border: none;
  grid-template-columns: 1fr auto auto;
  align-items: center;
  height: 3.5rem;
  border-radius: 1.8rem;
  border: 0.2rem solid transparent;
  margin: 0.1rem;
  gap: 1rem;
  background-color: var(--listview-backround-hover-button);
`;

const ButtonContainer = styled(Container)`
  display: flex;
  gap: 0.5rem;
`;

const AcceptButton = styled(Button)`
  background-color: var(--success-color, #45d14c);
  &:hover {
    background-color: var(--success-color-hover, #3ead44);
  }
`;

const DeclineButton = styled(Button)`
  background-color: var(--error-color, #f73131);
  &:hover {
    background-color: var(--error-color-hover, #c22727);
  }
`;

const PendingCompaniesContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: clamp(35%, 55rem, 70%);
`;

interface PendingCompaniesListProps {
  pendingCompanies: CompanyDTO[];
}

export const PendingCompaniesList = ({
  pendingCompanies,
}: PendingCompaniesListProps) => {
  const dispatch = useAppDispatch();

  const handleCompanyAccepted = async (company: CompanyDTO) => {
    await authenticatedPut(`company/invitation`, {
      companyId: company.id,
      isCompanyAccepted: true,
    });

    dispatch(onAcceptCompany(company));
  };

  const handleCompanyDeclined = async (company: CompanyDTO) => {
    await authenticatedPut(`company/invitation`, {
      companyId: company.id,
      isCompanyAccepted: false,
    });

    dispatch(onDeclineCompany(company));
  };

  return pendingCompanies.length > 0 ? (
    <PendingCompaniesContainer data-testid="pending-companies-container">
      <Title data-testid="title">{translate("Pending companies")}</Title>
      {pendingCompanies.map((company) => (
        <CompanyContainer key={company.name} data-testid="company-container">
          <CompanyName data-testid="company-name">{company.name}</CompanyName>
          <ButtonContainer data-testid="button-container">
            <AcceptButton
              data-testid="accept-button"
              onClick={() => handleCompanyAccepted(company)}
              label="strAccept"
            />
            <DeclineButton
              data-testid="decline-button"
              onClick={() => handleCompanyDeclined(company)}
              label="strDecline"
            />
          </ButtonContainer>
        </CompanyContainer>
      ))}
    </PendingCompaniesContainer>
  ) : (
    <></>
  );
};
