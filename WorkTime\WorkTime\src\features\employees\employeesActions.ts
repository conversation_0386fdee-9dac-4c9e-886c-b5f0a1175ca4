import { toast } from "react-toastify";
import { Action, Reducer } from "redux";
import { AppThunk, ClearStateAction, RootState } from "../../app/store";
import { EmployeeDTO } from "../../models/DTOs/employees/EmployeeDTO";
import { LightPayrollDTO } from "../../models/DTOs/payrolls/LightPayrollDTO";
import { CreateEmployeeForCompanyRequest } from "../../models/Requests/CreateEmployeeForCompanyRequest";
import {
  authenticatedDelete,
  authenticatedGet,
  authenticatedPost,
} from "../../services/connectionService";
import { translate } from "../../services/language/Translator";
import { EmployeePayrollDTO } from "../../models/DTOs/payrolls/EmployeePayrollDTO";
import { EditEmployeeDTO } from "../../models/DTOs/editEmployee/EditEmployeeDTO";

interface EmployeesState {
  employees: EmployeeDTO[];
  pendingEmployees: EmployeePayrollDTO[];
}

interface AddEmployeeAction {
  type: "ADD_EMPLOYEE";
  employee: EmployeeDTO;
}

interface LoadEmployeesAction {
  type: "LOAD_EMPLOYEES";
  employees: EmployeeDTO[];
}

interface LoadPendingEmployeesAction {
  type: "LOAD_PENDING_EMPLOYEES";
  pendingEmployees: EmployeePayrollDTO[];
}

interface AddPendingEmployeesAction {
  type: "ADD_PENDING_EMPLOYEES";
  pendingEmployees: EmployeePayrollDTO[];
}

interface DeleteEmployeeAction {
  type: "DELETE_EMPLOYEE";
  employeeId: string;
}
interface ImportPendingEmployeesAction {
  type: "IMPORT_PENDING_EMPLOYEES";
  pendingEmployee: EmployeePayrollDTO;
}

interface ImportPendingEmployeesRequest {
  payrollIds: string[];
  companyId: string;
}

interface EditEmployeeAction {
  type: "EDIT_EMPLOYEE";
  employee: EditEmployeeDTO;
}

type KnownActions =
  | AddEmployeeAction
  | EditEmployeeAction
  | LoadEmployeesAction
  | LoadPendingEmployeesAction
  | AddPendingEmployeesAction
  | DeleteEmployeeAction
  | ImportPendingEmployeesAction
  | ClearStateAction;

const addEmployeeAction = (employee: EmployeeDTO): AddEmployeeAction => ({
  type: "ADD_EMPLOYEE",
  employee,
});

const loadEmployeesAction = (
  employees: EmployeeDTO[]
): LoadEmployeesAction => ({
  type: "LOAD_EMPLOYEES",
  employees,
});

const loadPendingEmployeesAction = (
  pendingEmployees: EmployeePayrollDTO[]
): LoadPendingEmployeesAction => ({
  type: "LOAD_PENDING_EMPLOYEES",
  pendingEmployees,
});

const addPendingEmployeesAction = (
  pendingEmployees: EmployeePayrollDTO[]
): AddPendingEmployeesAction => ({
  type: "ADD_PENDING_EMPLOYEES",
  pendingEmployees,
});

const deleteEmployeeAction = (employeeId: string): DeleteEmployeeAction => ({
  type: "DELETE_EMPLOYEE",
  employeeId,
});

const editEmployeeAction = (employee: EditEmployeeDTO): EditEmployeeAction => ({
  type: "EDIT_EMPLOYEE",
  employee,
});

export const actionCreators = {
  onEmployeeSaved: (
    createRequest: CreateEmployeeForCompanyRequest
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedPost<EmployeeDTO>(
        `companies/${createRequest.companyId}/employees`,
        createRequest
      ).then((employee) => {
        dispatch(addEmployeeAction(employee));

        toast.success(translate("EmployeeSuccessfullyCreated"));
      });
    };
  },
  onEmployeesLoaded: (companyId: string): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedGet<EmployeeDTO[]>(`employees?companyId=${companyId}`).then(
        (employees) => {
          dispatch(loadEmployeesAction(employees));
        }
      );
    };
  },
  onPendingEmployeesLoaded: (
    companyId: string
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedGet<EmployeePayrollDTO[]>(
        `pending-employee-payrolls?companyId=${companyId}`
      ).then((pendingEmployees) => {
        dispatch(loadPendingEmployeesAction(pendingEmployees));
      });
    };
  },
  onPendingEmployeesUpdated: (
    pendingEmployees: EmployeePayrollDTO[]
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(addPendingEmployeesAction(pendingEmployees));
    };
  },
  onEmployeeDelete: (employeeId: string): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedDelete("employees/delete-employee?EmployeeId=", employeeId)
        .then(() => {
          dispatch(deleteEmployeeAction(employeeId));
        })
        .catch((er) => {
          alert(er);
        });
    };
  },
  onEmployeeEdited: (
    employee: EditEmployeeDTO
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(editEmployeeAction(employee));
    };
  },
  onTRZEmployeesImported: (
    selectedIds: string[],
    companyId: string
  ): AppThunk<Promise<void>, KnownActions> => {
    return async (dispatch: any) => {
      const importPendingEmployeesRequest = {
        payrollIds: selectedIds,
        companyId: companyId,
      } as ImportPendingEmployeesRequest;

      return authenticatedPost<LightPayrollDTO[]>(
        "pending-employee-payrolls/import",
        importPendingEmployeesRequest
      )
        .then((payrolls: LightPayrollDTO[]) => {
          dispatch(onPendingEmployeesLoaded(companyId));
          toast.success(translate("EmployeeSuccessfullyCreated"));
        })
        .catch((error) => {
          console.error(error);
          toast.error(translate("ErrorImportingEmployees"));
        });
    };
  },
};
export const {
  onEmployeeSaved,
  onEmployeeEdited,
  onEmployeesLoaded,
  onPendingEmployeesLoaded,
  onPendingEmployeesUpdated,
  onEmployeeDelete,
  onTRZEmployeesImported,
} = actionCreators;

const initialState = {
  employees: [],
  pendingEmployees: [],
} as EmployeesState;

export const reducer: Reducer<EmployeesState> = (
  state = initialState,
  action: Action
) => {
  var incomingAction = action as KnownActions;
  switch (incomingAction.type) {
    case "ADD_EMPLOYEE":
      return {
        ...state,
        employees: [...state.employees, incomingAction.employee],
      };
    case "LOAD_EMPLOYEES":
      return {
        ...state,
        employees: [...incomingAction.employees],
      };
    case "LOAD_PENDING_EMPLOYEES":
      return {
        ...state,
        pendingEmployees: [...incomingAction.pendingEmployees],
      };
    case "ADD_PENDING_EMPLOYEES":
      const newEmployees = incomingAction.pendingEmployees.filter(
        (incomingEmployee) =>
          !state.pendingEmployees.some(
            (existingEmployee) =>
              existingEmployee.payrollId === incomingEmployee.payrollId
          )
      );
      return {
        ...state,
        pendingEmployees: [...state.pendingEmployees, ...newEmployees],
      };
    case "EDIT_EMPLOYEE":
      const employee = incomingAction.employee;
      const updatedEmployees = state.employees.map((e) =>
        e.id === employee.employeeId ? { ...e, ...employee } : e
      );
      return {
        ...state,
        employees: updatedEmployees,
      };
    case "DELETE_EMPLOYEE":
      const employeeId = incomingAction.employeeId;
      return {
        ...state,
        employees: [
          ...state.employees.filter((p) => p.workTimeId !== employeeId),
        ],
      };
    case "IMPORT_PENDING_EMPLOYEES":
      var pendingEmployeeId = incomingAction.pendingEmployee.payrollId;
      return {
        ...state,
        pendingEmployees: [
          ...state.pendingEmployees.filter(
            (e) => e.payrollId !== pendingEmployeeId
          ),
        ],
      };
    case "CLEAR_STATE":
      return initialState;
    default:
      return state;
  }
};

export const selectEmployees = (state: RootState) => state.employees;
