import { AbsenceHospitalDTO } from "../absence/AbsenceHospitalDTO";
import { LightEmployeeDTO } from "../employees/LightEmployeeDTO";
import { NomenclatureDTO } from "../nomenclatures/NomenclatureDTO";

// Тук е важно да няма данни за заплати и по-конфиденциални данни,
// защото се зареждат и от служители без права.
export interface LightPayrollDTO {
  id: string;
  employee: LightEmployeeDTO;
  companyId: string;
  contractNumber: string;
  position: NomenclatureDTO;
  structureLevelId: string;
  contractType: number;
  leaves: AbsenceHospitalDTO[];
  workTimeId: string; // Това Id трябва да е равно на id, за да работи коректно
}

export const createLightPayrollDTO = (
  data: Omit<LightPayrollDTO, "workTimeId">
): LightPayrollDTO => {
  return {
    ...data,
    workTimeId: data.id,
  };
};
