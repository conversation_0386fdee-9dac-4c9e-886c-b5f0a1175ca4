import { render, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import DatesElement from "../DateElement";
import { ViewMode } from "../../../models/Enums/ViewMode";
import { LanguageContext } from "../../../services/language/LanguageContext";
import {
  AvailableLanguages,
  DictionaryList,
  LanguageOptions,
} from "../../../services/language/constants";

const renderWithMockedContext = (
  component: React.ReactNode | React.ReactNode[]
) => {
  const provider = {
    userLanguage: "en" as AvailableLanguages,
    dictionary: DictionaryList["en"],
    userLanguageChange: (selected: AvailableLanguages) => {
      const newLanguage = LanguageOptions[selected] ? selected : "en";
    },
  };

  return render(
    <LanguageContext.Provider value={provider}>
      {component}
    </LanguageContext.Provider>
  );
};

describe("DatesElement", () => {
  it("renders days of the week", () => {
    const { getByText } = render(
      <DatesElement
        selectedDay={15}
        selectedMonth={6}
        selectedYear={2023}
        handleDateClick={() => {}}
        active={ViewMode.DatesView}
      />
    );

    expect(getByText("Пон")).toBeInTheDocument();
    expect(getByText("Вт")).toBeInTheDocument();
    expect(getByText("Ср")).toBeInTheDocument();
    expect(getByText("Чет")).toBeInTheDocument();
    expect(getByText("Пет")).toBeInTheDocument();
    expect(getByText("Съб")).toBeInTheDocument();
    expect(getByText("Нед")).toBeInTheDocument();
  });

  it("renders days of the week", () => {
    const { getByText } = renderWithMockedContext(
      <DatesElement
        selectedDay={15}
        selectedMonth={6}
        selectedYear={2023}
        handleDateClick={() => {}}
        active={ViewMode.DatesView}
      />
    );

    expect(getByText("Mon")).toBeInTheDocument();
    expect(getByText("Tue")).toBeInTheDocument();
    expect(getByText("Wed")).toBeInTheDocument();
    expect(getByText("Thu")).toBeInTheDocument();
    expect(getByText("Fri")).toBeInTheDocument();
    expect(getByText("Sat")).toBeInTheDocument();
    expect(getByText("Sun")).toBeInTheDocument();
  });

  it("renders the correct number of days for July 2023", () => {
    const { getAllByText } = render(
      <DatesElement
        selectedDay={15}
        selectedMonth={6}
        selectedYear={2023}
        handleDateClick={() => {}}
        active={ViewMode.DatesView}
      />
    );

    expect(getAllByText("1")).toHaveLength(2);
    expect(getAllByText("31")).toHaveLength(2);
  });

  it("invokes handleDateClick callback when a day is clicked", () => {
    const handleDateClickMock = jest.fn();

    const { getByText } = render(
      <DatesElement
        selectedDay={15}
        selectedMonth={6}
        selectedYear={2023}
        handleDateClick={handleDateClickMock}
        active={ViewMode.DatesView}
      />
    );

    fireEvent.click(getByText("15"));
    expect(handleDateClickMock).toHaveBeenCalledWith(15);
  });
});
