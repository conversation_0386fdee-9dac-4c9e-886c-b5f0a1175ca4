import React from "react";
import styled from "styled-components";
import Label from "../Inputs/Label";
import { getMonthWorkingDays } from "../../services/calendar/calendarService";
import { translate } from "../../services/language/Translator";

interface NonWorkingDaysInfoProps {
  selectedMonth: number;
  selectedYear: number;
}

interface MonthInfo {
  nonWorkingDays: number;
  officialHolidays: number;
}

const NonWorkingDaysContainer = styled.div`
  display: flex;
  position: absolute;
  flex-direction: column;
  right: 2.5em;
  top: 1em;
`;

const InfoRow = styled.div`
  display: flex;
  align-items: left;
  gap: 0.5em;
  max-height: 1em;
  padding: 0 1em;
  margin-bottom: 0.5em;
`;

const NumberContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5em;
`;

const NumberText = styled(Label)`
  font-size: 0.7rem;
  color: #ffffff;
  min-width: 1.5em;
  text-align: right;
`;

const LabelText = styled(Label)`
  font-size: 0.7rem;
  color: #a8ccf2;
`;

const NonWorkingDaysInfo: React.FC<NonWorkingDaysInfoProps> = ({
  selectedMonth,
  selectedYear,
}) => {
  const getMonthInfo = (month: number, year: number): MonthInfo => {
    const workingDays = getMonthWorkingDays(month, year);
    const totalDays = new Date(year, month + 1, 0).getDate();

    // TODO: Implement official holidays calculation
    // This should fetch holidays from an API or use a holidays library
    // For now, setting to 0 as requested
    const officialHolidays = 0;

    const nonWorkingDays = totalDays - workingDays - officialHolidays;

    return {
      nonWorkingDays,
      officialHolidays,
    };
  };

  const monthInfo = getMonthInfo(selectedMonth, selectedYear);

  return (
    <NonWorkingDaysContainer>
      <InfoRow>
        <NumberContainer>
          <NumberText>{monthInfo.nonWorkingDays.toString()}</NumberText>
          <LabelText>{translate("Non-working days")}</LabelText>
        </NumberContainer>
      </InfoRow>
      <InfoRow>
        <NumberContainer>
          <NumberText>{monthInfo.officialHolidays.toString()}</NumberText>
          <LabelText>{translate("Official holidays")}</LabelText>
        </NumberContainer>
      </InfoRow>
    </NonWorkingDaysContainer>
  );
};

export default NonWorkingDaysInfo;
