import React from "react";
import styled from "styled-components";

interface FieldsetProps
  extends React.DetailedHTMLProps<
    React.FieldsetHTMLAttributes<HTMLFieldSetElement>,
    HTMLFieldSetElement
  > {
  children?: React.ReactNode | React.ReactNode[];
}

const StyledFieldset = styled.fieldset`
  border-radius: 2rem;
  padding: 0rem;
`;

const Fieldset: React.FC<FieldsetProps> = (props) => {
  const { children } = props;
  return (
    <StyledFieldset data-testid="fieldset" {...props}>
      {children}
    </StyledFieldset>
  );
};

export default Fieldset;
