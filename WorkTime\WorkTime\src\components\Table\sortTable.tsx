import { Direction } from "./Direction";

const sortColumns = (data: any, sortedField: string, direction: Direction) => {
  const sortedPayrolls = [...data];

  if (sortedField != null) {
    sortedPayrolls.sort((a, b) => {
      if ((a as any)[sortedField] === (b as any)[sortedField]) {
        return 0;
      }

      if ((a as any)[sortedField] > (b as any)[sortedField]) {
        return direction === Direction.Ascending ? 1 : -1;
      } else {
        return direction === Direction.Ascending ? -1 : 1;
      }
    });
  }

  return sortedPayrolls;
};

export default sortColumns;
