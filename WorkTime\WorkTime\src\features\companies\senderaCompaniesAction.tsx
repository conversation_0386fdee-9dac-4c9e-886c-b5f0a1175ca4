import { toast } from "react-toastify";
import { Action, Reducer } from "redux";
import { AppThunk, ClearStateAction, RootState } from "../../app/store";
import { CompanyDTO } from "../../models/DTOs/companies/CompanyDTO";
import { SenderaCompanyDTO } from "../../models/DTOs/companies/SenderaCompanyDTO";
import { authenticatedGet } from "../../services/connectionService";
import { translate } from "../../services/language/Translator";

interface SenderaCompaniesState {
  senderaCompanies: SenderaCompanyDTO[];
}

interface GetSenderaCompaniesAction {
  type: "GET_SENDERA_COMPANIES";
  senderaCompanies: SenderaCompanyDTO[];
}

interface ImportSenderaCompanyAction {
  type: "IMPORT_SENDERA_COMPANY_ACTION";
  company: CompanyDTO;
}

type KnownActions =
  | GetSenderaCompaniesAction
  | ImportSenderaCompanyAction
  | ClearStateAction;

const getSenderaCompaniesAction = (
  senderaCompanies: SenderaCompanyDTO[]
): GetSenderaCompaniesAction => ({
  type: "GET_SENDERA_COMPANIES",
  senderaCompanies,
});

const importSenderaCompanyAction = (
  company: CompanyDTO
): ImportSenderaCompanyAction => ({
  type: "IMPORT_SENDERA_COMPANY_ACTION",
  company,
});

export const actionCreators = {
  onGetSenderaCompanies: (): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedGet<SenderaCompanyDTO[]>(`sendera-companies`)
        .then((senderaCompanies) => {
          dispatch(getSenderaCompaniesAction(senderaCompanies));
        })
        .catch((error) => {
          console.error(error);
        });
    };
  },
  onImportSenderaCompany: (
    company: CompanyDTO
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(importSenderaCompanyAction(company));
      toast.success(
        translate("Company {0} was successfully added", company.name)
      );
    };
  },
};

export const { onGetSenderaCompanies, onImportSenderaCompany } = actionCreators;
const initialState = {
  senderaCompanies: [],
} as SenderaCompaniesState;

export const reducer: Reducer<SenderaCompaniesState> = (
  state = initialState,
  action: Action
) => {
  var incomingAction = action as KnownActions;
  switch (incomingAction.type) {
    case "GET_SENDERA_COMPANIES":
      return {
        ...state,
        senderaCompanies: [...incomingAction.senderaCompanies],
      };
    case "IMPORT_SENDERA_COMPANY_ACTION":
      const companyId = incomingAction.company.userRegistrationCompanyId;
      return {
        ...state,
        senderaCompanies: [
          ...state.senderaCompanies.filter((c) => c.id !== companyId),
        ],
      };
    case "CLEAR_STATE":
      return initialState;
    default:
      return state;
  }
};

export const senderaCompanies = (state: RootState) => state.senderaCompanies;
