import React, { useState } from "react";
import styled from "styled-components";
import Container from "../Container";
import Label from "../Inputs/Label";

import sortUpImage from "../../assets/images/employees-sort/sortUp.png";
import sortUpHover from "../../assets/images/employees-sort/sortUpHover.png";
import sortDownImage from "../../assets/images/employees-sort/sortDown.png";
import sortDownHover from "../../assets/images/employees-sort/sortDownHover.png";
import sortDefault from "../../assets/images/employees-sort/sortDefault.png";
import sortHover from "../../assets/images/employees-sort/sortHover.png";

import { ColumnDefinitionType } from "./Table";
import { Direction } from "./Direction";

const SecondaryHeaderWrapper = styled(Container)`
  display: flex;
  height: 2.5rem;
  margin-top: 1.2rem;
  justify-content: space-between;
  align-items: center;
  background-color: var(--secondary-header-background-color);
  border-bottom: 0.01rem solid var(--table-row-bottom-line-color);
`;

const ColumnWrapper = styled.button<{ isActive?: boolean }>`
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0.5rem 0rem;
  background: none;
  border: none;
`;

const TableCell = styled.td<{ children?: React.ReactNode }>`
  font-size: 0.875rem;
  color: var(--table-cell-color);
`;

const HeaderRow = styled.tr`
  display: grid;
  width: 100%;
  grid-template-columns: 0.1fr 1.5fr 1fr 1fr 1fr 1fr 0.4fr;
`;

const ColumnTitle = styled(Label)`
  font-size: 1rem;
  font-family: Inter;
  font-weight: 400;
  color: var(--text-employees-secondary-header-color);
  cursor: pointer;
  user-select: none;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const SortImage = styled.span<{ image: string }>`
  background-image: url(${(props) => props.image});
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  margin: 0rem 0.625rem;
  width: 0.875rem;
  height: 1.3125rem;
  display: inline-block;
  vertical-align: middle;
`;

interface SecondaryHeaderProps<T, K extends keyof T> {
  columns: Array<ColumnDefinitionType<T, K>>;
  onSortChange: (column: string, direction: Direction) => void;
}

const getSortImage = ({
  isActive,
  hovered,
  direction,
}: {
  isActive: boolean;
  hovered: boolean;
  direction?: Direction;
}) => {
  if (isActive && direction === Direction.Descending && hovered)
    return sortDownHover;
  if (isActive && direction === Direction.Descending) return sortDownImage;
  if (isActive && direction === Direction.Ascending && hovered)
    return sortUpHover;
  if (isActive && direction === Direction.Ascending) return sortUpImage;
  if (hovered) return sortHover;
  return sortDefault;
};

const SecondaryHeader = <T, K extends keyof T>({
  columns,
  onSortChange,
}: SecondaryHeaderProps<T, K>): JSX.Element => {
  const [activeColumn, setActiveColumn] = useState<string | null>(null);
  const [direction, setDirection] = useState<Direction>(Direction.Ascending);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  const handleSort = (columnKey: string) => {
    const newDirection =
      activeColumn === columnKey && direction === Direction.Ascending
        ? Direction.Descending
        : Direction.Ascending;

    setDirection(newDirection);
    setActiveColumn(columnKey);
    onSortChange?.(columnKey, newDirection);
  };

  return (
    <SecondaryHeaderWrapper>
      <HeaderRow data-testid="secondary-header-row">
        {columns.map((column, index) => (
          <TableCell
            key={`headCell-${index}`}
            data-testid={`table-header-cell-${index}`}
          >
            {index === 0 ? null : (
              <ColumnWrapper
                data-testid={`column-wrapper-${index}`}
                isActive={activeColumn === column.key.toString()}
                onClick={() => handleSort(column.key.toString())}
                onMouseEnter={() => setHoveredIndex(index)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <SortImage
                  image={getSortImage({
                    isActive: activeColumn === column.key.toString(),
                    hovered: hoveredIndex === index,
                    direction:
                      activeColumn === column.key.toString()
                        ? direction
                        : undefined,
                  })}
                />
                <ColumnTitle uppercase={true}>{column.value}</ColumnTitle>
              </ColumnWrapper>
            )}
          </TableCell>
        ))}
      </HeaderRow>
    </SecondaryHeaderWrapper>
  );
};

export default SecondaryHeader;
