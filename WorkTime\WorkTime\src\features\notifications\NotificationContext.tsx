import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useRef,
  useEffect,
} from "react";

import {
  selectNotifications,
  onMarkNotificationAsRead,
  onAllUserNotificationsLoaded,
} from "./notificationsActions";
import { useAppSelector, useAppDispatch } from "../../app/hooks";
import { useUserEmployee } from "../UserEmployeeContext";

interface NotificationContextType {
  isNotificationsOpen: boolean;
  toggleNotifications: () => boolean;
  closeNotifications: () => void;
  notificationDropdownRef: React.RefObject<HTMLDivElement>;
  unseenCount: number;
  markAsRead: (notificationId: string) => void;
}

const NotificationContext = createContext<NotificationContextType>(
  {} as NotificationContextType
);

export const NotificationProvider = ({ children }: { children: ReactNode }) => {
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const notificationDropdownRef = useRef<HTMLDivElement>(null);
  const dispatch = useAppDispatch();
  const { userEmployee } = useUserEmployee();
  const toggleNotifications = () => {
    const newState = !isNotificationsOpen;

    setTimeout(() => setIsNotificationsOpen(newState), 10);
    return newState;
  };
  const closeNotifications = () => setIsNotificationsOpen(false);
  const allNotifications = useAppSelector(selectNotifications).notifications;
  const unseenCount = allNotifications.filter((n) => !n.isRead).length;

  const markAsRead = (notificationId: string) => {
    dispatch(onMarkNotificationAsRead(notificationId));
  };

  useEffect(() => {
    if (userEmployee && userEmployee.employeeId) {
      dispatch(onAllUserNotificationsLoaded(userEmployee.userId));
    }
  }, [dispatch, userEmployee?.userId, userEmployee]);

  useEffect(() => {
    if (!isNotificationsOpen) return;
    const handleClickOutside = (event: MouseEvent) => {
      if (
        notificationDropdownRef.current &&
        !notificationDropdownRef.current.contains(event.target as Node)
      ) {
        closeNotifications();
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [isNotificationsOpen]);

  return (
    <NotificationContext.Provider
      value={{
        isNotificationsOpen,
        toggleNotifications,
        closeNotifications,
        notificationDropdownRef,
        unseenCount,
        markAsRead,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotificationDropdown = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error(
      "useNotificationDropdown must be used within a NotificationProvider"
    );
  }
  return context;
};
