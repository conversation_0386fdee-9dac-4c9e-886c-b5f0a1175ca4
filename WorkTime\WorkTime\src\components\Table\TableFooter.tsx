import React, { Dispatch, SetStateAction, useEffect } from "react";
import { IEntity } from "../../models/DTOs/IEntity";
import styled from "styled-components";
import Translator from "../../services/language/Translator";

interface TableFooterProps<T>
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  range: Array<number>;
  page: Number | any;
  slice: Array<T>;
  setPage: Dispatch<SetStateAction<number>>;
  setCurrentPage: (page: number) => void;
}

const TableFooterDiv = styled.div`
  text-align: center;
  background-color: var(--table-footer-color);
  color: var(--table-footer-color);
  padding: 0.938rem 0.5rem 0.5rem 0.5rem;
  opacity: 1;
  font-family: segoe-ui;
  font-style: normal;
  display: block;
  border-bottom-left-radius: 1.6rem;
  border-bottom-right-radius: 1.6rem;
`;

const FooterLabel = styled.label`
  color: var(--footer-label-color);
  float: right;
  padding-right: 1.25rem;
  opacity: 1;
  font-family: Segoe UI;
  font-size: 0.75rem;
`;

const PageButton = styled.button`
  color: var(--page-button-color);
  background-color: var(--page-button-backgroundColor);
  font-size: 0.75rem;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  border-radius: 0.188rem;
`;

const PageButtonClicked = styled.button<{
  isCliked: boolean;
}>`
  color: ${(p) =>
    p.isCliked
      ? "var(--page-number-button-color)"
      : "var(--page-button-color)"};
  background-color: ${(p) =>
    p.isCliked
      ? "var(--page-number-button-clicked-color)"
      : "var(--page-button-backgroundColor)"};
  font-size: ${(p) => (p.isCliked ? "0.75rem" : "0.625rem")};
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  border-radius: 0.188rem;
`;

const TableFooter = <T extends IEntity>(
  props: TableFooterProps<T>
): JSX.Element => {
  const { range, page, setPage, slice, setCurrentPage } = props;

  useEffect(() => {
    if (slice.length < 1 && page !== 1) {
      setPage(page - 1);
    }
  }, [slice, page, setPage]);

  return (
    <TableFooterDiv>
      <PageButton
        disabled={page === 1}
        onClick={() => setCurrentPage(page - 1)}
        data-testid="table-footer-previous-button"
      >
        <Translator getString="Previous" />
      </PageButton>
      {range.map((pageNumber, index) => (
        <PageButtonClicked
          key={index}
          isCliked={page === pageNumber}
          onClick={() => setCurrentPage(pageNumber)}
          data-testid={`table-footer-page-button-${pageNumber}`}
        >
          {pageNumber}
        </PageButtonClicked>
      ))}
      <PageButton
        disabled={page === range.length}
        onClick={() => setCurrentPage(page + 1)}
        data-testid="table-footer-next-button"
      >
        <Translator getString="Next" />
      </PageButton>
      <FooterLabel data-testid="table-footer-label">
        {slice.length} <Translator getString="EmployeesTable" />
      </FooterLabel>
    </TableFooterDiv>
  );
};

export default TableFooter;
