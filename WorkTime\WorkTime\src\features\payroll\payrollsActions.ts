import { Action, Reducer } from "redux";
import { AppThunk, ClearStateAction, RootState } from "../../app/store";
import { ChangeCompanyAction } from "../companies/companiesActions";
import {
  createLightPayrollDTO,
  LightPayrollDTO,
} from "../../models/DTOs/payrolls/LightPayrollDTO";
import { AbsenceHospitalDTO } from "../../models/DTOs/absence/AbsenceHospitalDTO";
import { AbsenceStatus } from "../../models/DTOs/absence/AbsenceStatus";
import { authenticatedGet } from "../../services/worktimeConnectionService";

interface PayrollsState {
  payrolls: LightPayrollDTO[];
}

interface LoadPayrollsAction {
  type: "LOAD_PAYROLLS";
  payrolls: LightPayrollDTO[];
}

interface AddPayrollsAction {
  type: "ADD_PAYROLLS";
  payrolls: LightPayrollDTO[];
}

interface UpdatePayrollAbsenceAction {
  type: "UPDATE_PAYROLL_ABSENCE";
  absence: AbsenceHospitalDTO;
}

interface AddPayrollAbsencesAction {
  type: "ADD_PAYROLL_ABSENCES";
  absences: AbsenceHospitalDTO[];
}

interface AddPayrollAbsenceAction {
  type: "ADD_PAYROLL_ABSENCE";
  absence: AbsenceHospitalDTO;
}

interface RemovePayrollAbsenceAction {
  type: "REMOVE_PAYROLL_ABSENCE";
  absenceId: string;
}

type KnownActions =
  | LoadPayrollsAction
  | AddPayrollsAction
  | UpdatePayrollAbsenceAction
  | AddPayrollAbsenceAction
  | AddPayrollAbsencesAction
  | RemovePayrollAbsenceAction
  | ClearStateAction
  | ChangeCompanyAction;

export const loadPayrollsAction = (
  payrolls: LightPayrollDTO[]
): LoadPayrollsAction => ({
  type: "LOAD_PAYROLLS",
  payrolls,
});

const addPayrollsAction = (payrolls: LightPayrollDTO[]): AddPayrollsAction => ({
  type: "ADD_PAYROLLS",
  payrolls,
});

const updatePayrollAbsenceAction = (
  absence: AbsenceHospitalDTO
): UpdatePayrollAbsenceAction => ({
  type: "UPDATE_PAYROLL_ABSENCE",
  absence,
});

const addPayrollAbsencesAction = (
  absences: AbsenceHospitalDTO[]
): AddPayrollAbsencesAction => ({
  type: "ADD_PAYROLL_ABSENCES",
  absences,
});
const addPayrollAbsenceAction = (
  absence: AbsenceHospitalDTO
): AddPayrollAbsenceAction => ({
  type: "ADD_PAYROLL_ABSENCE",
  absence,
});

const removePayrollAbsenceAction = (
  absenceId: string
): RemovePayrollAbsenceAction => ({
  type: "REMOVE_PAYROLL_ABSENCE",
  absenceId,
});

export const actionCreators = {
  onPayrollsLoaded: (companyId: string): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      if (!companyId) return;

      authenticatedGet<LightPayrollDTO[]>(
        `${companyId}/payrolls/light/load`
      ).then((payrolls) => {
        dispatch(loadPayrollsAction(payrolls.map(createLightPayrollDTO)));
      });
    };
  },
  onPayrollsAdded: (
    payrolls: LightPayrollDTO[]
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(addPayrollsAction(payrolls.map(createLightPayrollDTO)));
    };
  },
  onPayrollAbsenceAdded: (
    absence: AbsenceHospitalDTO
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(addPayrollAbsenceAction(absence));
    };
  },
  onPayrollAbsencesAdded: (
    absences: AbsenceHospitalDTO[]
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(addPayrollAbsencesAction(absences));
    };
  },
  onPayrollAbsenceUpdated: (
    absence: AbsenceHospitalDTO
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(updatePayrollAbsenceAction(absence));
    };
  },
  onPayrollAbsenceRemoved: (
    absenceId: string
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(removePayrollAbsenceAction(absenceId));
    };
  },
};
export const {
  onPayrollsLoaded,
  onPayrollsAdded,
  onPayrollAbsenceAdded,
  onPayrollAbsencesAdded,
  onPayrollAbsenceUpdated,
  onPayrollAbsenceRemoved,
} = actionCreators;

const initialState = {
  payrolls: [],
} as PayrollsState;

export const reducer: Reducer<PayrollsState> = (
  state = initialState,
  action: Action
) => {
  var incomingAction = action as KnownActions;
  switch (incomingAction.type) {
    case "LOAD_PAYROLLS":
      return {
        ...state,
        payrolls: [...incomingAction.payrolls],
      };
    case "ADD_PAYROLLS": {
      const addAction = incomingAction as AddPayrollsAction;
      const newPayrolls = Array.isArray(addAction.payrolls)
        ? addAction.payrolls
        : [];

      return {
        ...state,
        payrolls: newPayrolls.reduce((currentPayrolls, newPayroll) => {
          const existingPayroll = currentPayrolls.find(
            (payroll) => payroll.id === newPayroll.id
          );

          if (existingPayroll === undefined) {
            // Payroll doesn't exist, add it with all its leaves
            return [...currentPayrolls, newPayroll];
          } else {
            // Payroll exists, merge leaves
            const mergedLeaves = newPayroll.leaves.reduce(
              (leaves: AbsenceHospitalDTO[], newLeave: AbsenceHospitalDTO) => {
                const existingLeave = leaves.find(
                  (leave: AbsenceHospitalDTO) => leave.id === newLeave.id
                );

                if (existingLeave === undefined) {
                  // Leave doesn't exist, add it
                  return [...leaves, newLeave];
                } else {
                  // Leave exists, update it
                  return leaves.map((leave: AbsenceHospitalDTO) =>
                    leave.id === existingLeave.id ? newLeave : leave
                  );
                }
              },
              existingPayroll.leaves
            );

            // Update the existing payroll with merged leaves
            const resultPayrolls = [...currentPayrolls];
            const payroll = resultPayrolls.find(
              (payroll) => payroll.id === newPayroll.id
            );
            if (payroll !== undefined) {
              payroll.leaves = mergedLeaves;
            }
            return resultPayrolls;
          }
        }, state.payrolls),
      };
    }
    case "ADD_PAYROLL_ABSENCE": {
      const addAction = incomingAction as AddPayrollAbsenceAction;
      return {
        ...state,
        payrolls: state.payrolls.map((payroll) =>
          payroll.id === addAction.absence.payrollId
            ? {
                ...payroll,
                leaves: [
                  ...payroll.leaves.filter(
                    (leave) => leave.id !== addAction.absence.id
                  ),
                  addAction.absence,
                ],
              }
            : payroll
        ),
      };
    }
    case "ADD_PAYROLL_ABSENCES": {
      const addAction = incomingAction as AddPayrollAbsencesAction;
      return {
        ...state,
        payrolls: state.payrolls.map((payroll) => ({
          ...payroll,
          leaves: [
            ...payroll.leaves,
            ...addAction.absences.filter(
              (absence) => absence.payrollId === payroll.id
            ),
          ],
        })),
      };
    }
    case "UPDATE_PAYROLL_ABSENCE": {
      const updateAction = incomingAction as UpdatePayrollAbsenceAction;
      if (
        updateAction.absence.status === AbsenceStatus.Declined ||
        updateAction.absence.status === AbsenceStatus.Deleted ||
        updateAction.absence.status === AbsenceStatus.DeletedByAdmin
      ) {
        return {
          ...state,
          payrolls: state.payrolls.map((payroll) => ({
            ...payroll,
            leaves: payroll.leaves.filter(
              (leave) => leave.id !== updateAction.absence.id
            ),
          })),
        };
      }
      return {
        ...state,
        payrolls: state.payrolls.map((payroll) =>
          payroll.id === updateAction.absence.payrollId
            ? {
                ...payroll,
                leaves: [
                  ...payroll.leaves.filter(
                    (leave) => leave.id !== updateAction.absence.id
                  ),
                  updateAction.absence,
                ],
              }
            : payroll
        ),
      };
    }
    case "REMOVE_PAYROLL_ABSENCE": {
      const removeAction = incomingAction as RemovePayrollAbsenceAction;
      return {
        ...state,
        payrolls: state.payrolls.map((payroll) => ({
          ...payroll,
          leaves: payroll.leaves.filter(
            (leave) => leave.id !== removeAction.absenceId
          ),
        })),
      };
    }
    case "CHANGE_COMPANY": {
      return {
        ...state,
        payrolls: [],
      };
    }
    case "CLEAR_STATE":
      return initialState;
    default:
      return state;
  }
};

export const selectPayrolls = (state: RootState) => state.payrolls;
