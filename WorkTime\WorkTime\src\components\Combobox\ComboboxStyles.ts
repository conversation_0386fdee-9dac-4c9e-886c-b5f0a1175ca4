import styled from "styled-components";
import Container from "../Container";
import Image from "../Image";

export interface StyledProps {
  isOpen: boolean;
  height?: string;
  width?: string;
}

export const ComboboxContainer = styled.div`
  position: relative;
  display: block;
  align-items: center;
  margin: 0.45rem 0rem;
  min-width: 10rem;
  user-select: none;
`;

export const SelectedDisplay = styled(Container)<{ isOpen: boolean }>`
  display: flex;
  position: relative;
  padding: 0.8rem;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  border-radius: 1.625rem;
  border: 0.063rem;
  background-color: var(--combobox-display-background-color);
  cursor: pointer;
  color: var(--combobox-header-text-color);
  font-family: Segoe UI;
  font-weight: 400;
  min-width: 8rem;
  min-height: 2.9rem;
  overflow: hidden;
  z-index: 10;
  transition: border-radius 0.5s ease;
  box-shadow: -0.05rem -0.05rem 0.05rem var(--comboboxShadow),
    0.05rem -0.05rem 0.05rem var(--comboboxShadow),
    0rem -0.05rem 0.05rem var(--comboboxShadow);

  ${({ isOpen }) =>
    isOpen &&
    `
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  `}
`;

export const TriangularArrowContainer = styled(Image)<{ isHovered: boolean }>`
  width: 0.65rem;
  height: 0.65rem;
  opacity: 0.5;
  transition: all 0.2s ease;
`;

export const DropdownList = styled(Container)<{ isOpen: boolean }>`
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  grid-column: 2/2;
  top: 85%;
  max-height: 30rem;
  min-height: 5rem;
  padding: 0.6rem;
  background-color: var(--combobox-dropdown-background-color);
  opacity: ${({ isOpen }) => (isOpen ? "1" : "0")};
  visibility: ${({ isOpen }) => (isOpen ? "visible" : "hidden")};
  transition: all 0.3s ease-in-out;
  z-index: 9;
  overflow-y: auto;
  overflow-x: auto;
  border-bottom-left-radius: 2rem;
  border-bottom-right-radius: 2rem;
  box-shadow: -0.05rem 0.05rem 0.05rem var(--comboboxShadow),
    0.05rem 0.05rem 0.05rem var(--comboboxShadow),
    0px 0.05rem 0.05rem var(--comboboxShadow);
  white-space: nowrap;
`;

export const ScrollableWrapper = styled(Container)`
  box-sizing: content-box;
  overflow-y: auto;
  scrollbar-width: 0.5rem;
  padding-top: 0.1rem;
  max-height: 20rem;
  min-height: 5rem;
  scrollbar-color: var(--scrollbar-hover) var(--scrollbar-background);
`;

export const OptionItem = styled(Container)<{ level?: number }>`
  display: flex;
  position: relative;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  z-index: 9;
  font-family: segoe-ui;
  font-weight: normal;
  padding: 0.2rem 0.5rem;
  color: var(--combobox-header-text-color);
  gap: 0.5rem;
  margin-left: ${({ level = 0 }) => level * 1.5}rem;
`;

export const CheckboxContainer = styled(Container)`
  display: flex;
  align-items: flex-start;
  padding-top: 0.1rem;
  flex-shrink: 0;
`;

export const OptionText = styled.span`
  flex: 1;
  text-align: left;
  white-space: nowrap;
  text-overflow: ellipsis;
`;

export const CheckmarkImage = styled(Image)<{ isHovered?: boolean }>`
  position: absolute;
  left: 35%;
  top: 30%;
  transform: translate(-25%, -50%) scale(1.2);
  width: 1.1rem;
  height: 1rem;
  transition: all 0.2s ease;
  pointer-events: none;
`;

export const RoundCheckbox = styled(Container)<{
  checked: boolean;
  isLast?: boolean;
}>`
  position: relative;
  width: 1rem;
  height: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: visible;
  user-select: none;
  ${({ isLast }) =>
    isLast &&
    `
   margin-left: 1.5rem;
  `}
`;

export const SelectedCount = styled.span`
  color: var(--combobox-header-text-color);
  font-size: 0.9em;
  margin-right: 1rem;
  user-select: none;
`;

export const MarkerIcon = styled(Image)`
  width: 1rem;
  height: 1rem;
  cursor: pointer;
  margin-right: 0.5rem;
`;
