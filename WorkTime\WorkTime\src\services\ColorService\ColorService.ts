export class ColorService {
  /**
   * Converts a HEX color string to an RGB color string.
   * @param hex The HEX color string.
   * @returns The RGB color string.
   */
  public static hexToRgb(hex: string): string {
    let r = 0,
      g = 0,
      b = 0;

    hex = hex.toLowerCase();

    if (hex.length === 4) {
      r = parseInt(hex[1] + hex[1], 16);
      g = parseInt(hex[2] + hex[2], 16);
      b = parseInt(hex[3] + hex[3], 16);
    } else if (hex.length === 7) {
      r = parseInt(hex[1] + hex[2], 16);
      g = parseInt(hex[3] + hex[4], 16);
      b = parseInt(hex[5] + hex[6], 16);
    }
    return `${r},${g},${b}`;
  }
}
