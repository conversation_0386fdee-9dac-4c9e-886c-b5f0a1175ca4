import styled from "styled-components";
import UserNavMenu from "./authentication/UserNavMenu";
import LanguageSelector from "./LanguageSelector";
import MainWindow from "./MainWindow";
import { Link, useLocation } from "react-router-dom";
import { useEffect, useRef, useState } from "react";
import { useMenu } from "./MenuContext";
import { useAuth } from "./authentication/AuthContext";
import { useModal } from "../components/PopUp/ActionModalContext";
import Logo from "../assets/images/logos/Logo.png";
import Container from "../components/Container";

const NavContainer = styled(Container)`
  display: flex;
  flex: 0 1 auto;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.7rem 0;
  background-color: var(--app-header-color);
`;

const NavigationLink = styled(Link)`
  text-decoration: none;
  margin-left: 1.5rem;
`;

const AppLabel = styled.label`
  letter-spacing: var(--unnamed-character-spacing-0);
  color: var(--app-label-color);
  font-size: 2rem;
  font-weight: Montserrat;
  letter-spacing: 0;

  &:hover {
    cursor: pointer;
  }
`;

const Layout = () => {
  const menuRef = useRef<HTMLDivElement | null>(null);
  const location = useLocation();
  const { isOpen, toggleMenu, closeMenu } = useMenu();
  const mouseDownInsideMenuRef = useRef(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const { user } = useAuth();
  const { isModalOpen } = useModal();

  useEffect(() => {
    setIsAuthenticated(user.email ? true : false);
  }, [user, isOpen]);

  useEffect(() => {
    const handleMouseDown = (event: MouseEvent) => {
      mouseDownInsideMenuRef.current = !!(
        menuRef.current && menuRef.current.contains(event.target as Node)
      );
    };

    const handleMouseUp = (event: MouseEvent) => {
      const mouseUpOutside = !!(
        menuRef.current && !menuRef.current.contains(event.target as Node)
      );
      if (mouseUpOutside && !mouseDownInsideMenuRef.current && !isModalOpen) {
        closeMenu();
      }

      mouseDownInsideMenuRef.current = false;
    };

    window.addEventListener("mousedown", handleMouseDown);
    window.addEventListener("mouseup", handleMouseUp);
    return () => {
      window.removeEventListener("mousedown", handleMouseDown);
      window.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isModalOpen, closeMenu]);

  return (
    <>
      {isAuthenticated || location.pathname !== "/" ? (
        <NavContainer data-testid="nav-container">
          <NavigationLink to="/" data-testid="nav-link">
            <AppLabel data-testid="app-label">
              <img
                src={Logo}
                alt="Logo"
                style={{
                  height: "2.5rem",
                  verticalAlign: "middle",
                  marginRight: "1rem",
                }}
              />
            </AppLabel>
          </NavigationLink>
          <UserNavMenu onClick={toggleMenu} data-testid="user-nav-menu" />
          <LanguageSelector data-testid="language-selector" />
        </NavContainer>
      ) : (
        <></>
      )}

      <MainWindow
        isMenuOpen={isOpen}
        menuRef={menuRef}
        onClick={() => !isModalOpen && toggleMenu()}
        data-testid="main-window"
      />
    </>
  );
};

export default Layout;
