import { useAppSelector } from "../../app/hooks";
import { companiesState } from "../../features/companies/companiesActions";
import { CompanyDTO } from "../../models/DTOs/companies/CompanyDTO";

export const companyFilterService = () => {
  const { activeCompanies: companies } = useAppSelector(companiesState);

  const companyFilter = (searchText: string) => {
    const filteredCompaniesArray = companies.filter(
      (company: CompanyDTO) =>
        company.name.toLowerCase().includes(searchText.toLowerCase()) ||
        company.bulstat.toLowerCase().includes(searchText.toLowerCase())
    );
    return filteredCompaniesArray;
  };

  const mapToFilteredDTOs = (companies: CompanyDTO[]): CompanyDTO[] => {
    return companies.map(
      (c) =>
        ({
          id: c.id,
          name: c.name,
          bulstat: c.bulstat,
          contactName: c.contactName,
        } as CompanyDTO)
    );
  };

  return {
    companyFilter,
    mapToFilteredDTOs,
  };
};
