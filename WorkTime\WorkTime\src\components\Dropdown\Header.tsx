import { useContext } from "react";
import { DropdownContext } from "./Context";

interface HeaderProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  children: React.ReactNode | React.ReactNode[];
}

export const Header = (props: HeaderProps) => {
  const { children } = props;
  const { isOpen, setIsOpen, isOpened } = useContext(DropdownContext);

  const openDropdown = () => {
    isOpened && isOpened(!isOpen);
    setTimeout(() => setIsOpen((current) => !current), 200);
  };

  return (
    <div {...props} data-testid="dropdown-header" onClick={openDropdown}>
      {children}
    </div>
  );
};
