import { AbsenceTypeDTO } from "../../models/DTOs/nomenclatures/AbsenceTypeDTO";
import { HospitalTypeDTO } from "../../models/DTOs/nomenclatures/HospitalTypeDTO";
import { authenticatedGet } from "../connectionService";

export const initAbsenceTypes = async (): Promise<AbsenceTypeDTO[]> => {
    try {
        const response = await authenticatedGet<AbsenceTypeDTO[]>(`absence-types`);
        return response;
    } catch (error) {
        console.error("Error fetching absence types:", error);
        return [];
    }
};

export const initHospitalTypes = async (): Promise<HospitalTypeDTO[]> => {
    try {
        const response = await authenticatedGet<HospitalTypeDTO[]>(`hospital-types`);
        return response;
    } catch (error) {
        console.error("Error fetching hospital types:", error);
        return [];
    }
};