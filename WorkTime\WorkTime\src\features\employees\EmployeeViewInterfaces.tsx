import React from "react";
import { IEntity } from "../../models/DTOs/IEntity";
import { NomenclatureDTO } from "../../models/DTOs/nomenclatures/NomenclatureDTO";

export interface BaseEmployeeView extends IEntity {
  marker?: React.ReactNode;
  payrollId?: string;
  id: string;
  egn: string;
  fullName: string;
  email: string;
  position: string;
  departmentId: string;
  department: string;
  category: NomenclatureDTO;
  typeOfAppointment: NomenclatureDTO;
  number: string;
}

export interface RegularEmployeeView extends BaseEmployeeView {}

export interface PendingEmployeeView extends BaseEmployeeView {
  isChecked: boolean;
}
