import { createContext, ReactNode, useContext, useRef, useState } from "react";

interface MenuContextProps {
  isOpen: boolean;
  toggleMenu: () => void;
  closeMenu: () => void;
  changeView: (
    activeView: string,
    openedFrom?: "companyLabel" | "associates" | "other",
    data?: any
  ) => void;
  activeView: string;
  currentPage: string;
  menuOpenedFrom: "companyLabel" | "associates" | "other";
  viewData?: any;
}

const MenuContext = createContext<MenuContextProps>({
  isOpen: false,
  toggleMenu: () => {},
  closeMenu: () => {},
  changeView: () => {},
  activeView: "myCompanies",
  currentPage: location.pathname,
  menuOpenedFrom: "other",
  viewData: undefined,
});

export const useMenu = () => {
  return useContext(MenuContext);
};

interface MenuProviderProps {
  children: ReactNode;
}

export const MenuProvider = ({ children }: MenuProviderProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const closeTimeoutRef = useRef<NodeJS.Timeout>();

  const [activeView, setActiveView] = useState("myCompanies");
  const [currentPage, setCurrentPage] = useState(location.pathname);
  const [menuOpenedFrom, setMenuOpenedFrom] = useState<
    "companyLabel" | "other" | "associates"
  >("other");
  const [viewData, setViewData] = useState<any>(undefined);

  const changeView = (
    activeView: string,
    openedFrom: "companyLabel" | "associates" | "other" = "other",
    data?: any
  ) => {
    setActiveView(activeView);
    setMenuOpenedFrom(openedFrom);
    setViewData(data);
  };

  const toggleMenu = () => {
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = undefined;
    }
    setIsOpen((prevState: boolean) => !prevState);
    setCurrentPage(location.pathname);
  };

  const closeMenu = () => {
    setIsOpen(false);
    closeTimeoutRef.current = setTimeout(() => {
      changeView("profile", "other");
    }, 400);
  };

  const contextValue = {
    isOpen,
    toggleMenu,
    closeMenu,
    changeView,
    activeView,
    currentPage,
    menuOpenedFrom,
    viewData,
  };

  return (
    <MenuContext.Provider value={contextValue}>{children}</MenuContext.Provider>
  );
};
