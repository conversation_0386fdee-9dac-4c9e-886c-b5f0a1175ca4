import styled from "styled-components";

interface MainWindowContainerProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  ref?: React.Ref<HTMLDivElement>;
  as?: undefined;
  children?: React.ReactNode | React.ReactNode[];
}

const MainWindowContainerDiv = styled.div<MainWindowContainerProps>`
  flex: 1 1 auto;
`;

const MainWindowContainer = (props: MainWindowContainerProps) => {
  const { children } = props;
  return (
    <MainWindowContainerDiv {...props} data-testid="main-window-container">
      {children}
    </MainWindowContainerDiv>
  );
};

export default MainWindowContainer;
