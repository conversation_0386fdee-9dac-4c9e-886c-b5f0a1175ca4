import React, { useState } from "react";
import styled from "styled-components";
import { IEntity } from "../../models/DTOs/IEntity";
import { Direction } from "./Direction";
import SecondaryHeader from "./SecondaryHeader";
import TableFooter from "./TableFooter";
import TableHeader from "./TableHeader";
import TableRows, { ButtonCell } from "./TableRow";
import useTable from "./useTable";

export type ColumnDefinitionType<T, K extends keyof T> = {
  key: K;
  value: string;
};

interface TableProps<T, K extends keyof T>
  extends React.DetailedHTMLProps<
    React.TableHTMLAttributes<HTMLTableElement>,
    HTMLTableElement
  > {
  data: Array<T>;
  columns?: Array<ColumnDefinitionType<T, K>>;
  filterColumns?: (columnKey: string, searchText: string) => void;
  onRowClick?: (row: T) => void;
  buttons?: ButtonCell[];
  headerComponent?: JSX.Element;
  onSortChange?: (columnKey: string, direction: Direction) => void;
}

const TableWrapper = styled.table`
  border-collapse: collapse;
  margin: 0.5rem 5rem 0 5rem;
  table-layout: fixed;
  display: flex;
  flex-direction: column;
`;

const Table = <T extends IEntity, K extends keyof T>({
  data,
  columns,
  filterColumns,
  onRowClick,
  buttons,
  headerComponent,
  onSortChange,
  datatype,
}: TableProps<T, K>): JSX.Element => {
  const [page, setPage] = useState(1);
  const { slice, range, setSlice } = useTable(data, page, 15);

  return (
    <TableWrapper data-testid="table-table">
      {headerComponent ? (
        headerComponent
      ) : (
        <TableHeader
          columns={columns ?? []}
          data={slice}
          filterColumns={filterColumns ?? (() => {})}
          setTableData={setSlice}
        />
      )}
      <SecondaryHeader
        columns={columns ?? []}
        onSortChange={
          onSortChange as (column: string, direction: Direction) => void
        }
      />
      <TableRows
        data={slice}
        columns={columns ?? []}
        onRowClick={onRowClick}
        buttons={buttons}
        datatype={datatype}
      />

      <TableFooter
        data-testid="table-footer"
        range={range}
        slice={slice}
        setPage={setPage}
        page={page}
        setCurrentPage={setPage}
      />
    </TableWrapper>
  );
};

export default Table;
