import React, { useEffect } from "react";
import { useDispatch } from "react-redux";
import { SignalRService } from "../../services/signalR/SignalRService";
import { handleSignalREvent } from "./signalRActions";
import { useAuth } from "../authentication/AuthContext";

const signalRService = new SignalRService(
  () => localStorage.getItem("access-token") || "",
  () => localStorage.getItem("refresh-token") || ""
);

export const SignalRProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const dispatch = useDispatch();
  const { user } = useAuth();

  useEffect(() => {
    if (!user || !user.email) {
      signalRService.disconnect();
      return;
    }

    signalRService.connect((eventName: string, data: any) => {
      handleSignalREvent(eventName, data, dispatch);
    });

    return () => {
      signalRService.disconnect();
    };
  }, [user, dispatch]);

  return <>{children}</>;
};
