import { useEffect, useState } from "react";
import { css, keyframes, styled } from "styled-components";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import upImage from "../../assets/images/button/up.png";
import Container from "../../components/Container";
import Dropdown from "../../components/Dropdown/Dropdown";
import { SenderaCompanyDTO } from "../../models/DTOs/companies/SenderaCompanyDTO";
import { translate } from "../../services/language/Translator";
import CreateCompany from "./CreateCompany";
import {
  onGetSenderaCompanies,
  senderaCompanies,
} from "./senderaCompaniesAction";

const HeaderWrapper = styled(Container)<{
  isOpen: boolean;
}>`
  box-sizing: border-box;
  width: 100%;
  border: 0;
  border-radius: 1.9rem;
  padding: 1rem 1rem 1rem 1.5rem;
  transition: 0.4s;
  background: ${(props) =>
    props.isOpen
      ? "var(--company-dropdown-opened-color)"
      : "var(--company-dropdown-closed-color)"};
  outline: none;
  cursor: pointer;
  &:first-child {
    border-radius: ${(props) =>
      props.isOpen ? "1.625rem 1.625rem 0 0" : "2rem"};
  }
  data-testid: "header-wrapper";
`;

const HeaderImage = styled(Container)<{ isClicked: boolean }>`
  position: absolute;
  background-size: cover;
  height: 1rem;
  width: 1rem;
  right: 1.5rem;
  top: 40%;
  cursor: pointer;
  background-image: url(${upImage});
  transition-duration: 0.5s;
  ${({ isClicked }) =>
    !isClicked &&
    `transform:rotate(180deg);
    transition-duration: 0.5s;
    background-image: url(${upImage});
    top: 45%;
  `}
  data-testid: "header-image";
`;

const DropdownBody = styled(Dropdown.Body)`
  width: 100%;
  max-height: 30rem;
  overflow-y: auto;
  scrollbar-width: none;
  overflow-x: hidden;
  scrollbar-color: var(--scrollbar-thumb-color) var(--scrollbar-track-color);

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--scrollbar-track-color);
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb-color);
    border-radius: 10px;
    border: 2px solid var(--scrollbar-track-color);
  }
  data-testid: "dropdown-body";
`;

const fadeIn = keyframes`
  from {
      opacity: 0;
      transform: translateY(-0.625rem);
      padding-bottom: 0;
    }
    to {
      opacity: 2;
      transform: translateY(0);
      padding-bottom: 0.625rem;
    }
`;

const fadeOut = keyframes`
  from {
      opacity: 2;
      transform: translateY(0);
      padding-bottom: 0.188rem;
      background-color: var(--company-dropdown-closed-color);
    }
    to {
      opacity: 0;
      transform: translateY(-0.625rem);
      padding-bottom: 0;
      background-color: var(--company-dropdown-closed-color);
    }
`;

const Wrapper = styled(Container)<{
  isOpen: boolean;
}>`
  box-sizing: border-box;
  width: 100%;
  border: 0;
  padding: 0.5rem 1rem 0.5rem 1.5rem;
  background: var(--textbox-color);
  outline: none;
  cursor: pointer;
  &:last-child {
    border-radius: 0 0 2rem 2rem;
    padding-bottom: 2rem;
  }

  animation: ${({ isOpen }) =>
    isOpen
      ? css`
          ${fadeIn} 0.3s ease-in
        `
      : css`
          ${fadeOut} 0.3s ease-out
        `};
  data-testid: "company-wrapper";
`;

const CompanyContainer = styled(Container)`
  margin-top: 0.5rem;
  data-testid: "company-container";
`;

const ImportCompany = () => {
  const placeholderText = translate("Import companies");
  const placeholderCompany = {
    name: placeholderText,
    bulstat: "",
    contactName: "",
    id: 0,
    userStatus: "Accepted",
  } as SenderaCompanyDTO;
  const dispatch = useAppDispatch();
  const senderaCompaniesState = useAppSelector(senderaCompanies);
  const [companies, setCompanies] = useState<SenderaCompanyDTO[]>([]);
  const [company, setCompany] = useState({} as SenderaCompanyDTO);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    dispatch(onGetSenderaCompanies());
  }, [dispatch]);

  useEffect(() => {
    const activeCompanies = senderaCompaniesState.senderaCompanies.filter(
      (company) => company.userStatus === "Accepted"
    );

    setCompanies(activeCompanies);

    if (
      company.name !== placeholderText &&
      (activeCompanies.length === 0 ||
        activeCompanies.find((c) => c.id === company.id) === undefined)
    ) {
      setCompany(placeholderCompany);
    }
  }, [senderaCompaniesState, company, placeholderText, placeholderCompany]);

  useEffect(() => {
    if (Object.keys(company || {}).length === 0) {
      setCompany(placeholderCompany);
    }
  }, [company, placeholderCompany]);

  const isOpened = (isOpen: boolean) => {
    setIsOpen(isOpen);
  };

  const handleSelectImportCompany = (company: SenderaCompanyDTO) => {
    setCompany(company);
    setIsOpen(!isOpen);
  };

  return (
    <Container data-testid="import-company">
      <Dropdown isOpened={isOpened} data-testid="dropdown">
        <Dropdown.Header data-testid="dropdown-header">
          <HeaderWrapper isOpen={isOpen} data-testid="header-wrapper">
            {company?.name}
          </HeaderWrapper>
          <HeaderImage
            isClicked={isOpen}
            data-testid="header-image"
          ></HeaderImage>
        </Dropdown.Header>
        <DropdownBody data-testid="dropdown-body">
          {companies.map((company) => (
            <Wrapper
              isOpen={isOpen}
              key={company?.id}
              onClick={() => handleSelectImportCompany(company)}
              data-testid={`company-wrapper-${company?.id}`}
            >
              {company.name}
            </Wrapper>
          ))}
        </DropdownBody>
      </Dropdown>
      <CompanyContainer data-testid="company-container">
        {company.name !== placeholderText && (
          <CreateCompany importCompany={company} data-testid="create-company" />
        )}
      </CompanyContainer>
    </Container>
  );
};

export default ImportCompany;
