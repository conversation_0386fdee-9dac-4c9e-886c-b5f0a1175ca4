import React, { useState } from "react";
import styled from "styled-components";
import notificationIconActive from "../../assets/images/notifications/notificationsIconActive.svg";
import notificationIconInactive from "../../assets/images/notifications/notificationsIconInactive.svg";
import notificationIconInactiveHover from "../../assets/images/notifications/notificationsIconInactiveHover.svg";
import notificationNewMessageBigCircle from "../../assets/images/notifications/notificationsIconNewMessageBigCircle.svg";

const NotificationIconWrapper = styled.div`
  position: relative;
  display: inline-block;
`;

const NotificationBadge = styled.span`
  position: absolute;
  top: 0.2rem;
  right: 0.2rem;
  color: white;
  border-radius: 50%;
  min-width: 1.5em;
  height: 1.5em;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9em;
  font-weight: bold;
  pointer-events: none;
  z-index: 2;
`;

const NotificationButtonStyled = styled.button`
  border: none;
  cursor: pointer;
  padding: 0;
  background: transparent;
`;

interface NotificationsButtonProps {
  isOpen: boolean;
  unseenCount: number;
  onClick: () => boolean;
}

const StyledImage = styled.img`
  width: 1.95rem;
  height: 1.98rem;
`;

const getNotificationIconSrc = (
  isOpen: boolean,
  unseenCount: number,
  isHovered: boolean
) => {
  if (isOpen && unseenCount == 0) return notificationIconActive;
  if (unseenCount > 0) return notificationNewMessageBigCircle;
  if (isHovered) return notificationIconInactiveHover;
  return notificationIconInactive;
};

const NotificationsButton: React.FC<NotificationsButtonProps> = ({
  isOpen,
  unseenCount,
  onClick,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const iconSrc = getNotificationIconSrc(isOpen, unseenCount, isHovered);

  return (
    <NotificationButtonStyled
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      aria-label="Notifications"
    >
      <NotificationIconWrapper>
        <StyledImage src={iconSrc} alt="Notifications" />
        {unseenCount > 0 && iconSrc === notificationNewMessageBigCircle && (
          <NotificationBadge>
            {unseenCount > 99 ? "99" : unseenCount}
          </NotificationBadge>
        )}
      </NotificationIconWrapper>
    </NotificationButtonStyled>
  );
};

export default NotificationsButton;
