function hashStringToColor(input: string): string {
    if (!input || typeof input !== "string") {
      console.warn("hashStringToColor called with invalid input:", input);
      return "#cccccc";
    }
  
    input = input.trim().toLowerCase();
  
    const today = new Date();
    const dateString = today.toISOString().slice(0, 10);
  
    const combined = `${input}-${dateString}`;
  
    let hash = 0;
    for (let i = 0; i < combined.length; i++) {
      hash = combined.charCodeAt(i) + ((hash << 5) - hash);
    }
  
    let color = "#";
    for (let i = 0; i < 3; i++) {
      const value = (hash >> (i * 8)) & 0xff;
      color += ("00" + value.toString(16)).substr(-2);
    }
  
    return color;
  }
  
  export const generateColorFromName = hashStringToColor;
  export const generateColorFromId = hashStringToColor;
  