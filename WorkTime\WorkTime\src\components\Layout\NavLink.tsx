import { Link } from "react-router-dom";
import styled from "styled-components";

type NavLinkProps = React.ComponentProps<typeof Link> & {
  "data-testid"?: string;
};

const CustomLink = styled(Link)`
  margin: 0.5rem;
`;

const NavLink = (props: NavLinkProps) => {
  const { children, "data-testid": testId = "nav-link" } = props;
  return (
    <CustomLink {...props} data-testid={testId}>
      {children}
    </CustomLink>
  );
};

export default NavLink;
