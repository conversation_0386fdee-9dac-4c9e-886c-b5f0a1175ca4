interface ArrowTipProps {
  width?: number;
  height?: number;
  strokeColor?: string;
  direction?: "left" | "right";
  onClick?: () => void;
}

const ArrowTip = ({
  width = 20,
  height = 20,
  strokeColor = "white",
  direction = "right",
  onClick = () => {},
}: ArrowTipProps) => {
  const transform = direction === "left" ? "rotate(180 12 12)" : "";

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      width={width}
      height={height}
      onClick={onClick}
      style={{
        cursor: "pointer",
      }}
      data-testid="arrow-tip"
    >
      <path
        d="M12,4 L20,12 L12,20"
        fill="none"
        stroke={strokeColor}
        strokeWidth="3"
        transform={transform}
      />
    </svg>
  );
};

export default ArrowTip;
