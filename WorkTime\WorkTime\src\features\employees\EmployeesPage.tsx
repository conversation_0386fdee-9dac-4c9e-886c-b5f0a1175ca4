import { MouseEvent, useEffect, useState } from "react";
import styled from "styled-components";
import { useAppDispatch } from "../../app/hooks";
import Container from "../../components/Container";
import Button from "../../components/Inputs/Button";
import { useModal } from "../../components/PopUp/ActionModalContext";
import SideSpace from "../../components/view-switch/ViewSwitcher";
import {
  LOCAL_STORAGE_IS_EMPLOYEES_VIEW_LIST_VIEW,
  LOCAL_STORAGE_NEW_EMPLOYEE_DATA,
} from "../../constants/local-storage-constants";
import { initialNewEmployeeFormData } from "../../models/DTOs/newEmployee/NewEmployeeFormData";
import { translate } from "../../services/language/Translator";
import { useCompany } from "../companies/CompanyContext";
import { useMenu } from "../MenuContext";
import { onEmployeesLoaded } from "./employeesActions";
import EmployeesGridView from "./EmployeesGridView";
import EmployeesListView from "./EmployeesListView";

const EmployeesContainer = styled(Container)`
  display: flex;
  flex-direction: column;
`;

const NewEmployeeButton = styled(Button)`
  margin: 0;
  margin-bottom: 0.5rem;
  margin-right: 5rem;
  width: 12rem;
  align-self: flex-end;
`;

const Employees = () => {
  const { company } = useCompany();
  const { openModal } = useModal();
  const { toggleMenu, changeView } = useMenu();
  const dispatch = useAppDispatch();
  const [isListView, setIsListView] = useState<boolean>(
    localStorage.getItem(LOCAL_STORAGE_IS_EMPLOYEES_VIEW_LIST_VIEW) !== "false"
  );

  const handleClickListView = () => {
    localStorage.setItem(LOCAL_STORAGE_IS_EMPLOYEES_VIEW_LIST_VIEW, `${true}`);

    setIsListView(true);
  };

  const handleClickGridView = () => {
    localStorage.setItem(LOCAL_STORAGE_IS_EMPLOYEES_VIEW_LIST_VIEW, `${false}`);

    setIsListView(false);
  };

  useEffect(() => {
    if (company && company.id) {
      dispatch(onEmployeesLoaded(company.id));
    }
  }, [dispatch, company.id]);

  const handleAddNewEmployee = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    const savedData = localStorage.getItem(LOCAL_STORAGE_NEW_EMPLOYEE_DATA);

    if (
      savedData &&
      savedData !== "" &&
      savedData !== JSON.stringify(initialNewEmployeeFormData)
    ) {
      openModal({
        type: "info",
        title: translate("strContinueAddingEmployee"),
        requireMessage: false,
        confirmLabel: translate("New employee"),
        cancelLabel: translate("Yes"),
        onConfirm: () => {
          localStorage.removeItem(LOCAL_STORAGE_NEW_EMPLOYEE_DATA);
          changeView("new-employee", "other");
          toggleMenu();
        },
        onCancel: () => {
          changeView("new-employee", "other");
          toggleMenu();
        },
      });
    } else {
      changeView("new-employee", "other");
      toggleMenu();
    }
  };

  return (
    <EmployeesContainer data-testid="employees-container">
      <NewEmployeeButton
        data-testid="add-employee-button"
        label="New employee"
        onClick={handleAddNewEmployee}
      />
      {isListView ? (
        <EmployeesListView data-testid="employees-list-view" />
      ) : (
        <EmployeesGridView data-testid="employees-grid-view" />
      )}
      <SideSpace
        isListView={isListView}
        onClickListView={handleClickListView}
        onClickGridView={handleClickGridView}
      />
    </EmployeesContainer>
  );
};
export default Employees;
