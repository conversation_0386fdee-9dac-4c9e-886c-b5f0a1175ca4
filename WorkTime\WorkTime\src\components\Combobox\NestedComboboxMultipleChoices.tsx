import { useState, useRef, useCallback, useEffect } from "react";
import checkActive from "../../assets/images/combobox/check-active.svg";
import checkHover from "../../assets/images/combobox/check-hover.svg";
import checkInactive from "../../assets/images/combobox/check-inactive.svg";
import treeClosedIcon from "../../assets/images/combobox/tree-closed-icon.svg";
import treeOpenIcon from "../../assets/images/combobox/tree-open-icon.svg";
import TriangularArrow from "../../assets/images/combobox/arrow-open.svg";
import ArrowHover from "../../assets/images/combobox/arrow-hover.svg";
import Translator from "../../services/language/Translator";

import {
  ComboboxContainer,
  SelectedDisplay,
  TriangularArrowContainer,
  DropdownList,
  ScrollableWrapper,
  OptionItem,
  CheckboxContainer,
  CheckmarkImage,
  RoundCheckbox,
  MarkerIcon,
} from "./ComboboxStyles";
import styled from "styled-components";
import Container from "../Container";

interface NestedOption {
  id: string;
  label: string;
  value: any;
  childStructureLevels?: NestedOption[];
}

interface NestedComboboxMultipleChoicesProps {
  options: NestedOption[];
  selectedValues: any[];
  onChange: (selected: any[]) => void;
  placeholder?: string;
  className?: string;
}

export const OptionText = styled.span`
  flex: 1;
  text-align: left;
  white-space: nowrap;
`;
const NestedComboboxMultipleChoices = ({
  options,
  selectedValues,
  onChange,
}: NestedComboboxMultipleChoicesProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [hoveredOption, setHoveredOption] = useState<string | null>(null);
  const [openIds, setOpenIds] = useState<Set<string>>(new Set());
  const containerRef = useRef<HTMLDivElement>(null);
  const [isHoveredHeader, setIsHoveredHeader] = useState(false);

  const toggleDropdown = () => {
    setIsOpen((prev) => !prev);
  };

  const toggleOpen = (id: string) => {
    setOpenIds((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) newSet.delete(id);
      else newSet.add(id);
      return newSet;
    });
  };

  const getAllChildValues = useCallback(
    (option: NestedOption): NestedOption[] => {
      const values: any[] = [];
      if (
        option.childStructureLevels &&
        option.childStructureLevels.length > 0
      ) {
        option.childStructureLevels.forEach((child) => {
          values.push(child.value);
          if (
            child.childStructureLevels &&
            child.childStructureLevels.length > 0
          ) {
            values.push(...getAllChildValues(child));
          }
        });
      }
      return values;
    },
    []
  );

  const handleLeafClick = useCallback(
    (value: any) => {
      const newSelected = selectedValues.includes(value)
        ? selectedValues.filter((v) => v !== value)
        : [...selectedValues, value];
      onChange(newSelected);
    },
    [selectedValues, onChange]
  );

  const handleParentClick = useCallback(
    (option: NestedOption) => {
      const childValues = getAllChildValues(option);
      childValues.push(option.value);

      const isParentSelected = childValues.every((value) =>
        selectedValues.includes(value)
      );

      let newSelected: any[];
      if (isParentSelected) {
        newSelected = selectedValues.filter(
          (value) => !childValues.includes(value)
        );
      } else {
        const missingValues = childValues.filter(
          (value) => !selectedValues.includes(value)
        );
        newSelected = [...selectedValues, ...missingValues];
      }
      onChange(newSelected);
    },
    [selectedValues, onChange, getAllChildValues]
  );

  const isOptionSelected = useCallback(
    (option: NestedOption) => {
      if (
        !option.childStructureLevels ||
        option.childStructureLevels.length === 0
      ) {
        return selectedValues.includes(option.value);
      }
      return selectedValues.includes(option.value);
    },
    [selectedValues]
  );

  const getCheckmarkImage = (option: NestedOption) => {
    const isSelected = isOptionSelected(option);
    const isHovered = hoveredOption === option.value;
    if (isSelected) {
      return checkActive;
    } else if (isHovered) {
      return checkHover;
    } else {
      return checkInactive;
    }
  };

  const getTriangleImage = () => {
    return isHoveredHeader ? ArrowHover : TriangularArrow;
  };

  const renderOptions = (options: NestedOption[], level = 0): JSX.Element[] =>
    options.map((option) => {
      const isOpen = openIds.has(option.id);
      const isLeaf =
        !option.childStructureLevels ||
        option.childStructureLevels.length === 0;
      const isSelected = isOptionSelected(option);

      return (
        <>
          <OptionItem
            level={level}
            onMouseEnter={() => setHoveredOption(option.value)}
            onMouseLeave={() => setHoveredOption(null)}
            style={{ marginLeft: `${level * 0.5}rem` }}
          >
            <CheckboxContainer>
              {option.childStructureLevels &&
              option.childStructureLevels.length > 0 ? (
                <>
                  <MarkerIcon
                    src={isOpen ? treeOpenIcon : treeClosedIcon}
                    onClick={() => toggleOpen(option.id)}
                    alt={isOpen ? "Collapse" : "Expand"}
                  />
                  <RoundCheckbox
                    checked={isSelected}
                    onClick={() => handleParentClick(option)}
                  >
                    <CheckmarkImage
                      src={getCheckmarkImage(option)}
                      isHovered={!isSelected && hoveredOption === option.value}
                    />
                  </RoundCheckbox>
                </>
              ) : (
                <RoundCheckbox
                  isLast={true}
                  checked={isSelected}
                  onClick={() => handleLeafClick(option.value)}
                >
                  <CheckmarkImage
                    src={getCheckmarkImage(option)}
                    isHovered={!isSelected && hoveredOption === option.value}
                  />
                </RoundCheckbox>
              )}
            </CheckboxContainer>
            <OptionText
              onClick={
                isLeaf
                  ? () => handleLeafClick(option.value)
                  : () => toggleOpen(option.id)
              }
              style={{ cursor: "pointer" }}
            >
              {option.label}
            </OptionText>
          </OptionItem>
          {isOpen &&
            option.childStructureLevels &&
            renderOptions(option.childStructureLevels, level + 1)}
        </>
      );
    });

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        !isOpen
      ) {
        setIsOpen(false);
      }
    };
    window.addEventListener("mousedown", handleClickOutside);
    return () => {
      window.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <ComboboxContainer ref={containerRef} data-testid="combobox-container">
      <SelectedDisplay
        onClick={toggleDropdown}
        isOpen={isOpen && options.length > 0}
        onMouseEnter={() => setIsHoveredHeader(true)}
        onMouseLeave={() => setIsHoveredHeader(false)}
        data-testid="selected-display"
      >
        <Container
          style={{
            fontSize: `16px`,
            textOverflow: "clip",
            whiteSpace: "nowrap",
            flex: 1,
            minWidth: 0,
          }}
        >
          <Translator
            getString={"strDepartment"}
            data-testid="display-text-translator"
          />
        </Container>

        <TriangularArrowContainer
          src={getTriangleImage()}
          isHovered={isHoveredHeader}
          data-testid="dropdown-arrow"
        />
      </SelectedDisplay>
      <DropdownList
        isOpen={isOpen && options.length > 0}
        data-testid="dropdown-list"
      >
        <ScrollableWrapper>{renderOptions(options)}</ScrollableWrapper>
      </DropdownList>
    </ComboboxContainer>
  );
};

export default NestedComboboxMultipleChoices;
