import { Action, Reducer } from "redux";
import { AppThunk, ClearStateAction, RootState } from "../../app/store";
import { AddStructureLevelRequest } from "../../models/Requests/CompanyStructure/AddStructureLevelRequest";
import {
  authenticatedGet,
  authenticatedDelete,
  authenticatedPost,
} from "../../services/connectionService";
import { StructureLevelDTO } from "../../models/DTOs/companyStructure/StructureLevelDTO";

interface companyStructureState {
  structureLevels: StructureLevelDTO[];
}

interface AddStructureLevelAction {
  type: "ADD_STRUCTURE_LEVEL";
  structureLevelDTO: StructureLevelDTO;
}

interface LoadStructureLevelsAction {
  type: "LOAD_STRUCTURE_LEVELS";
  structureLevels: StructureLevelDTO[];
}

interface DeleteStructureLevelAction {
  type: "DELETE_STRUCTURE_LEVEL";
  structureLevelId: string;
}

type KnownActions =
  | AddStructureLevelAction
  | LoadStructureLevelsAction
  | DeleteStructureLevelAction
  | ClearStateAction;

const addStructureLevelAction = (
  structureLevelDTO: StructureLevelDTO
): AddStructureLevelAction => ({
  type: "ADD_STRUCTURE_LEVEL",
  structureLevelDTO: structureLevelDTO,
});

const loadStructureLevelAction = (
  structureLevels: StructureLevelDTO[]
): LoadStructureLevelsAction => ({
  type: "LOAD_STRUCTURE_LEVELS",
  structureLevels: structureLevels,
});

const deleteStructureLevelAction = (
  structureLevelId: string
): DeleteStructureLevelAction => ({
  type: "DELETE_STRUCTURE_LEVEL",
  structureLevelId: structureLevelId,
});

export const actionCreators = {
  onStructureLevelAdded: (
    name: string,
    typeId: number,
    companyId: string,
    parentStructureLevelId?: string
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      const addStructureLevelRequest = {
        name: name,
        typeId: typeId,
        parentStructureLevelId: parentStructureLevelId,
        companyId: companyId,
      } as AddStructureLevelRequest;

      authenticatedPost<StructureLevelDTO>(
        "structure-levels",
        addStructureLevelRequest
      ).then((structureLevelDTO) => {
        dispatch(addStructureLevelAction(structureLevelDTO));
      });
    };
  },
  onStructureLevelsLoaded: (
    companyId: string
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      companyId &&
        authenticatedGet<StructureLevelDTO | StructureLevelDTO[]>(
          "structure-levels?companyId=" + companyId
        )
          .then((response) => {
            let structureLevels: StructureLevelDTO[];

            if (Array.isArray(response)) {
              structureLevels = response;
            } else if (response && typeof response === "object") {
              structureLevels = [response];
            } else {
              console.error("Unexpected response format:", response);
              return;
            }

            dispatch(loadStructureLevelAction(structureLevels));
          })
          .catch((error) => {
            console.error("Error fetching structure levels:", error);
          });
    };
  },
  onStructureLevelDeleted: (
    structureLevelId: string
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedDelete("structure-levels/" + structureLevelId).then(() => {
        dispatch(deleteStructureLevelAction(structureLevelId));
      });
    };
  },
};

export const {
  onStructureLevelAdded,
  onStructureLevelsLoaded,
  onStructureLevelDeleted,
} = actionCreators;

const initialState = {
  structureLevels: [],
} as companyStructureState;

export const reducer: Reducer<companyStructureState> = (
  state = initialState,
  action: Action
) => {
  var incomingAction = action as KnownActions;

  switch (incomingAction.type) {
    case "ADD_STRUCTURE_LEVEL":
      return {
        ...state,
        structureLevels: [
          ...state.structureLevels,
          incomingAction.structureLevelDTO,
        ],
      };
    case "LOAD_STRUCTURE_LEVELS":
      return {
        ...state,
        structureLevels: [...incomingAction.structureLevels],
      };
    case "DELETE_STRUCTURE_LEVEL":
      const structureLevelId = incomingAction.structureLevelId;

      return {
        ...state,
        structureLevels: [
          ...state.structureLevels.filter(
            (sl) => sl.workTimeId !== structureLevelId
          ),
        ],
      };
    case "CLEAR_STATE":
      return initialState;
    default:
      return state;
  }
};

export const selectStructureLevels = (state: RootState) =>
  state.structureLevels;
