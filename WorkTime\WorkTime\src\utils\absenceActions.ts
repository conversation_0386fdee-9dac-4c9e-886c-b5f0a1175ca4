import { AbsenceInfo } from "../components/CalendarComponent/types/AbsenceInfo";
import { AbsenceStatus } from "../models/DTOs/absence/AbsenceStatus";
import { AbsenceHospitalDTO } from "../models/DTOs/absence/AbsenceHospitalDTO";
import { useAppDispatch, useAppSelector } from "../app/hooks";
import { useModal } from "../components/PopUp/ActionModalContext";
import { useCompany } from "../features/companies/CompanyContext";
import { toast } from "react-toastify";
import { translate } from "../services/language/Translator";
import {
  getLeaveType,
  getLeaveDeletedMessage,
  getLeaveApprovedMessage,
  getLeaveDeclinedMessage,
} from "./leaveTypeUtils";
import { authenticatedPut } from "../services/worktimeConnectionService";
import { onPayrollAbsenceUpdated } from "../features/payroll/payrollsActions";
import { useMenu } from "../features/MenuContext";
import { useUserEmployee } from "../features/UserEmployeeContext";
import { DefaultPermissions } from "../constants/permissions";

function formatDate(dateString: string) {
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = date.getFullYear();
  return `${day}.${month}.${year}`;
}

export function useAbsenceActions() {
  const { openModal } = useModal();
  const dispatch = useAppDispatch();
  const { company } = useCompany();
  const { closeMenu } = useMenu();
  const { userEmployee } = useUserEmployee();

  const isAdmin = userEmployee.permissions.includes(
    DefaultPermissions.Attendances.Write
  );

  const handleDeleteAbsence = (absence: AbsenceInfo) => {
    const leaveType = getLeaveType(absence);
    const period = `${formatDate(absence.startDate)} - ${formatDate(
      absence.endDate
    )}`;
    const isDeleting =
      absence.status !== AbsenceStatus.DeletedByUserAfterApproval;
    openModal({
      type: "error",
      title: isDeleting
        ? translate("strDeleteAbsenceTitle")
            .replace("{0}", absence.employeeName)
            .replace("{1}", leaveType)
            .replace("{2}", period)
        : translate("strCancelAbsenceDeletion"),
      requireMessage: false,
      confirmLabel: isDeleting ? translate("Delete") : translate("Yes"),
      cancelLabel: isDeleting ? translate("Cancel") : translate("No"),
      onConfirm: async () => {
        const editedAbsence = await authenticatedPut<AbsenceHospitalDTO>(
          isAdmin
            ? `attendances/${absence.id}/delete/admin`
            : `attendances/${absence.id}/delete`,
          {
            absenceId: absence.id,
            payrollId: absence.payrollId,
            isHospital: absence.isHospital,
            companyId: company.id,
          }
        );
        dispatch(onPayrollAbsenceUpdated(editedAbsence));
        toast.success(getLeaveDeletedMessage(absence));
        closeMenu();
      },
    });
  };

  // Approve
  const handleApproveAbsence = (absence: AbsenceInfo) => {
    const { id, isHospital, employeeName, startDate, endDate, isOverlapping } =
      absence;
    const isDeleting =
      absence.status === AbsenceStatus.DeletedByUserAfterApproval;
    const leaveType = getLeaveType(absence);
    const period = `${formatDate(startDate)} - ${formatDate(endDate)}`;
    const title = isOverlapping
      ? translate("strAbsenceExistsOnSickLeaveApprove")
      : isDeleting
      ? translate("strApproveAbsenceDeletion")
      : translate("strApproveAbsenceTitle")
          .replace("{0}", employeeName)
          .replace("{1}", leaveType)
          .replace("{2}", period);
    const confirmLabel =
      isOverlapping || isDeleting ? translate("Yes") : translate("Approve");
    const cancelLabel =
      isOverlapping || isDeleting ? translate("No") : translate("Cancel");
    openModal({
      type: "info",
      title,
      requireMessage: false,
      confirmLabel,
      cancelLabel,
      onConfirm: async () => {
        const editedAbsence = await authenticatedPut<AbsenceHospitalDTO>(
          `attendances/${id}/update`,
          {
            absenceId: id,
            status: isDeleting
              ? AbsenceStatus.DeletedByAdmin
              : AbsenceStatus.Approved,
            isHospital,
            message: "",
          }
        );
        dispatch(onPayrollAbsenceUpdated(editedAbsence));
        if (isDeleting) {
          toast.success(
            isHospital
              ? translate("strSickLeaveDeletionApproved")
              : translate("strAbsenceDeletionApproved")
          );
        } else {
          toast.success(getLeaveApprovedMessage(absence));
        }
      },
    });
  };

  // Decline
  const handleDeclineAbsence = (absence: AbsenceInfo) => {
    const isDeleting =
      absence.status !== AbsenceStatus.DeletedByUserAfterApproval;
    openModal({
      type: "info",
      title: isDeleting
        ? translate("strDeclineAbsenceTitle").replace(
            "{0}",
            absence.employeeName
          )
        : translate("strCancelAbsenceDeletion"),
      requireMessage: isDeleting,
      confirmLabel: isDeleting ? translate("Decline") : translate("Yes"),
      cancelLabel: isDeleting ? translate("Cancel") : translate("No"),
      onConfirm: async (message?: string) => {
        const editedAbsence = await authenticatedPut<AbsenceHospitalDTO>(
          `attendances/${absence.id}/update`,
          {
            absenceId: absence.id,
            status: isDeleting
              ? AbsenceStatus.Declined
              : AbsenceStatus.Approved,
            isHospital: absence.isHospital,
            message,
          }
        );
        dispatch(onPayrollAbsenceUpdated(editedAbsence));
        if (isDeleting) {
          toast.success(getLeaveDeclinedMessage(absence));
        } else {
          toast.success(
            absence.isHospital
              ? translate("strSickLeaveDeletionDeclined")
              : translate("strAbsenceDeletionDeclined")
          );
        }
        closeMenu();
      },
    });
  };

  return { handleDeleteAbsence, handleApproveAbsence, handleDeclineAbsence };
}
