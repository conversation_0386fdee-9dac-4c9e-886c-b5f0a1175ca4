import React from "react";
import styled from "styled-components";
import ArrowIcon from "../../assets/images/arrows/arrow.png";
import Image from "../Image";

interface ArrowButtonProps {
  direction: "left" | "right";
  onClick: () => void;
}

const StyledArrow = styled(Image)<{ direction: "left" | "right" }>`
  cursor: pointer;
  transform: ${({ direction }) =>
    direction === "left" ? "rotate(270deg)" : "rotate(90deg)"};
  width: 1.5rem;
  height: 1.35rem;
  position: absolute;
  top: 50%;
  transform: ${({ direction }) =>
    direction === "left"
      ? "translateY(-50%) rotate(270deg)"
      : "translateY(-50%) rotate(90deg)"};
  ${({ direction }) => direction}: 2rem;
`;

export const ArrowButton: React.FC<ArrowButtonProps> = ({
  direction,
  onClick,
}) => {
  return (
    <StyledArrow
      src={ArrowIcon}
      direction={direction}
      onClick={onClick}
      alt="Arrow"
      data-testid={`arrow-button-${direction}`}
    />
  );
};
