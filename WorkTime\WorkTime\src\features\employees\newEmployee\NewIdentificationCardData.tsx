import { useEffect, useState } from "react";
import styled from "styled-components";
import addressDot from "../../../assets/images/dot-icons/addressDot.svg";
import idDot from "../../../assets/images/dot-icons/idDot.svg";
import Container from "../../../components/Container";
import Datepicker from "../../../components/Datepicker/Datepicker";
import Dropdown from "../../../components/Dropdown/Dropdown";
import {
  DropdownContainer,
  DropdownFormField,
  DropdownFormRow,
  HeaderImage,
  HeaderWrapper,
  DropdownBody as StyledDropdownBody,
  StyledInput,
  Wrapper,
} from "../../../components/Dropdown/DropdownStyles";
import Image from "../../../components/Image";
import Label from "../../../components/Inputs/Label";
import Textbox from "../../../components/Inputs/Textbox";
import { Genders } from "../../../constants/enum";
import { AddressPurpose } from "../../../models/DTOs/address/AddressDTO";
import { ICitiesType } from "../../../models/DTOs/enums/ICitiesType";
import { IdentityCardDataDTO } from "../../../models/DTOs/newEmployee/IdentityCardDataDTO";
import { NewAddressDTO } from "../../../models/DTOs/newEmployee/NewAddressDTO";
import Translator, { translate } from "../../../services/language/Translator";
import { useDefaultPlaces } from "../../DefaultLocationDataContext";

const FormContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const SectionHeader = styled(Container)`
  display: flex;
  align-items: center;
  margin: 1.5rem 0 1rem 0;

  &:first-child {
    margin-top: 0;
  }
`;

const SectionTitle = styled(Label)`
  font-size: 1rem;
  margin-left: 0.5rem;
  color: #333;
  white-space: nowrap;
`;

const SectionSeparator = styled.div`
  height: 1px;
  display: flex;
  background-color: white;
  width: 100%;
  margin-left: 0.5rem;
  margin-top: 0.3rem;
`;

const FormRow = styled(Container)`
  display: flex;
  flex-direction: row;
  gap: 0.3rem;
  width: 100%;
`;

const FormField = styled(Container)`
  flex: 1;
  min-width: 0;
`;

const DropdownBody = styled(StyledDropdownBody).attrs({ as: Dropdown.Body })`
  background: var(--textbox-color);
`;

interface Props {
  onValidation?: (isValid: boolean, data: any) => void;
  onChange?: () => void;
  data?: IdentityCardDataDTO;
}

const NewIdentificationCardData = ({ onValidation, onChange, data }: Props) => {
  const [formData, setFormData] = useState<IdentityCardDataDTO>({
    idNumber: data?.idNumber || "",
    issuedOn: data?.issuedOn ? new Date(data.issuedOn) : undefined,
    issuedBy: data?.issuedBy || "",
    citizenship: data?.citizenship || "",
    gender: data?.gender || Genders.None,
    idAddress: {
      city: data?.idAddress?.city || null,
      postalCode: data?.idAddress?.postalCode || "",
      municipality: data?.idAddress?.municipality || null,
      district: data?.idAddress?.district || null,
      street: data?.idAddress?.street || "",
      block: data?.idAddress?.block || "",
      apartment: data?.idAddress?.apartment || "",
      phone: data?.idAddress?.phone || "",
      workPhone: data?.idAddress?.workPhone || "",
      email: data?.idAddress?.email || "",
      workEmail: data?.idAddress?.workEmail || "",
      country: undefined,
      purpose: AddressPurpose.IdentityCard,
      description: data?.idAddress?.description || "",
      neighborhood: data?.idAddress?.neighborhood || "",
      cityName: data?.idAddress?.cityName || "",
      districtName: data?.idAddress?.districtName || "",
      municipalityName: data?.idAddress?.municipalityName || "",
    } as NewAddressDTO,
  });

  const [isGenderDropdownOpen, setIsGenderDropdownOpen] = useState(false);
  const [isCityDropdownOpen, setIsCityDropdownOpen] = useState(false);
  const [isDistrictDropdownOpen, setIsDistrictDropdownOpen] = useState(false);
  const [isMunicipalityDropdownOpen, setIsMunicipalityDropdownOpen] =
    useState(false);
  const [isPostalCodeDropdownOpen, setIsPostalCodeDropdownOpen] =
    useState(false);

  const { cities, districts, municipalities } = useDefaultPlaces();
  const postCodes = Array.from(
    new Set(
      cities
        .map((city) => city.postCode)
        .filter((postCode) => postCode !== undefined)
    )
  );

  const genderOptions = [
    { value: Genders.Male, label: "Male" },
    { value: Genders.Female, label: "Female" },
  ];

  const [citySearchText, setCitySearchText] = useState("");
  const [districtSearchText, setDistrictSearchText] = useState("");
  const [municipalitySearchText, setMunicipalitySearchText] = useState("");
  const [postalCodeSearchText, setPostalCodeSearchText] = useState("");

  useEffect(() => {
    const isValid = true;

    if (onValidation) {
      onValidation(isValid, formData);
    }
  }, [formData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev: IdentityCardDataDTO) => {
      const newData = {
        ...prev,
        [name]: value,
      };

      if (onChange) {
        onChange();
      }

      return newData;
    });
  };

  const handleAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev: IdentityCardDataDTO) => {
      const newData = {
        ...prev,
        idAddress: {
          ...prev.idAddress,
          [name]: value,
        },
      };

      if (onChange) {
        onChange();
      }

      return newData;
    });
  };

  const handleGenderSelect = (value: Genders) => {
    setFormData((prev: IdentityCardDataDTO) => {
      const newData = {
        ...prev,
        gender: value,
      };

      if (onChange) {
        onChange();
      }

      return newData;
    });
    setIsGenderDropdownOpen(false);
  };

  const getGenderLabel = () => {
    if (formData.gender === Genders.None) return "Gender";

    const option = genderOptions.find((opt) => opt.value === formData.gender);
    return option ? option.label : "Gender";
  };

  const handleGenderDropdownToggle = (isOpen: boolean) => {
    setIsGenderDropdownOpen(isOpen);
  };

  const handleCityDropdownToggle = (isOpen: boolean) => {
    setIsCityDropdownOpen(isOpen);
  };

  const handleCityTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCitySearchText(e.target.value);
    setFormData((prev: IdentityCardDataDTO) => ({
      ...prev,
      idAddress: {
        ...prev.idAddress,
        city: null,
        cityName: e.target.value,
      },
    }));
    setIsCityDropdownOpen(true);
  };

  const filteredCities = cities
    .filter(
      (city) =>
        city.postCode &&
        city.postCode !== "" &&
        (!formData.idAddress?.municipality ||
          formData.idAddress?.municipality?.id === city.municipalityId) &&
        city.name.toLowerCase().startsWith(citySearchText.toLowerCase())
    )
    .filter(
      (city, index, array) =>
        array.findIndex((c) => c.name === city.name) === index
    )
    .sort((a, b) => {
      const municipalityName =
        formData?.idAddress?.municipality?.name?.toLowerCase();
      const aIsMatch = a.name.toLowerCase() === municipalityName;
      const bIsMatch = b.name.toLowerCase() === municipalityName;

      return (
        (bIsMatch ? 1 : 0) - (aIsMatch ? 1 : 0) || a.name.localeCompare(b.name)
      );
    });

  const filteredDistricts = districts
    .filter((district) =>
      district.name.toLowerCase().startsWith(districtSearchText.toLowerCase())
    )
    .sort((a, b) => a.name.localeCompare(b.name));

  const filteredMunicipalities = municipalities
    .filter(
      (municipality) =>
        (!formData.idAddress?.district ||
          formData.idAddress?.district?.id === municipality.districtId) &&
        municipality.name
          .toLowerCase()
          .startsWith(municipalitySearchText.toLowerCase())
    )
    .sort((a, b) => a.name.localeCompare(b.name));

  const handleCitySelect = (city: ICitiesType) => {
    setCitySearchText(city.name);

    const matchingMunicipality = municipalities.find(
      (municipality) => municipality.id === city.municipalityId
    );

    const matchingDistrict = districts.find(
      (district) => district.id === matchingMunicipality?.districtId
    );

    setFormData((prev: IdentityCardDataDTO) => {
      const newData = {
        ...prev,
        idAddress: {
          ...prev.idAddress,
          postalCode:
            cities
              .filter((c) => c.name === city.name)
              .sort((a, b) =>
                (a.postCode || "").localeCompare(b.postCode || "")
              )[0]?.postCode || prev.idAddress.postalCode,
          city: city,
          cityName: city.name,
          ...(matchingDistrict && {
            district: matchingDistrict,
            districtName: matchingDistrict.name,
          }),
          ...(matchingMunicipality && {
            municipality: matchingMunicipality,
            municipalityName: matchingMunicipality.name,
          }),
        },
      };

      if (onChange) {
        onChange();
      }

      return newData;
    });

    setIsCityDropdownOpen(false);
  };

  const handleDateChange = (value: Date) => {
    setFormData((prev: IdentityCardDataDTO) => {
      const newData = {
        ...prev,
        issuedOn: value,
      };

      if (onChange) {
        onChange();
      }

      return newData;
    });
  };

  const handleDistrictDropdownToggle = (isOpen: boolean) => {
    setIsDistrictDropdownOpen(isOpen);
  };

  const handleMunicipalityDropdownToggle = (isOpen: boolean) => {
    setIsMunicipalityDropdownOpen(isOpen);
  };

  const handleDistrictSelect = (district: any) => {
    setDistrictSearchText(district.name);
    setFormData((prev: IdentityCardDataDTO) => {
      const newData = {
        ...prev,
        idAddress: {
          ...prev.idAddress,
          district: district,
          districtName: district.name,
        },
      };

      if (onChange) {
        onChange();
      }

      return newData;
    });
    setIsDistrictDropdownOpen(false);
  };

  const handleMunicipalitySelect = (municipality: any) => {
    setMunicipalitySearchText(municipality.name);
    setFormData((prev: IdentityCardDataDTO) => {
      const newData = {
        ...prev,
        idAddress: {
          ...prev.idAddress,
          municipality: municipality,
          municipalityName: municipality.name,
        },
      };

      if (onChange) {
        onChange();
      }

      return newData;
    });
    setIsMunicipalityDropdownOpen(false);
  };

  const handleDistrictTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setDistrictSearchText(e.target.value);
    setFormData((prev: IdentityCardDataDTO) => ({
      ...prev,
      idAddress: {
        ...prev.idAddress,
        district: null,
        districtName: e.target.value,
      },
    }));
    setIsDistrictDropdownOpen(true);
  };

  const handleMunicipalityTextChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setMunicipalitySearchText(e.target.value);
    setFormData((prev: IdentityCardDataDTO) => ({
      ...prev,
      idAddress: {
        ...prev.idAddress,
        municipality: null,
        municipalityName: e.target.value,
      },
    }));
    setIsMunicipalityDropdownOpen(true);
  };

  const handlePostalCodeDropdownToggle = (isOpen: boolean) => {
    setIsPostalCodeDropdownOpen(isOpen);
  };

  const handlePostalCodeTextChange = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setPostalCodeSearchText(e.target.value);
    setFormData((prev: IdentityCardDataDTO) => ({
      ...prev,
      idAddress: {
        ...prev.idAddress,
        postalCode: e.target.value,
      },
    }));
    setIsPostalCodeDropdownOpen(true);
  };

  const handlePostalCodeSelect = (postCode: string) => {
    setPostalCodeSearchText(postCode);

    let matchingCity = cities.find(
      (city) =>
        city.postCode === postCode &&
        city.name.toLowerCase() ===
          municipalities
            .find((municipality) => municipality.id === city.municipalityId)
            ?.name?.toLowerCase()
    );

    if (!matchingCity) {
      matchingCity = cities.find((city) => city.postCode === postCode);
    }

    if (matchingCity) {
      const matchingMunicipality = municipalities.find(
        (municipality) => municipality.id === matchingCity.municipalityId
      );

      const matchingDistrict = districts.find(
        (district) => district.id === matchingMunicipality?.districtId
      );

      setFormData((prev: IdentityCardDataDTO) => {
        const newData = {
          ...prev,
          idAddress: {
            ...prev.idAddress,
            postalCode: postCode,
            city: matchingCity,
            cityName: matchingCity.name,
            ...(matchingDistrict && {
              district: matchingDistrict,
              districtName: matchingDistrict.name,
            }),
            ...(matchingMunicipality && {
              municipality: matchingMunicipality,
              municipalityName: matchingMunicipality.name,
            }),
          },
        };

        if (onChange) {
          onChange();
        }

        return newData;
      });
    } else {
      setFormData((prev: IdentityCardDataDTO) => {
        const newData = {
          ...prev,
          idAddress: {
            ...prev.idAddress,
            postalCode: postCode,
          },
        };

        if (onChange) {
          onChange();
        }

        return newData;
      });
    }

    setIsPostalCodeDropdownOpen(false);
  };

  const filteredPostCodes = postCodes
    .filter((postCode) => postCode?.includes(postalCodeSearchText))
    .sort((a, b) => a.localeCompare(b));

  return (
    <FormContainer>
      <SectionHeader>
        <Image src={idDot} data-testid="address-dot-image" />
        <SectionTitle>{translate("Personal Identity Card")}</SectionTitle>
        <SectionSeparator />
      </SectionHeader>

      <FormRow>
        <FormField>
          <Textbox
            name="idNumber"
            label="ID number"
            value={formData.idNumber}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Datepicker
            name="issuedOn"
            label="Issued on"
            initialDate={formData.issuedOn}
            onSelectDate={handleDateChange}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="issuedBy"
            label="Issued by"
            value={formData.issuedBy}
            handleChange={handleChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="citizenship"
            label="Citizenship"
            value={formData.citizenship}
            handleChange={handleChange}
          />
        </FormField>
      </FormRow>

      <DropdownFormRow>
        <DropdownFormField>
          <DropdownContainer>
            <Dropdown
              isOpened={handleGenderDropdownToggle}
              data-testid="dropdown"
            >
              <Dropdown.Header data-testid="dropdown-header">
                <HeaderWrapper
                  isOpen={isGenderDropdownOpen}
                  data-testid="header-wrapper"
                >
                  <Translator getString={getGenderLabel()} />
                </HeaderWrapper>
                <HeaderImage
                  isClicked={isGenderDropdownOpen}
                  data-testid="header-image"
                ></HeaderImage>
              </Dropdown.Header>
              <DropdownBody data-testid="dropdown-body">
                {genderOptions.map((c) => (
                  <Wrapper
                    isOpen={isGenderDropdownOpen}
                    key={c?.value}
                    onClick={() => handleGenderSelect(c.value)}
                    data-testid={`gender-wrapper-${c?.value}`}
                  >
                    <Translator getString={c.label} />
                  </Wrapper>
                ))}
              </DropdownBody>
            </Dropdown>
          </DropdownContainer>
        </DropdownFormField>
        <FormField />
      </DropdownFormRow>

      <SectionHeader>
        <Image src={addressDot} data-testid="address-dot-image" />
        <SectionTitle>{translate("strIDAddress")}</SectionTitle>
        <SectionSeparator />
      </SectionHeader>

      <DropdownFormRow>
        <DropdownFormField>
          <DropdownContainer>
            <Dropdown
              isOpened={handleCityDropdownToggle}
              isOpen={isCityDropdownOpen}
              data-testid="city-dropdown"
            >
              <Dropdown.Header data-testid="cities-dropdown-header">
                <HeaderWrapper
                  isOpen={isCityDropdownOpen}
                  data-testid="header-wrapper"
                >
                  <StyledInput
                    type="text"
                    value={
                      citySearchText || formData.idAddress?.city?.name || ""
                    }
                    placeholder={translate("strCityVillage")}
                    onChange={handleCityTextChange}
                    onClick={(e) => e.stopPropagation()}
                  />
                </HeaderWrapper>
                <HeaderImage
                  isClicked={isCityDropdownOpen}
                  data-testid="header-image"
                ></HeaderImage>
              </Dropdown.Header>
              <DropdownBody data-testid="cities-dropdown-body">
                {filteredCities.map((city) => (
                  <Wrapper
                    isOpen={isCityDropdownOpen}
                    key={city.id}
                    onClick={() => handleCitySelect(city)}
                    data-testid={`city-wrapper-${city.id}`}
                  >
                    <Translator getString={city.name} />
                  </Wrapper>
                ))}
              </DropdownBody>
            </Dropdown>
          </DropdownContainer>
        </DropdownFormField>

        <DropdownFormField>
          <DropdownContainer>
            <Dropdown
              isOpened={handlePostalCodeDropdownToggle}
              isOpen={isPostalCodeDropdownOpen}
              data-testid="postal-code-dropdown"
            >
              <Dropdown.Header data-testid="postal-code-dropdown-header">
                <HeaderWrapper
                  isOpen={isPostalCodeDropdownOpen}
                  data-testid="postal-code-header-wrapper"
                >
                  <StyledInput
                    type="text"
                    value={
                      postalCodeSearchText ||
                      formData.idAddress?.postalCode ||
                      ""
                    }
                    placeholder={translate("PC")}
                    onChange={handlePostalCodeTextChange}
                    onClick={(e) => e.stopPropagation()}
                  />
                </HeaderWrapper>
                <HeaderImage
                  isClicked={isPostalCodeDropdownOpen}
                  data-testid="postal-code-header-image"
                ></HeaderImage>
              </Dropdown.Header>
              <DropdownBody data-testid="postal-code-dropdown-body">
                {filteredPostCodes.map((postCode) => (
                  <Wrapper
                    isOpen={isPostalCodeDropdownOpen}
                    key={postCode}
                    onClick={() => handlePostalCodeSelect(postCode)}
                    data-testid={`postal-code-wrapper-${postCode}`}
                  >
                    {postCode}
                  </Wrapper>
                ))}
              </DropdownBody>
            </Dropdown>
          </DropdownContainer>
        </DropdownFormField>
      </DropdownFormRow>

      <DropdownFormRow>
        <DropdownFormField>
          <DropdownContainer>
            <Dropdown
              isOpened={handleDistrictDropdownToggle}
              isOpen={isDistrictDropdownOpen}
              data-testid="district-dropdown"
            >
              <Dropdown.Header data-testid="district-dropdown-header">
                <HeaderWrapper
                  isOpen={isDistrictDropdownOpen}
                  data-testid="district-header-wrapper"
                >
                  <StyledInput
                    type="text"
                    value={
                      districtSearchText ||
                      formData.idAddress?.district?.name ||
                      ""
                    }
                    placeholder={translate("District")}
                    onChange={handleDistrictTextChange}
                    onClick={(e) => e.stopPropagation()}
                  />
                </HeaderWrapper>
                <HeaderImage
                  isClicked={isDistrictDropdownOpen}
                  data-testid="district-header-image"
                ></HeaderImage>
              </Dropdown.Header>
              <DropdownBody data-testid="district-dropdown-body">
                {filteredDistricts.map((district) => (
                  <Wrapper
                    isOpen={isDistrictDropdownOpen}
                    key={district.name}
                    onClick={() => handleDistrictSelect(district)}
                    data-testid={`district-wrapper-${district.name}`}
                  >
                    <Translator getString={district.name} />
                  </Wrapper>
                ))}
              </DropdownBody>
            </Dropdown>
          </DropdownContainer>
        </DropdownFormField>

        <DropdownFormField>
          <DropdownContainer>
            <Dropdown
              isOpened={handleMunicipalityDropdownToggle}
              isOpen={isMunicipalityDropdownOpen}
              data-testid="municipality-dropdown"
            >
              <Dropdown.Header data-testid="municipality-dropdown-header">
                <HeaderWrapper
                  isOpen={isMunicipalityDropdownOpen}
                  data-testid="municipality-header-wrapper"
                >
                  <StyledInput
                    type="text"
                    value={
                      municipalitySearchText ||
                      formData.idAddress?.municipality?.name ||
                      ""
                    }
                    placeholder={translate("Municipality")}
                    onChange={handleMunicipalityTextChange}
                    onClick={(e) => e.stopPropagation()}
                  />
                </HeaderWrapper>
                <HeaderImage
                  isClicked={isMunicipalityDropdownOpen}
                  data-testid="municipality-header-image"
                ></HeaderImage>
              </Dropdown.Header>
              <DropdownBody data-testid="municipality-dropdown-body">
                {filteredMunicipalities.map((municipality) => (
                  <Wrapper
                    isOpen={isMunicipalityDropdownOpen}
                    key={municipality.name}
                    onClick={() => handleMunicipalitySelect(municipality)}
                    data-testid={`municipality-wrapper-${municipality.name}`}
                  >
                    <Translator getString={municipality.name} />
                  </Wrapper>
                ))}
              </DropdownBody>
            </Dropdown>
          </DropdownContainer>
        </DropdownFormField>
      </DropdownFormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="neighborhood"
            label="Region"
            value={formData.idAddress?.neighborhood}
            handleChange={handleAddressChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="street"
            label="Street"
            value={formData.idAddress?.street}
            handleChange={handleAddressChange}
          />
        </FormField>
      </FormRow>

      <FormRow>
        <FormField>
          <Textbox
            name="block"
            label="Block"
            value={formData.idAddress?.block}
            handleChange={handleAddressChange}
          />
        </FormField>
        <FormField>
          <Textbox
            name="apartment"
            label="Apartment"
            value={formData.idAddress?.apartment}
            handleChange={handleAddressChange}
          />
        </FormField>
      </FormRow>
    </FormContainer>
  );
};

export default NewIdentificationCardData;
