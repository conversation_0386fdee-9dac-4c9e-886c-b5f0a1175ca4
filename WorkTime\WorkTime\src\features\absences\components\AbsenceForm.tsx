import { styled } from "styled-components";
import Container from "../../../components/Container";
import Image from "../../../components/Image";
import Datepicker from "../../../components/Datepicker/Datepicker";
import Combobox from "../../../components/Combobox/Combobox";
import Textbox from "../../../components/Inputs/Textbox";
import noImage from "../../../assets/images/attendancies/no-image.svg";
import noImageHover from "../../../assets/images/attendancies/no-image-hover.svg";
import Button from "../../../components/Inputs/Button";
import { AbsenceInfo } from "../../../components/CalendarComponent/types/AbsenceInfo";
import { useEffect, useState } from "react";
import { EventType } from "../../../models/DTOs/absence/EventType";
import { AbsenceStatus } from "../../../models/DTOs/absence/AbsenceStatus";
import { useAbsenceActions } from "../../../utils/absenceActions";

const Row = styled(Container)`
  justify-content: center;
  width: 100%;
`;

const ImageUpload = styled(Container)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;

const AbsenceDetails = styled(Container)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 9rem;
  margin-left: 2em;
  width: 100%;
`;

const UploadImage = styled(Image)`
  cursor: pointer;

  &:hover {
    content: url(${noImageHover});
  }
`;

const ButtonGroup = styled(Container)`
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
`;

const ActionButton = styled(Button)`
  flex: 1;
`;

const RequestButton = styled(Button)`
  margin-top: 1rem;
  width: 100%;
`;

export interface FormData {
  startDate: string | null;
  endDate: string | null;
  selectedOption: string;
  comment: string;
}

interface AbsenceFormProps {
  options: string[];
  isLoading: boolean;
  selectedAbsence: AbsenceInfo | null;
  absencesVisible: boolean;
  initialData?: Partial<FormData>;
  onSubmit: (formData: FormData) => void;
  onEdit: (formData: FormData) => void;
  isEditing: boolean;
  isAdmin: boolean;
}

const formatDate = (date: Date) => {
  const day = date.getDate().toString().padStart(2, "0");
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const year = date.getFullYear();
  return `${day}.${month}.${year}`;
};

const parseDateString = (dateStr: string): Date => {
  const [day, month, year] = dateStr.split(".");
  return new Date(Date.UTC(Number(year), Number(month) - 1, Number(day)));
};

const AbsenceForm = ({
  options,
  isLoading,
  selectedAbsence,
  absencesVisible,
  initialData,
  onSubmit,
  onEdit,
  isEditing,
  isAdmin,
}: AbsenceFormProps) => {
  const [startDate, setStartDate] = useState<string | null>(
    initialData?.startDate || null
  );
  const [endDate, setEndDate] = useState<string | null>(
    initialData?.endDate || null
  );
  const [selectedOption, setSelectedOption] = useState<string>(
    initialData?.selectedOption || options[0]
  );
  const [comment, setComment] = useState<string>(initialData?.comment || "");
  const [hasChanges, setHasChanges] = useState(false);

  const isAbsenceActive = (absence: AbsenceInfo | null): boolean => {
    if (!absence) return false;
    if (absence.status === AbsenceStatus.Pending) return false;

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const absenceStartDate = new Date(absence.startDate);
    absenceStartDate.setHours(0, 0, 0, 0);

    const absenceEndDate = new Date(absence.endDate);
    absenceEndDate.setHours(0, 0, 0, 0);

    return today >= absenceStartDate && today <= absenceEndDate;
  };

  const { handleDeleteAbsence, handleApproveAbsence, handleDeclineAbsence } =
    useAbsenceActions();

  useEffect(() => {
    if (selectedAbsence) {
      const fromDate = formatDate(new Date(selectedAbsence.startDate));
      const toDate = formatDate(new Date(selectedAbsence.endDate));
      setStartDate(fromDate);
      setEndDate(toDate);
      setComment(
        selectedAbsence.isHospital
          ? selectedAbsence.sickNote || ""
          : selectedAbsence.comment || ""
      );

      if (selectedAbsence.isHospital) {
        setSelectedOption(
          selectedAbsence.typeIdentifier === EventType.БолниченПоБременност
            ? "Maternity Leave"
            : "Sick Leave"
        );
      } else {
        setSelectedOption(
          selectedAbsence.typeIdentifier === EventType.ПлатенГодишенОтпуск
            ? "Paid Leave"
            : "Unpaid Leave"
        );
      }
      setHasChanges(false);
    } else {
      setStartDate(initialData?.startDate || null);
      setEndDate(initialData?.endDate || null);
      setSelectedOption(initialData?.selectedOption || options[0]);
      setComment(initialData?.comment || "");
      setHasChanges(false);
    }
  }, [selectedAbsence, initialData, options]);

  useEffect(() => {
    if (!selectedAbsence && options.length > 0) {
      setSelectedOption(options[0]);
    }
  }, [options, selectedAbsence]);

  const handleStartDateSelect = (date: Date) => {
    setStartDate(formatDate(date));
    setHasChanges(true);
  };

  const handleEndDateSelect = (date: Date) => {
    setEndDate(formatDate(date));
    setHasChanges(true);
  };

  const handleSelectedChange = (newSelectedOption: string) => {
    setSelectedOption(newSelectedOption);
    setHasChanges(true);
  };

  const handleCommentChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setComment(event.target.value);
    setHasChanges(true);
  };

  const handleSubmit = () => {
    const formData: FormData = {
      startDate,
      endDate,
      selectedOption,
      comment,
    };
    onSubmit(formData);
    setHasChanges(false);
  };

  const handleEdit = () => {
    const formData: FormData = {
      startDate,
      endDate,
      selectedOption,
      comment,
    };
    onEdit(formData);
    setHasChanges(false);
  };

  return (
    <>
      <Row data-testid="absence-row">
        <Container
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            width: "100%",
          }}
        >
          <ImageUpload data-testid="image-upload-container">
            <UploadImage
              data-testid="no-image"
              size="large"
              alt="no image"
              src={noImage}
            />
          </ImageUpload>
          <AbsenceDetails data-testid="absence-details">
            <Datepicker
              data-testid="start-date-picker"
              initialDate={startDate ? parseDateString(startDate) : null}
              onSelectDate={handleStartDateSelect}
              label="Start Date"
              disabled={!isEditing && selectedAbsence !== null && isAdmin}
            />
            <Datepicker
              data-testid="end-date-picker"
              initialDate={endDate ? parseDateString(endDate) : null}
              onSelectDate={handleEndDateSelect}
              label="End Date"
              disabled={!isEditing && selectedAbsence !== null && isAdmin}
            />
            <Combobox
              data-testid="absence-type-combobox"
              options={options}
              initialSelectedItem={selectedOption}
              onChange={handleSelectedChange}
              disabled={!isEditing && selectedAbsence !== null && isAdmin}
            />
          </AbsenceDetails>
        </Container>
        <Textbox
          data-testid="comment-input"
          label="Comment"
          value={comment}
          handleChange={handleCommentChange}
          readOnly={!isEditing && selectedAbsence !== null && isAdmin}
        />
      </Row>
      {selectedAbsence ? (
        <ButtonGroup>
          <ActionButton
            label={!isEditing && isAdmin ? "Cancel" : "Edit"}
            onClick={() =>
              !isEditing && isAdmin
                ? handleDeclineAbsence(selectedAbsence)
                : handleEdit()
            }
            disabled={isLoading || (!hasChanges && isEditing)}
          />
          <ActionButton
            label={!isEditing && isAdmin ? "Approve" : "Delete"}
            onClick={() =>
              !isEditing && isAdmin
                ? handleApproveAbsence(selectedAbsence)
                : handleDeleteAbsence(selectedAbsence)
            }
            disabled={
              isLoading || (!isAdmin && isAbsenceActive(selectedAbsence))
            }
          />
        </ButtonGroup>
      ) : (
        <RequestButton
          data-testid="request-button"
          label={absencesVisible ? "Request absence" : "Send sick leave"}
          onClick={handleSubmit}
          disabled={
            startDate === null ||
            endDate === null ||
            isLoading ||
            (startDate !== null &&
              endDate !== null &&
              parseDateString(endDate) < parseDateString(startDate))
          }
        />
      )}
    </>
  );
};

export default AbsenceForm;
