import React from "react";
import styled from "styled-components";
import { generateColorFromName } from "../../utils/colorUtils";

interface AvatarProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  photo: string;
  name: string;
  size: number;
  color?: string;
  background?: string;
}

function getColorset() {
  var letters = "0123456789ABCDEF";
  var color = "#";
  for (var i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
}

const getStringHashCode = (name: string) => {
  let initials;
  const nameSplit = name?.split(" ");
  const nameLength = nameSplit?.length;
  if (nameLength > 1) {
    initials =
      nameSplit[0].substring(0, 1) + nameSplit[nameLength - 1].substring(0, 1);
  } else if (nameLength === 1) {
    initials = nameSplit[0].substring(0, 1);
  } else return;

  return initials.toUpperCase();
};

const AvatarWrapper = styled.div`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
`;

const AvatarDiv = styled.div<{
  initials: string;
  color: string;
}>`
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  padding: 0.2rem;
  color: var(--avatar-initials-color);
  text-align: center;
  background: ${(p) => p.color};
  border-radius: 50%;
  width: 1.3rem;
  height: 1.3rem;
`;

const AvatarPhoto = styled.img<{ size: number }>`
  ${({ size }) => `
       height: ${size}rem;
       width: ${size}rem;
  `}
  border-radius: 1rem;
  margin-left: 1rem;
`;

const Avatar = (props: AvatarProps) => {
  const { photo, name, size, className, color, background } = props;
  const initials = getStringHashCode(name) ?? "";
  const avatarColor = background || color || generateColorFromName(name);

  return (
    <>
      {photo.length === 0 ? (
        <AvatarWrapper className={className}>
          <AvatarDiv color={avatarColor} initials={initials}>
            {initials}
          </AvatarDiv>
        </AvatarWrapper>
      ) : (
        <AvatarPhoto src={photo} size={size} />
      )}
    </>
  );
};

export default Avatar;
