import styled from "styled-components";
import MainWindowContainer from "../../../components/MainWindowContainer";
import Container from "../../../components/Container";
import Button from "../../../components/Inputs/Button";
import { css } from "styled-components";
import keyImage from "../../../assets/images/side-menu-buttons/key-active.svg";
import keyImageDisable from "../../../assets/images/side-menu-buttons/key.svg";
import userImage from "../../../assets/images/side-menu-buttons/user-active.svg";
import userImageDisable from "../../../assets/images/side-menu-buttons/profile.svg";
import userGroupsImage from "../../../assets/images/side-menu-buttons/associates-active.svg";
import userGroupsImageDisable from "../../../assets/images/side-menu-buttons/associates.svg";
import companiesImage from "../../../assets/images/side-menu-buttons/companies-active.svg";
import companiesImageDisable from "../../../assets/images/side-menu-buttons/companies.svg";
import { useEffect, useState } from "react";
import MyProfileSideMenu from "./MyProfileSideMenu";
import ChangePassword from "../../authentication/ChangePassword";
import CompaniesSideMenuIndex from "../../companies/CompaniesSideMenuIndex";
import CoworkersSideMenu from "../../coworkers/CoworkersSideMenu";
import CompaniesListSideMenu from "../../companies/companiesPage/CompaniesListSideMenu";
import { DefaultPermissions } from "../../../constants/permissions";
import { useUserEmployee } from "../../UserEmployeeContext";
import { LOCAL_STORAGE_WORKTIME_ROLE_NAME } from "../../../constants/local-storage-constants";
import { useAuth } from "../../authentication/AuthContext";

const MainContainer = styled(MainWindowContainer)`
  margin: 0 auto;
  width: 100%;
`;

const ButtonsContainer = styled(Container)<{
  hide: boolean;
}>`
  display: ${({ hide }) => (hide ? "none" : "grid")};
  margin-bottom: 0.625rem;
  width: 100%;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 0.5rem;
  grid-row-gap: 0;
`;

const ProfileButton = styled(Button)<{
  profileImage: string;
  profileHoverImage: string;
  profileDisabledImage: string;
  isDisable: boolean;
}>`
  width: 100%;
  background-color: var(--profile-button-background-color);
  color: var(--profile-button-color);
  font-size: 1rem;
  background-image: url(${(p) => p.profileImage});
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: 1.6rem;
  background-position: left 1rem center;
  padding: 1rem 0 1rem 3.1rem;
  text-align: left;
  cursor: default;

  ${(p) =>
    p.isDisable &&
    css`
      background-image: url(${p.profileDisabledImage});
      background-color: var(--profile-button-background-color-disable);
      color: var(--profile-button-color-disable);
      background-position: left 1rem center;
      background-size: 1.6rem;
      background-repeat: no-repeat;
      cursor: pointer;
    `}

  &:hover {
    background-image: url(${(p) => p.profileHoverImage});
    background-position: left 1rem center;
    background-size: 1.6rem;
    background-repeat: no-repeat;
    cursor: pointer;
    background-color: ${(p) =>
      p.isDisable
        ? "var(--company-button-background-color-hover)"
        : "var(--company-button-background-color)"};
  }

  @media (max-width: 760px) {
    padding: 1rem 1rem 1rem 3rem;
  }
`;

interface ProfileSideMenuProps {
  onButtonClick: (button: string) => void;
  goBack: boolean;
  initialPage?: string;
}

const ProfileSideMenu: React.FC<ProfileSideMenuProps> = ({
  onButtonClick,
  goBack,
  initialPage = "profile",
}) => {
  const [activeButton, setActiveButton] = useState(initialPage);
  const { userEmployee } = useUserEmployee();
  const { user } = useAuth();

  useEffect(() => {
    const handleStorage = () => {
      setActiveButton("profile");
    };

    handleStorage();
  }, [userEmployee.employeeId]);

  useEffect(() => {
    if (goBack && activeButton === "myCompanies") {
      handleButtonClick("profile");
    }
  }, [goBack, activeButton]);

  useEffect(() => {
    setActiveButton(initialPage);
  }, [initialPage, user]);

  const handleButtonClick = (button: string) => {
    setActiveButton(button);
    onButtonClick(button);
  };

  const doesUserHavePermission =
    user.workTimeRoleName === "grOwner" ||
    (userEmployee.permissions &&
      userEmployee.permissions.includes(DefaultPermissions.Companies.Write));

  return (
    <MainContainer data-testid="profile-side-menu">
      <ButtonsContainer
        hide={activeButton === "myCompanies"}
        data-testid="buttons-container"
      >
        <ProfileButton
          label="Profile"
          profileImage={userImage}
          profileDisabledImage={userImageDisable}
          profileHoverImage={
            activeButton !== "profile" ? userImageDisable : userImage
          }
          isDisable={activeButton !== "profile"}
          onClick={() => handleButtonClick("profile")}
          data-testid="profile-button"
        />
        <ProfileButton
          label="Passwords"
          profileImage={keyImage}
          profileDisabledImage={keyImageDisable}
          profileHoverImage={
            activeButton !== "password" ? keyImageDisable : keyImage
          }
          isDisable={activeButton !== "password"}
          onClick={() => handleButtonClick("password")}
          data-testid="password-button"
        />
        {doesUserHavePermission && (
          <ProfileButton
            label="My Companies"
            profileImage={companiesImage}
            profileDisabledImage={companiesImageDisable}
            profileHoverImage={
              activeButton !== "myCompanies"
                ? companiesImageDisable
                : companiesImage
            }
            isDisable={activeButton !== "myCompanies"}
            onClick={() => handleButtonClick("myCompanies")}
            data-testid="my-companies-button"
          />
        )}
        {!doesUserHavePermission && (
          <ProfileButton
            label="Companies list"
            profileImage={companiesImage}
            profileDisabledImage={companiesImageDisable}
            profileHoverImage={
              activeButton !== "companiesList"
                ? companiesImageDisable
                : companiesImage
            }
            isDisable={activeButton !== "companiesList"}
            onClick={() => handleButtonClick("companiesList")}
            data-testid="companies-list-button"
          />
        )}

        <ProfileButton
          label="Coworkers"
          profileImage={userGroupsImage}
          profileDisabledImage={userGroupsImageDisable}
          profileHoverImage={
            activeButton !== "coworkers"
              ? userGroupsImageDisable
              : userGroupsImage
          }
          isDisable={activeButton !== "coworkers"}
          onClick={() => handleButtonClick("coworkers")}
          data-testid="coworkers-button"
        />
      </ButtonsContainer>
      <div data-testid="content-container">
        {activeButton === "profile" && (
          <MyProfileSideMenu data-testid="my-profile-menu" />
        )}
        {activeButton === "password" && (
          <ChangePassword data-testid="change-password" />
        )}
        {activeButton === "coworkers" && (
          <CoworkersSideMenu data-testid="coworkers-menu" />
        )}
        {activeButton === "myCompanies" && (
          <CompaniesSideMenuIndex data-testid="companies-menu" />
        )}
        {activeButton === "companiesList" && (
          <CompaniesListSideMenu data-testid="companies-menu" />
        )}
      </div>
    </MainContainer>
  );
};

export default ProfileSideMenu;
