import React, { useState } from "react";
import Image from "../Image";
import styled from "styled-components";
import logoImage from "../../assets/images/button/logo.png";
import logoHoverImage from "../../assets/images/button/logoHover.png";

const Wrapper = styled.div`
  position: relative;
  border-radius: 2rem;
`;

const Container = styled.label`
  cursor: pointer;
`;

const StyledInput = styled.input`
  display: none;
`;

const StyledImage = styled(Image)`
  position: absolute;
  top: 5%;
  border-radius: 4rem;
`;

const UploadButton = () => {
  const [image, setImage] = useState({ preview: "", file: {} });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.length) {
      setImage({
        preview: URL.createObjectURL(e.target.files[0]),
        file: e.target.files[0],
      });
    }
  };

  return (
    <Wrapper data-testid="upload-button-wrapper">
      <Container htmlFor="upload-button" data-testid="upload-button-container">
        {image.preview ? (
          <StyledImage src={image.preview} size="extraLarge" />
        ) : (
          <StyledImage
            src={logoImage}
            size="extraLarge"
            title="Upload"
            onMouseOver={(e) => (e.currentTarget.src = logoHoverImage)}
            onMouseOut={(e) => (e.currentTarget.src = logoImage)}
          />
        )}
      </Container>
      <StyledInput
        id="upload-button"
        type="file"
        onChange={handleChange}
        data-testid="upload-button-input"
      />
    </Wrapper>
  );
};

export default UploadButton;
