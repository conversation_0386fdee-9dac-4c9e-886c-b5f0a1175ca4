import { Action, Reducer } from "redux";
import { AppThunk, RootState } from "../../app/store";
import { OnboardingDocumentRequest } from "../../models/Requests/AddOnboardingRequest";
import { authenticatedPost } from "../../services/connectionService";
import { OnboardingDocumentDTO } from "../../models/DTOs/OnboardingDocumentDTO";

interface onboardingDocumentsState {
  onboardingDocuments: OnboardingDocumentDTO[];
}

interface AddDocumentAction {
  type: "ADD_ONBOARDINGDOCUMENT";
  onboardingDocument: OnboardingDocumentDTO;
}

type KnownActions = AddDocumentAction;

const addDocumentAction = (
  onboardingDocument: OnboardingDocumentDTO
): AddDocumentAction => ({
  type: "ADD_ONBOARDINGDOCUMENT",
  onboardingDocument: onboardingDocument,
});

export const actionCreators = {
  onDocumentSaved: (
    onboardingDocument: OnboardingDocumentDTO
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      const addonboardingDocumentRequest = {
        onboardingDocumentDTO: onboardingDocument,
      } as OnboardingDocumentRequest;
      authenticatedPost<OnboardingDocumentDTO>(
        "onboarding-documents/add-document",
        addonboardingDocumentRequest
      ).then((onboardingDocument) => {
        dispatch(addDocumentAction(onboardingDocument));
      });
    };
  },
};

export const { onDocumentSaved } = actionCreators;

const initialState = {
  onboardingDocuments: [],
} as onboardingDocumentsState;

export const reducer: Reducer<onboardingDocumentsState> = (
  state = initialState,
  action: Action
) => {
  var incomingAction = action as KnownActions;
  switch (incomingAction.type) {
    case "ADD_ONBOARDINGDOCUMENT":
      return {
        ...state,
        onboardingdocuments: [
          ...state.onboardingDocuments,
          incomingAction.onboardingDocument,
        ],
      };
    default:
      return state;
  }
};

export const selectDocument = (state: RootState) => state.onboardingDocuments;
