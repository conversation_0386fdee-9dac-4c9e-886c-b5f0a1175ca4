import { IEnumType } from "../../models/DTOs/enums/IEnumType";
import { authenticatedGet } from "../worktimeConnectionService";

export const initTypesOfAppointment = async (): Promise<IEnumType[]> => {
  try {
    const response = await authenticatedGet<IEnumType[]>(`appointment-types`);
    return response;
  } catch (error) {
    console.error("Error fetching appointment types:", error);
    return [];
  }
};

export const initPayrollCategories = async (): Promise<IEnumType[]> => {
  try {
    const response = await authenticatedGet<IEnumType[]>(`payroll-categories`);
    return response;
  } catch (error) {
    console.error("Error fetching payroll categories:", error);
    return [];
  }
};
