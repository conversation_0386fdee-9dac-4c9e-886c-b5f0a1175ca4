import {
  LOCAL_STORAGE_ACCESS_TOKEN,
  LOCAL_STORAGE_COMPANY_ID,
  LOCAL_STORAGE_HAS_SIGNED_IN,
  LOCAL_STORAGE_NEW_EMPLOYEE_DATA,
  LOCAL_STORAGE_REFRESH_TOKEN,
  LOCAL_STORAGE_USER_EMAIL,
  LOCAL_STORAGE_USER_ID,
  LOCAL_STORAGE_WORKTIME_ROLE_NAME,
} from "../../constants/local-storage-constants";
import { User } from "../../features/authentication/AuthContext";
import { LoginUserDTO } from "../../models/DTOs/LoginUserDTO";
import { RegisterUserDTO } from "../../models/DTOs/RegisterUserDTO";
import { UserDTO } from "../../models/DTOs/users/UserDTO";
import { ChangeForgottenPasswordRequest } from "../../models/Requests/ChangeForgottenPasswordRequest";
import { authenticatedPost, getResponse, post } from "../connectionService";
import { getUser } from "../users/userService";

function getBasicAuthenticationToken(email: string, password: string) {
  return "Basic " + window.btoa(email + ":" + password);
}

export const registration = async (registerDTO: RegisterUserDTO) => {
  return await post(`user-registration/registration`, registerDTO);
};

export const confirmEmail = async (userId: string, code: string) => {
  return await post(`user-registration/confirm-email`, { userId, code });
};

export const logout = () => {
  localStorage.removeItem(LOCAL_STORAGE_USER_EMAIL);
  localStorage.removeItem(LOCAL_STORAGE_ACCESS_TOKEN);
  localStorage.removeItem(LOCAL_STORAGE_REFRESH_TOKEN);
  localStorage.removeItem(LOCAL_STORAGE_HAS_SIGNED_IN);
  localStorage.removeItem(LOCAL_STORAGE_COMPANY_ID);
  localStorage.removeItem(LOCAL_STORAGE_WORKTIME_ROLE_NAME);
  localStorage.removeItem(LOCAL_STORAGE_USER_ID);
  localStorage.removeItem(LOCAL_STORAGE_NEW_EMPLOYEE_DATA);

  authenticatedPost(`sso/logout`);
};

export const login = async (
  loginDTO: LoginUserDTO,
  rememberMeChecked: boolean
) => {
  const authToken = getBasicAuthenticationToken(
    loginDTO.email,
    loginDTO.password
  );

  const response = await getResponse(`sso/try-login`, {
    Authorization: authToken,
  });

  if (response.status !== 200) {
    return undefined;
  }

  const user = (await response.json()) as UserDTO;

  localStorage.setItem(
    LOCAL_STORAGE_HAS_SIGNED_IN,
    user.hasSignedIn.toString()
  );
  localStorage.setItem(LOCAL_STORAGE_USER_ID, user.id);
  localStorage.setItem(LOCAL_STORAGE_USER_EMAIL, loginDTO.email);
  localStorage.setItem(
    LOCAL_STORAGE_ACCESS_TOKEN,
    response.headers.get("Authorization")
  );
  if (rememberMeChecked) {
    localStorage.setItem(
      LOCAL_STORAGE_REFRESH_TOKEN,
      response.headers.get("Refresh-token")
    );
  }

  return user;
};

export const facebookLogin = async (
  token: string
): Promise<string | undefined> => {
  const response = await getResponse(`sso/try-facebook-login`, {
    Authorization: token,
  });

  if (response.status !== 200) return undefined;

  const email = await response.text();

  localStorage.setItem(LOCAL_STORAGE_USER_EMAIL, email);
  localStorage.setItem(
    LOCAL_STORAGE_ACCESS_TOKEN,
    response.headers.get("Authorization")
  );
  localStorage.setItem(
    LOCAL_STORAGE_REFRESH_TOKEN,
    response.headers.get("Refresh-token")
  );

  return email;
};

export const microsoftLogin = async (
  token: string
): Promise<string | undefined> => {
  const response = await getResponse(`sso/try-microsoft-login`, {
    Authorization: token,
  });

  if (response.status !== 200) return undefined;

  const email = await response.text();

  localStorage.setItem(LOCAL_STORAGE_USER_EMAIL, email);
  localStorage.setItem(
    LOCAL_STORAGE_ACCESS_TOKEN,
    response.headers.get("Authorization")
  );
  localStorage.setItem(
    LOCAL_STORAGE_REFRESH_TOKEN,
    response.headers.get("Refresh-token")
  );

  return email;
};

export const googleLogin = async (
  token?: string
): Promise<string | undefined> => {
  const response = await getResponse(`sso/try-google-login`, {
    Authorization: token,
  });

  if (response.status !== 200) return undefined;

  const email = await response.text();
  localStorage.setItem(LOCAL_STORAGE_USER_EMAIL, email);
  localStorage.setItem(
    LOCAL_STORAGE_ACCESS_TOKEN,
    response.headers.get("Authorization")
  );
  localStorage.setItem(
    LOCAL_STORAGE_REFRESH_TOKEN,
    response.headers.get("Refresh-token")
  );

  return email;
};

export const isAuthenticated = () => {
  const accessToken = localStorage.getItem(LOCAL_STORAGE_ACCESS_TOKEN);
  return (
    accessToken !== undefined && accessToken !== null && accessToken !== ""
  );
};

export const initUser = async (): Promise<User> => {
  const email = localStorage.getItem(LOCAL_STORAGE_USER_EMAIL);
  const hasSignedIn = localStorage.getItem(LOCAL_STORAGE_HAS_SIGNED_IN);
  try {
    const response = await getUser();
    const workTimeRoleName = localStorage.getItem(
      LOCAL_STORAGE_WORKTIME_ROLE_NAME
    );
    const employeeId = localStorage.getItem(LOCAL_STORAGE_USER_ID);
    if (isAuthenticated() && email) {
      if (hasSignedIn) {
        return {
          email: email ?? undefined,
          hasSignedIn: hasSignedIn === "true",
          workTimeRoleName: workTimeRoleName ?? undefined,
          userId: employeeId ?? undefined,
          firstName: response.firstName ?? undefined,
          secondName: response.secondName ?? undefined,
          lastName: response.lastName ?? undefined,
        };
      }
      return {
        email: email,
        hasSignedIn: false,
        workTimeRoleName: workTimeRoleName ?? undefined,
        userId: undefined,
        firstName: undefined,
        secondName: undefined,
        lastName: undefined,
      };
    }
    return {
      email: undefined,
      hasSignedIn: false,
      workTimeRoleName: undefined,
      userId: undefined,
      firstName: undefined,
      secondName: undefined,
      lastName: undefined,
    };
  } catch (error) {
    return {
      email: undefined,
      hasSignedIn: false,
      workTimeRoleName: undefined,
      userId: undefined,
      firstName: undefined,
      secondName: undefined,
      lastName: undefined,
    };
  }
};

export const changeForgottenPassword = async (
  changeForgottenPassword: ChangeForgottenPasswordRequest
) => {
  return await post<boolean>(
    "sso/change-forgotten-password",
    changeForgottenPassword
  );
};
