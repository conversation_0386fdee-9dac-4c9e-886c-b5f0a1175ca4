import { createContext, useContext, useEffect, useState } from "react";
import { UserEmployeeDTO } from "../models/DTOs/users/UserEmployeeDTO";
import { initUserEmployee } from "../services/users/userService";
import { useCompany } from "./companies/CompanyContext";

const initialUserEmployeeState: UserEmployeeDTO = {
  userId: "",
  employeeId: "",
  name: "",
  payrolls: [],
  permissions: [],
};

interface UserEmployeeContextType {
  userEmployee: UserEmployeeDTO;
  setUserEmployee: (userEmployee: UserEmployeeDTO) => void;
  resetUserEmployee: () => void;
}

const UserEmployeeContext = createContext<UserEmployeeContextType | undefined>(
  undefined
);

export const UserEmployeeProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [userEmployee, setUserEmployee] = useState<UserEmployeeDTO>(
    initialUserEmployeeState
  );
  const { company } = useCompany();

  const resetUserEmployee = () => {
    setUserEmployee(initialUserEmployeeState);
  };

  useEffect(() => {
    const initializeUserEmployee = async () => {
      if (company.id) {
        const userEmployeeData = await initUserEmployee();
        setUserEmployee(userEmployeeData);
      }
    };

    initializeUserEmployee();
  }, [company.id]);

  return (
    <UserEmployeeContext.Provider
      value={{ userEmployee, setUserEmployee, resetUserEmployee }}
    >
      {children}
    </UserEmployeeContext.Provider>
  );
};

export const useUserEmployee = (): UserEmployeeContextType => {
  const context = useContext(UserEmployeeContext);
  if (context === undefined) {
    throw new Error(
      "useUserEmployee must be used within a UserEmployeeProvider"
    );
  }
  return context;
};
