import { useMenu } from "../MenuContext";
import { translate } from "../../services/language/Translator";
import { MyAbsencesContainer } from "./containers/MyAbsencesContainer";
import { AllAbsencesContainer } from "./containers/AllAbsencesContainer";
import {
  OuterContainer,
  ToggleContainer,
  ToggleLabel,
  ToggleSwitch,
  ToggleInput,
  ToggleSlider,
} from "./styles";
import { DefaultPermissions } from "../../constants/permissions";
import { useUserEmployee } from "../UserEmployeeContext";
import { Employee } from "./useFilteredEmployees";
import { PayrollDTO } from "../../models/DTOs/payrolls/PayrollDTO";
import { LightPayrollDTO } from "../../models/DTOs/payrolls/LightPayrollDTO";
import { useEffect } from "react";

interface AttendancesRightViewProps {
  selectedPayroll?: PayrollDTO | LightPayrollDTO;
  selectedEmployee?: Employee;
  selectedYear?: number;
  selectedMonth?: number;
  onSelectEmployee: (employee: Employee | undefined) => void;
  hoveredEmployee?: Employee;
  onEmployeeHover?: (employee: Employee | undefined) => void;
  showMyAbsences: boolean;
  onToggleMyAbsences: () => void;
}

const AttendancesRightView: React.FC<AttendancesRightViewProps> = ({
  selectedEmployee,
  selectedPayroll,
  selectedYear,
  selectedMonth,
  onSelectEmployee,
  hoveredEmployee,
  onEmployeeHover,
  showMyAbsences,
  onToggleMyAbsences,
}) => {
  const { toggleMenu, changeView } = useMenu();
  const { userEmployee } = useUserEmployee();

  const handleAddAbsence = () => {
    toggleMenu();
    changeView("absence", "other", {
      selectedPayroll,
      selectedYear,
      selectedMonth,
    });
  };

  useEffect(() => {
    if (selectedPayroll) {
      changeView("absence", "other", {
        selectedPayroll,
        selectedYear,
        selectedMonth,
      });
    }
  }, [selectedPayroll, selectedYear, selectedMonth]);

  return (
    <OuterContainer data-testid="outer-container">
      <ToggleContainer>
        <ToggleLabel showMyAbsences={showMyAbsences}>
          {translate("strMyAbsences")}
        </ToggleLabel>
        <ToggleSwitch>
          <ToggleInput
            type="checkbox"
            checked={showMyAbsences}
            onChange={onToggleMyAbsences}
            data-testid="toggle-my-absences"
          />
          <ToggleSlider />
        </ToggleSwitch>
      </ToggleContainer>
      {!showMyAbsences &&
      userEmployee.permissions.includes(
        DefaultPermissions.Attendances.Write
      ) ? (
        <AllAbsencesContainer
          selectedEmployee={selectedEmployee}
          onSelectEmployee={onSelectEmployee}
          selectedMonth={selectedMonth}
          selectedYear={selectedYear}
        />
      ) : (
        <MyAbsencesContainer
          selectedPayroll={selectedPayroll as PayrollDTO}
          selectedYear={selectedYear}
          selectedMonth={selectedMonth}
          handleAddAbsence={handleAddAbsence}
          selectedEmployee={selectedEmployee}
          onSelectEmployee={onSelectEmployee}
        />
      )}
    </OuterContainer>
  );
};

export default AttendancesRightView;
