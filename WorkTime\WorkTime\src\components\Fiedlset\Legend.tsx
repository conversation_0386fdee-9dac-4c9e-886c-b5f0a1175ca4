import React from "react";
import styled from "styled-components";
import Translator from "../../services/language/Translator";

interface LegendProps
  extends React.DetailedHTMLProps<
    React.HTMLAttributes<HTMLLegendElement>,
    HTMLLegendElement
  > {
  children: React.ReactNode;
}

const StyledLegend = styled.legend`
  padding: 0rem 0.3rem;
  font-size: 1.2rem;
`;

const Legend: React.FC<LegendProps> = (props) => {
  const { children, ...rest } = props;
  const content =
    typeof children === "string" ? (
      <Translator getString={children} data-testid="legend-translator" />
    ) : (
      children
    );

  return (
    <StyledLegend data-testid="legend" {...rest}>
      {content}
    </StyledLegend>
  );
};

export default Legend;
