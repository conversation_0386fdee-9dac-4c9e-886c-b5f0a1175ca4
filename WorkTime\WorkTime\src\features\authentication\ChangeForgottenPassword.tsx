import { useNavigate, useParams } from "react-router-dom";
import Container from "../../components/Container";
import { ChangeEvent, MouseEvent, useState, useEffect } from "react";
import { changeForgottenPassword } from "../../services/authentication/authenticationService";
import PasswordBox from "../../components/Inputs/PasswordBox";
import styled from "styled-components";
import Button from "../../components/Inputs/Button";
import MainWindowContainer from "../../components/MainWindowContainer";
import { translate } from "../../services/language/Translator";
import Alert from "../../components/Inputs/Alert";
import { PasswordStrengthType } from "../../models/Enums/PasswortStrengthType";
import { passwordStrengthCheck } from "../../services/authentication/passwordService";
import { PasswordStrengthIndicator } from "./PasswordStrengthIndicator";

const ChangeForgottenPasswordContainer = styled(MainWindowContainer)`
  width: clamp(30%, 20rem, 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
`;

const DataContainer = styled(Container)`
  width: 100%;
`;

const ChangePasswordButton = styled(Button)`
  width: 100%;
`;

const PasswordStrengthIndicatorContainer = styled.div`
  color: var(--password-strength-indicator-color);
  margin-top: 0.5rem;
  text-align: center;
  min-height: 1.5em;
`;

const TopLeftMessage = styled.p`
  padding: 2rem;
  font-size: 1.1rem;
  width: 100%;
  text-align: left;
  line-height: 1.5;
  resize: none;
  overflow: hidden;
`;

const ChangeForgottenPassword = () => {
  const { email, code } = useParams();
  const [password, setPassword] = useState("");
  const [showStrengthHint, setShowStrengthHint] = useState(false);
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isChangePasswordButtonActive, setIsChangePasswordButtonActive] =
    useState(false);
  const navigate = useNavigate();
  const [message, setMessage] = useState("");
  const [passwordStrength, setPasswordStrength] = useState(
    PasswordStrengthType.Empty
  );

  useEffect(() => {
    setIsChangePasswordButtonActive(
      password === confirmPassword &&
        password !== "" &&
        passwordStrength == PasswordStrengthType.Strong
    );
  }, [password, confirmPassword, passwordStrength]);

  const handlePasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);

    const currentPasswordStrength = passwordStrengthCheck(newPassword);
    setPasswordStrength(currentPasswordStrength);
  };

  const handleConfirmPasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    setConfirmPassword(e.currentTarget.value);
  };

  const handleChangePasswordClicked = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    if (!email || !code) {
      setMessage("strSomethingWentWrong");
      return;
    }

    changeForgottenPassword({
      changeForgottenPasswordDTO: { email, code, password },
    })
      .then((isChangedSuccessfully) => {
        if (isChangedSuccessfully) navigate(`/auth/login/${email}`);
      })
      .catch((error: any) => {
        if (error === 400) setMessage("strInvalidCode");
        else setMessage("strSomethingWentWrong");
      });
  };

  const confirmPasswordValidation = {
    isValid:
      confirmPassword === undefined ||
      confirmPassword === "" ||
      password === undefined ||
      password === "" ||
      confirmPassword === password,
    alertMessage: translate("Passwords does not match"),
  };

  return (
    <ChangeForgottenPasswordContainer>
      <DataContainer>
        <TopLeftMessage>
          {translate("strForgottenPasswordPageDescription")}
        </TopLeftMessage>
        <PasswordBox
          name="password"
          handleChange={handlePasswordChange}
          label="Password"
          type="password"
          value={password}
          data-testid="password-textbox"
        />
        <PasswordBox
          name="confirm-password"
          handleChange={handleConfirmPasswordChange}
          label="Confirm Password"
          type="password"
          value={confirmPassword}
          data-testid="confirm-password-textbox"
          validation={confirmPasswordValidation}
        />
        <div
          onMouseEnter={() => setShowStrengthHint(true)}
          onMouseLeave={() => setShowStrengthHint(false)}
        >
          <PasswordStrengthIndicator
            passwordStrength={passwordStrength}
            setShowStrengthHint={() => {}}
          />
          <PasswordStrengthIndicatorContainer
            style={{ visibility: showStrengthHint ? "visible" : "hidden" }}
          >
            {translate("strPasswordStrenghtIndicator")}
          </PasswordStrengthIndicatorContainer>
        </div>
        <ChangePasswordButton
          disabled={!isChangePasswordButtonActive}
          onClick={handleChangePasswordClicked}
          label={translate("Change password")}
          data-testid="change-password-button"
        />
        {message.length > 0 && (
          <Alert
            type="error"
            color="var(--error-alert-color)"
            message={message}
            data-testid="error-alert"
          />
        )}
      </DataContainer>
    </ChangeForgottenPasswordContainer>
  );
};

export default ChangeForgottenPassword;
