import { useState } from "react";
import { Body } from "./Body";
import { DropdownContext } from "./Context";
import { Header } from "./Header";

interface DropdownProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  children: React.ReactNode | React.ReactNode[];
  isOpened?: (isOpen: boolean) => void;
  isOpen?: boolean;
}

export const Dropdown = (props: DropdownProps) => {
  const { children, style, isOpened, isOpen: externalIsOpen } = props;
  const [internalIsOpen, setInternalIsOpen] = useState(false);

  const isOpen = externalIsOpen !== undefined ? externalIsOpen : internalIsOpen;
  const setIsOpen =
    externalIsOpen !== undefined
      ? (value: boolean | ((prev: boolean) => boolean)) => {
          const newValue = typeof value === "function" ? value(isOpen) : value;
          isOpened?.(newValue);
        }
      : setInternalIsOpen;

  return (
    <DropdownContext.Provider value={{ isOpen, setIsOpen, isOpened }}>
      <div
        {...props}
        data-testid="dropdown-container"
        style={{
          ...style,
          position: "relative",
        }}
      >
        {children}
      </div>
    </DropdownContext.Provider>
  );
};

var _default = Object.assign(Dropdown, {
  Header,
  Body,
});

export default _default;
