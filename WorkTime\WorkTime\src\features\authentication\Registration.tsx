import { ChangeEvent, MouseEvent, useState } from "react";
import { useNavigate } from "react-router-dom";
import Form from "../../components/Form/Form";
import Button from "../../components/Inputs/Button";
import Textbox from "../../components/Inputs/Textbox";
import { RegisterUserDTO } from "../../models/DTOs/RegisterUserDTO";
import { registration } from "../../services/authentication/authenticationService";
import Container from "../../components/Container";
import styled from "styled-components";
import Checkbox from "../../components/Inputs/Checkbox";
import { isValidEmail } from "../../services/emailService";
import EmailBox from "../../components/Inputs/EmailBox";
import PasswordBox from "../../components/Inputs/PasswordBox";
import { PasswordStrengthType } from "../../models/Enums/PasswortStrengthType";
import { passwordStrengthCheck } from "../../services/authentication/passwordService";
import { toast } from "react-toastify";
import { translate } from "../../services/language/Translator";
import { TermsAndConditions } from "./TermsAndConditions";
import { PasswordStrengthIndicator } from "./PasswordStrengthIndicator";

const NamesContainer = styled(Container)`
  display: flex;
  justify-content: space-between;
`;

const NameContainer = styled(Container)`
  flex-basis: 49%;
  margin: 0;
  box-sizing: border-box;
`;

const RegistrationButton = styled(Button)`
  width: 100%;
`;

const PasswordStrengthIndicatorContainer = styled.div`
  color: var(--password-strength-indicator-color);
  margin-top: 0.5rem;
  text-align: center;
  min-height: 1.5em;
`;

const AcceptTermsContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
`;

const Registration = () => {
  const [user, setUser] = useState({
    callbackUrl: `${window.location.origin}/auth/confirm-email`,
  } as RegisterUserDTO);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [showStrengthHint, setShowStrengthHint] = useState(false);
  const [isRegistrationButtonActive, setIsRegistrationButtonActive] =
    useState(false);
  const [passwordStrength, setPasswordStrength] = useState(
    PasswordStrengthType.Empty
  );
  const navigate = useNavigate();

  const handleUserBlur = () => {};

  const handleUserChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newUser = {
      ...user,
      [e.currentTarget.name]: e.currentTarget.value.trim(),
    };
    setUser(newUser);

    const currentPasswordStrength = passwordStrengthCheck(newUser.password);
    setPasswordStrength(currentPasswordStrength);

    setIsRegistrationButtonActive(
      isValidEmail(newUser.email) &&
        newUser.password !== undefined &&
        newUser.password !== "" &&
        newUser.password === newUser.confirmPassword &&
        acceptTerms &&
        currentPasswordStrength >= PasswordStrengthType.Strong
    );
  };

  const handleRegistrationSubmit = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    registration(user)
      .then(() => {
        navigate(`/auth/confirmation-email-sent/${user.email}`);
      })
      .catch((error) => {
        const errorMessage =
          error === 403
            ? translate("DublicateUserNameError")
            : translate("BadRequestError");

        toast.error(errorMessage, {
          position: "top-right",
          autoClose: 10000,
          hideProgressBar: false,
          closeOnClick: true,
          pauseOnHover: true,
          draggable: true,
          progress: undefined,
        });
      });
  };

  const handleTermsCheckboxClick = () => {
    setAcceptTerms(!acceptTerms);

    setIsRegistrationButtonActive(
      isValidEmail(user.email) &&
        user.password !== undefined &&
        user.password !== "" &&
        user.password === user.confirmPassword &&
        !acceptTerms &&
        passwordStrength >= PasswordStrengthType.Strong
    );
  };

  const confirmPasswordValidation = {
    isValid:
      user.confirmPassword === undefined ||
      user.confirmPassword === "" ||
      user.password === undefined ||
      user.password === "" ||
      user.confirmPassword === user.password,
    alertMessage: translate("Passwords does not match"),
  };

  return (
    <Form data-testid="registration-form">
      <NamesContainer data-testid="names-container">
        <NameContainer data-testid="name-container">
          <Textbox
            name="firstName"
            handleChange={handleUserChange}
            label="Name"
            value={user.firstName}
            data-testid="first-name-textbox"
          />
        </NameContainer>
        <NameContainer data-testid="last-name-container">
          <Textbox
            name="lastName"
            handleChange={handleUserChange}
            label="Last name"
            value={user.lastName}
            data-testid="last-name-textbox"
          />
        </NameContainer>
      </NamesContainer>
      <EmailBox
        name="email"
        handleChange={handleUserChange}
        handleBlur={handleUserBlur}
        label="E-mail"
        placeholder="Fill your email"
        value={user.email}
        data-testid="email-box"
      />
      <PasswordBox
        name="password"
        handleChange={handleUserChange}
        handleBlur={handleUserBlur}
        label="Password"
        placeholder="Fill your password"
        value={user.password}
        type="password"
        data-testid="password-box"
      />
      <PasswordBox
        name="confirmPassword"
        handleChange={handleUserChange}
        handleBlur={handleUserBlur}
        label="Confirm Password"
        placeholder="Confirm your password"
        value={user.confirmPassword}
        type="password"
        data-testid="confirm-password-textbox"
        validation={confirmPasswordValidation}
      />
      <div
        onMouseEnter={() => setShowStrengthHint(true)}
        onMouseLeave={() => setShowStrengthHint(false)}
      >
        <PasswordStrengthIndicator
          passwordStrength={passwordStrength}
          setShowStrengthHint={() => {}}
        />
        <RegistrationButton
          data-testid="registration-button"
          onClick={handleRegistrationSubmit}
          label="Registration"
          disabled={!isRegistrationButtonActive}
        />
        <PasswordStrengthIndicatorContainer
          style={{ visibility: showStrengthHint ? "visible" : "hidden" }}
        >
          {translate("strPasswordStrenghtIndicator")}
        </PasswordStrengthIndicatorContainer>
      </div>

      <AcceptTermsContainer data-testid="accept-terms-container">
        <Checkbox
          isChecked={acceptTerms}
          handleChange={handleTermsCheckboxClick}
          label=""
          name="acceptTerms"
        />
        <TermsAndConditions headerTitle="Accept Terms" />
      </AcceptTermsContainer>
    </Form>
  );
};

export default Registration;
