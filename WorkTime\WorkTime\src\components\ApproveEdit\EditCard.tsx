import React from "react";
import styled from "styled-components";

interface EditCardProps {
  newValue: string;
  cardKey: string;
  onCancel?: () => void;
  onConfirm?: () => void;
}

const CardContainer = styled.div`
  background-color: var(--auth-button-background-color);
  border-radius: 1.5rem;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 20rem;
  box-sizing: border-box;
`;

const HeaderText = styled.p`
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--combobox-header-text-color);
  font-weight: normal;
`;

const ValueText = styled.p`
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
  color: var(--profile-department-name-font-color);
  font-weight: 500;
  line-height: 1.4;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
`;

const ActionButton = styled.button<{ variant: 'cancel' | 'confirm' }>`
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: ${props => props.variant === 'cancel' ? 'var(--error-alert-color)' : 'var(--success-alert-color)'};
  padding: 0.5rem;
  border-radius: 0.5rem;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--combobox-option-hover-background-color);
  }

  &:focus {
    outline: none;
    background-color: var(--combobox-option-hover-background-color);
  }
`;

const IconPlaceholder = styled.div<{ variant: 'cancel' | 'confirm' }>`
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  background-color: ${props => props.variant === 'cancel' ? 'var(--error-alert-color)' : 'var(--success-alert-color)'};
  opacity: 0.3;
`;

const EditCard: React.FC<EditCardProps> = ({ 
  newValue, 
  cardKey, 
  onCancel, 
  onConfirm 
}) => {
  return (
    <CardContainer data-testid="edit-card" data-key={cardKey}>
      <HeaderText>Нови данни:</HeaderText>
      <ValueText>{newValue}</ValueText>
      <ButtonContainer>
        <ActionButton 
          variant="cancel" 
          onClick={onCancel}
          data-testid="cancel-button"
        >
          <IconPlaceholder variant="cancel" />
          Откажи
        </ActionButton>
        <ActionButton 
          variant="confirm" 
          onClick={onConfirm}
          data-testid="confirm-button"
        >
          <IconPlaceholder variant="confirm" />
          Одобри
        </ActionButton>
      </ButtonContainer>
    </CardContainer>
  );
};

export default EditCard;
