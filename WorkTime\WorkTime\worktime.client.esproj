﻿<Project Sdk="Microsoft.VisualStudio.JavaScript.Sdk/0.5.425631-alpha">
  <PropertyGroup>
    <StartupCommand>npm start</StartupCommand>
    <JavaScriptTestRoot>src\</JavaScriptTestRoot>
    <JavaScriptTestFramework>Jest</JavaScriptTestFramework>
    <!-- Allows the build (or compile) script located on package.json to run on Build -->
    <ShouldRunBuildScript>false</ShouldRunBuildScript>
    <!-- Folder where production build objects will be placed -->
    <PublishAssetsDirectory>$(MSBuildProjectDirectory)\dist</PublishAssetsDirectory>
  </PropertyGroup>
  <ItemGroup>
    <Folder Include="src\models\DTOs\nomenclatures\" />
    <Folder Include="src\models\DTOs\trz\" />
    <Folder Include="src\services\trz\" />
  </ItemGroup>
</Project>