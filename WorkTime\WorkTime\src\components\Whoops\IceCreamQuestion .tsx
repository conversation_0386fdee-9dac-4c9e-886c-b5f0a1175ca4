import IceCream from "../../assets/whoops/ice-cream.gif";
import { useEffect, useState } from "react";
import { translate } from "../../services/language/Translator";
import styled from "styled-components";

const ModalOverlay = styled.div<{ isVisible: boolean }>`
  visibility: ${(p) => (p.isVisible ? "visible" : "hidden")};
  opacity: ${(p) => (p.isVisible ? "1" : "0")};
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  transition: opacity 500ms;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ModalContent = styled.div`
  background: #fff;
  border-radius: 1.5rem;
  padding: 1.25rem;
  position: relative;
  width: fit-content;
  max-width: 90vw;
  max-height: 90vh;
`;

const Title = styled.h2`
  margin-top: 0;
  margin-bottom: 1rem;
  text-align: center;
`;

const ImageWrapper = styled.div`
  text-align: center;
  padding: 1rem;
  img {
    width: 300px;
    border-radius: 1rem;
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 1rem;
`;

const Button = styled.button`
  min-width: 8.25rem;
  padding: 1rem 2rem;
  border-radius: 3.75rem;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  background: #186fc7;
  color: white;
  transition: background 0.2s;

  &:hover {
    background: #3893ef;
  }
`;

const IceCreamQuestion = () => {
  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    const checkTimeAndCount = () => {
      // Get current time
      const now = new Date();
      const hours = now.getHours();
      const minutes = now.getMinutes();
      const isAfter330PM = hours > 15 || (hours === 15 && minutes >= 30);

      // Check display count from localStorage
      const displayCount = parseInt(
        localStorage.getItem("iceCreamDisplayCount") || "0"
      );

      // Only show if it's after 3:30 PM and hasn't been shown 3 times
      if (isAfter330PM && displayCount < 3) {
        setShouldShow(true);
        // Increment the display count
        localStorage.setItem(
          "iceCreamDisplayCount",
          (displayCount + 1).toString()
        );
      }
    };

    checkTimeAndCount();
  }, []);

  const handleClose = () => {
    setShouldShow(false);
  };

  return (
    <ModalOverlay isVisible={shouldShow}>
      <ModalContent>
        <Title>{translate("strIceCreamQuestion")}</Title>
        <ImageWrapper>
          <img src={IceCream} alt="Ice cream" />
        </ImageWrapper>
        <ButtonContainer>
          <Button onClick={handleClose}>
            {translate("strGoingForIceCream")}
          </Button>
          <Button onClick={handleClose}>
            {translate("strAlreadyAteIceCream")}
          </Button>
        </ButtonContainer>
      </ModalContent>
    </ModalOverlay>
  );
};

export default IceCreamQuestion;
