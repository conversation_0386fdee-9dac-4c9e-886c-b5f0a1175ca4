import { useEffect, useState } from "react";
import Button from "../../components/Inputs/Button";
import JoinCompany from "./JoinCompany";
import styled, { css } from "styled-components";
import plusImage from "../../assets/images/side-menu-buttons/add-company-active.svg";
import plusHoverImage from "../../assets/images/side-menu-buttons/add-company.svg";
import plusDisabledImage from "../../assets/images/side-menu-buttons/add-company.svg";
import moveRightImage from "../../assets/images/side-menu-buttons/join-active.svg";
import moveRightHoverImage from "../../assets/images/side-menu-buttons/join-icon.svg";
import sendera from "../../assets/images/side-menu-buttons/sendera-active.svg";
import senderaDisabled from "../../assets/images/side-menu-buttons/sendera-icon.svg";
import moveRightDisabledImage from "../../assets/images/side-menu-buttons/join-icon.svg";
import { CompanyDTO } from "../../models/DTOs/companies/CompanyDTO";
import Container from "../../components/Container";
import { useAuth } from "../authentication/AuthContext";
import { translate } from "../../services/language/Translator";
import ImportCompany from "./ImportCompany";
import CreateCompany from "./CreateCompany";

const NoCompaniesContainer = styled(Container)`
  display: grid;
  place-items: center;
  height: 100vh;
`;

const TabsContainer = styled(Container)`
  margin-bottom: 0.625rem;
  margin-top: 3rem;
  width: clamp(20%, 32rem, 100%);
  padding: 0;
  display: flex;
  flex-direction: row;
  position: absolute;
  top: 2rem;
  left: 1rem;
`;

const ContentContainer = styled(Container)`
  width: clamp(20%, 32rem, 100%);
  height: 45rem;
  margin-top: 15%;
  overflow: auto;
`;

const CompanyButton = styled(Button)<{
  companyImage: string;
  companyHoverImage: string;
  companyDisabledImage: string;
  isDisable: boolean;
}>`
  width: 100%;
  background-color: var(--company-button-background-color);
  color: var(--company-button-color);
  font-size: 1rem;
  background-image: url(${(p) => p.companyImage});
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: 1.6rem;
  background-position: left 1rem center;
  padding: 0.7rem 0 0.7rem 3.5rem;
  text-align: left;
  cursor: default;
  min-height: 3.5rem;
  min-width: 12rem;

  ${(p) =>
    p.isDisable &&
    css`
      background-image: url(${p.companyDisabledImage});
      background-color: var(--company-button-background-color-disable);
      color: var(--company-button-color-disable);
      background-position: left 1rem center;
      background-size: 1.6rem;
      background-repeat: no-repeat;
      cursor: pointer;
    `}

  &:hover {
    background-image: url(${(p) => p.companyHoverImage});
    background-color: var(--company-button-background-color-hover);
    background-position: left 1rem center;
    background-size: 1.6rem;
    background-repeat: no-repeat;
    cursor: pointer;
  }

  @media (max-width: 760px) {
    padding: 1rem 1rem 1rem 3rem;
  }
`;

enum TabItems {
  Import = "import",
  Create = "create",
  Join = "join",
}

const CompaniesSideMenuIndex = () => {
  const [company, setCompany] = useState({} as CompanyDTO);
  const { user } = useAuth();
  const isEmployee = user.workTimeRoleName === "grEmployee";
  const [activeTab, setActiveTab] = useState("import");

  useEffect(() => {
    if (Object.keys(company || {}).length === 0) {
      setCompany({
        name: translate("Import companies"),
        bulstat: "",
        contactName: "",
        userRegistrationCompanyId: 0,
        id: "",
      });
    }
  }, [company]);

  const handleSetTab = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <NoCompaniesContainer data-testid="no-companies-container">
      <TabsContainer>
        <CompanyButton
          data-testid="import-company-tab"
          label="Import"
          companyImage={sendera}
          companyHoverImage={sendera}
          companyDisabledImage={senderaDisabled}
          onClick={() => handleSetTab(TabItems.Import)}
          isDisable={activeTab !== TabItems.Import}
        >
          <img
            src={sendera}
            alt="Import"
            style={{ width: 24, height: 24 }}
            data-img={sendera}
          />
          Import
        </CompanyButton>
        {!isEmployee && (
          <CompanyButton
            data-testid="create-company-tab"
            label="Create"
            companyImage={plusImage}
            companyHoverImage={plusHoverImage}
            companyDisabledImage={plusDisabledImage}
            onClick={() => handleSetTab(TabItems.Create)}
            isDisable={activeTab !== TabItems.Create}
          >
            <img
              src={plusImage}
              alt="Create"
              style={{ width: 24, height: 24 }}
              data-img={plusImage}
            />
            Create
          </CompanyButton>
        )}
        <CompanyButton
          data-testid="join-company-tab"
          label="Join company"
          companyImage={moveRightImage}
          companyHoverImage={moveRightHoverImage}
          companyDisabledImage={moveRightDisabledImage}
          onClick={() => handleSetTab(TabItems.Join)}
          isDisable={activeTab !== TabItems.Join}
        >
          <img
            src={moveRightImage}
            alt="Join"
            style={{ width: 24, height: 24 }}
            data-img={moveRightImage}
          />
          Join company
        </CompanyButton>
      </TabsContainer>
      <ContentContainer data-testid="content-container">
        {activeTab === TabItems.Import && <ImportCompany />}
        {activeTab === TabItems.Create && (
          <CreateCompany data-testid="initial-create-company" />
        )}
        {activeTab === TabItems.Join && (
          <JoinCompany data-testid="join-company" />
        )}
      </ContentContainer>
    </NoCompaniesContainer>
  );
};

export default CompaniesSideMenuIndex;
