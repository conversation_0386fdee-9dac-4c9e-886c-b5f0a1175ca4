import React, { ChangeEvent, useEffect, useState } from "react";
import styled from "styled-components";
import Search from "../../assets/images/search/search-icon.svg";
import SearchHover from "../../assets/images/search/search-icon-hover.svg";
import Container from "../Container";
import Textbox from "../Inputs/Textbox";

interface BaseFilterProps
  extends React.DetailedHTMLProps<
    React.TableHTMLAttributes<HTMLTableElement>,
    HTMLTableElement
  > {
  column: string;
}

const SearchInputWrapper = styled(Container)`
  position: relative;
  width: 100%;
`;

const SearchInput = styled(Textbox)`
  text-align: left;
  opacity: 1;
  font-family: Segoe UI;
  font-style: normal;
  font-weight: 400;
  border-radius: 1.625rem;
  border: 0.063rem;
  &:focus {
    outline: none;
    box-shadow: 0rem 0rem 0.125rem var(--search-input-box-shadow-color);
  }

  min-width: 8rem;
`;

const SearchIconStyled = styled.img`
  position: absolute;
  top: 1rem;
  right: 0.45rem;
  width: 1.25rem;
  height: 1.25rem;
  pointer-events: auto;
  transition: filter 0.2s ease-in-out;
  cursor: pointer;
`;

const SearchDiv = styled(Container)`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0.313rem 0rem;
`;

interface BaseFilterProps {
  column: string;
  placeholder: string;
  onFilterChange?: (column: string, value: string) => void;
  showSort?: boolean;
  value?: string;
}

const BaseFilter = ({
  column,
  placeholder,
  onFilterChange,
  value = "",
}: BaseFilterProps): JSX.Element => {
  const [searchValue, setSearchValue] = useState(value);
  const [isHovered, setIsHovered] = useState(false);

  const onSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    onFilterChange?.(column, value);
  };

  useEffect(() => {
    setSearchValue(value);
  }, [value]);

  return (
    <SearchDiv data-testid="base-filter-container">
      <SearchInputWrapper data-testid="search-input-wrapper">
        <SearchInput
          data-testid="search-input"
          label={placeholder}
          value={searchValue}
          handleChange={onSearchChange}
        />
        <SearchIconStyled
          data-testid="search-icon"
          src={isHovered ? SearchHover : Search}
          alt="search-icon"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        />
      </SearchInputWrapper>
    </SearchDiv>
  );
};

export default BaseFilter;
