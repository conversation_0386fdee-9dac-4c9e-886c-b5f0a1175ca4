import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
} from "react";

interface EmployeeSelectionContextType {
  selectedEmployeePayrollIds: string[];
  selectEmployee: (employeePayrollId: string, isSelected: boolean) => void;
  selectAllEmployees: (
    employeePayrollIds: string[],
    isSelected: boolean
  ) => void;
  isAllEmployeesSelected: (employeePayrollIds: string[]) => boolean;
  clearSelection: () => void;
}

const EmployeeSelectionContext = createContext<
  EmployeeSelectionContextType | undefined
>(undefined);

interface EmployeeSelectionProviderProps {
  children: ReactNode;
}

export const EmployeeSelectionProvider: React.FC<
  EmployeeSelectionProviderProps
> = ({ children }) => {
  const [selectedEmployeePayrollIds, setSelectedEmployeePayrollIds] = useState<
    string[]
  >([]);

  const selectEmployee = useCallback(
    (employeePayrollId: string, isSelected: boolean) => {
      setSelectedEmployeePayrollIds((prev) => {
        if (isSelected) {
          return prev.includes(employeePayrollId)
            ? prev
            : [...prev, employeePayrollId];
        } else {
          return prev.filter((id) => id !== employeePayrollId);
        }
      });
    },
    []
  );

  const selectAllEmployees = useCallback(
    (employeePayrollIds: string[], isSelected: boolean) => {
      setSelectedEmployeePayrollIds(isSelected ? employeePayrollIds : []);
    },
    []
  );

  const isAllEmployeesSelected = useCallback(
    (employeePayrollIds: string[]) => {
      return employeePayrollIds.every((id) =>
        selectedEmployeePayrollIds.includes(id)
      );
    },
    [selectedEmployeePayrollIds]
  );

  const clearSelection = useCallback(() => {
    setSelectedEmployeePayrollIds([]);
  }, []);

  const value: EmployeeSelectionContextType = {
    selectedEmployeePayrollIds: selectedEmployeePayrollIds,
    selectEmployee,
    selectAllEmployees,
    isAllEmployeesSelected,
    clearSelection,
  };

  return (
    <EmployeeSelectionContext.Provider value={value}>
      {children}
    </EmployeeSelectionContext.Provider>
  );
};

export const useEmployeeSelection = (): EmployeeSelectionContextType => {
  const context = useContext(EmployeeSelectionContext);
  if (context === undefined) {
    throw new Error("Error in selectionContext");
  }
  return context;
};
