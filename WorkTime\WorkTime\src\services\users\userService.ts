import {
  LOCAL_STORAGE_COMPANY_ID,
  LOCAL_STORAGE_USER_ID,
} from "../../constants/local-storage-constants";
import { authenticatedGet } from "../worktimeConnectionService";
import { UserEmployeeDTO } from "../../models/DTOs/users/UserEmployeeDTO";
import { UserDTO } from "../../models/DTOs/users/UserDTO";

export const initUserEmployee = async (): Promise<UserEmployeeDTO> => {
  const companyId = localStorage.getItem(LOCAL_STORAGE_COMPANY_ID) ?? "";
  const userId = localStorage.getItem(LOCAL_STORAGE_USER_ID) ?? "";

  var userEmployeeDb = await getUserEmployee(userId, companyId);
  if (userId && userEmployeeDb)
    return {
      userId: userId,
      name: userEmployeeDb.name ?? "",
      payrolls: userEmployeeDb.payrolls ?? [],
      employeeId: userEmployeeDb.employeeId ?? "",
      permissions: userEmployeeDb.permissions ?? [],
    };

  return {
    userId: "",
    employeeId: "",
    name: "",
    payrolls: [],
    permissions: [],
  };
};

export const getUser = async () => {
  return await authenticatedGet<UserDTO>(`user`);
};

export const getUserEmployee = async (userId: string, companyId: string) => {
  if (!userId || userId === "" || !companyId || companyId === "")
    return undefined;

  return await authenticatedGet<UserEmployeeDTO>(
    `user-employee-permissions/${userId}/${companyId}`
  );
};
