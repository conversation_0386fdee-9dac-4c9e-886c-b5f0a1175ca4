import "./App.css";
import { LanguageProvider } from "./services/language/LanguageProvider";
import AppRouter from "./AppRouter";
import Layout from "./features/Layout";
import { AuthProvider } from "./features/authentication/AuthContext";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { CompanyProvider } from "./features/companies/CompanyContext";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { PermissionsProvider } from "./features/authorization/PermissionsProvider";
import { MenuProvider } from "./features/MenuContext";
import { EnumProvider } from "./features/EnumContext";
import { NomenclatureWithDescriptionProvider } from "./features/NomenclatureWithDescription";
import { DefaultLocationDataProvider } from "./features/DefaultLocationDataContext";
import { UserEmployeeProvider } from "./features/UserEmployeeContext";
import { SignalRProvider } from "./features/signalR/SignalRProvider";
import { ModalProvider } from "./components/PopUp/ActionModalContext";
import IceCreamQuestion from "./components/Whoops/IceCreamQuestion ";
import { AbsenceProvider } from "./features/absences/AbsenceContext";
import { NotificationProvider } from "./features/notifications/NotificationContext";

function App() {
  return (
    <DefaultLocationDataProvider>
      <NomenclatureWithDescriptionProvider>
        <EnumProvider>
          <AuthProvider>
            <SignalRProvider>
              <CompanyProvider>
                <UserEmployeeProvider>
                  <PermissionsProvider>
                    <GoogleOAuthProvider
                      clientId={import.meta.env.VITE_GOOGLE_ID ?? ""}
                    >
                      <MenuProvider>
                        <LanguageProvider>
                          <NotificationProvider>
                            <ModalProvider>
                              <AbsenceProvider>
                                <div className="App">
                                  <Layout />
                                  <AppRouter />
                                  <ToastContainer />
                                  <IceCreamQuestion />
                                </div>
                              </AbsenceProvider>
                            </ModalProvider>
                          </NotificationProvider>
                        </LanguageProvider>
                      </MenuProvider>
                    </GoogleOAuthProvider>
                  </PermissionsProvider>
                </UserEmployeeProvider>
              </CompanyProvider>
            </SignalRProvider>
          </AuthProvider>
        </EnumProvider>
      </NomenclatureWithDescriptionProvider>
    </DefaultLocationDataProvider>
  );
}

export default App;
