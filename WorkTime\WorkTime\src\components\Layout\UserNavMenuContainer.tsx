interface ContainerProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  children: React.ReactNode | React.ReactNode[];
}

export const UserNavMenuContainer = ({ children, ...rest }: ContainerProps) => {
  return (
    <div
      {...rest}
      data-testid="user-nav-menu-container"
      style={{
        ...rest.style,
        position: "relative",
        float: "right",
        marginLeft: "auto",
        marginRight: "1rem",
      }}
    >
      {children}
    </div>
  );
};
