import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import MainWindowContainer from "../../components/MainWindowContainer";
import Avatar from "../../components/Table/Avatar";
import { Direction } from "../../components/Table/Direction";
import Table, { ColumnDefinitionType } from "../../components/Table/Table";
import { EmployeePayrollDTO } from "../../models/DTOs/payrolls/EmployeePayrollDTO";
import { useCompany } from "../companies/CompanyContext";
import {
  onStructureLevelsLoaded,
  selectStructureLevels,
} from "../company-structure/companyStructureActions";
import { useEnums } from "../EnumContext";
import {
  onEmployeePayrollsLoaded,
  selectEmployeePayrolls,
} from "../payroll/employeePayrollActions";
import {
  filterEmployees,
  findDepartmentById,
  sortEmployees,
} from "./employeeListUtils";
import { BaseEmployeeView } from "./EmployeeViewInterfaces";
import TableHeaderExtended from "./TableHeaderExtended";
import { mapDepartmentToNestedOption } from "../../services/departments/departmentService";

const EmployeesListView = () => {
  const dispatch = useAppDispatch();
  const { payrollCategories, appointmentTypes } = useEnums();
  const employeePayrolls = useAppSelector(selectEmployeePayrolls)
    .employeePayrolls as EmployeePayrollDTO[];
  const { company } = useCompany();
  const structureLevels = useAppSelector(selectStructureLevels).structureLevels;

  const [sort, setSort] = useState<{ [key: string]: number }>({});
  const [departmentsOptions, setDepartmentsOptions] = useState<any[]>([]);
  const [mappedEmployees, setMappedEmployees] = useState<BaseEmployeeView[]>(
    []
  );

  const [filteredEmployees, setFilteredEmployees] = useState<
    BaseEmployeeView[]
  >([]);
  const navigate = useNavigate();

  const [filters, setFilters] = useState({
    personalData: "",
    position: "",
    departments: [] as string[],
    categories: [] as string[],
    typeOfAppointment: [] as string[],
  });

  const categoriesOptions = payrollCategories.map((category) => ({
    identifier: category.identifier.toString(),
    name: category.name,
    description: category.name,
  }));

  const typesOfAppointmentOptions = appointmentTypes.map((type) => ({
    identifier: type.identifier.toString(),
    name: type.name,
    description: type.name,
  }));

  const rowColumns: ColumnDefinitionType<
    BaseEmployeeView,
    keyof BaseEmployeeView
  >[] = [
    { key: "marker", value: "" },
    { key: "fullName", value: "strName" },
    { key: "egn", value: "strEgn" },
    { key: "position", value: "strPosition" },
    { key: "department", value: "strDepartment" },
    { key: "typeOfAppointment", value: "strTypeOfAppointment" },
    { key: "number", value: "№" },
  ];

  useEffect(() => {
    dispatch(onEmployeePayrollsLoaded(company.id));
    dispatch(onStructureLevelsLoaded(company.id));
  }, [dispatch, company.id]);

  useEffect(() => {
    if (structureLevels !== undefined) {
      setDepartmentsOptions(structureLevels.map(mapDepartmentToNestedOption));
    }
  }, [structureLevels]);

  useEffect(() => {
    const mapped = mapToEmployeeListView(employeePayrolls);
    setMappedEmployees(mapped);
    onSortChange("fullName", Direction.Ascending);
  }, [departmentsOptions, employeePayrolls]);

  useEffect(() => {
    const filteredEmployees = filterEmployees(
      mappedEmployees,
      filters,
      departmentsOptions,
      categoriesOptions,
      typesOfAppointmentOptions
    );
    const sortedEmployees = sortEmployees(filteredEmployees, sort);
    setFilteredEmployees(sortedEmployees);
  }, [filters, sort, mappedEmployees]);

  const onTextFilterChange = (columnKey: string, searchText: string) => {
    setFilters((prev) => ({
      ...prev,
      [columnKey]: searchText,
    }));
  };

  const onFilterChange = (
    filterType: "departments" | "categories" | "typeOfAppointment",
    selected: string[]
  ) => {
    setFilters((prev) => ({
      ...prev,
      [filterType]: selected,
    }));
  };

  const onSortChange = (columnKey: string, direction: Direction) => {
    const directionValue = direction === Direction.Ascending ? 1 : -1;
    setSort({ [columnKey]: directionValue });
  };

  const onRowClick = (row: BaseEmployeeView) => {
    navigate(`${row.workTimeId}/${row.payrollId}`);
  };

  const mapToEmployeeListView = (
    employees: EmployeePayrollDTO[]
  ): BaseEmployeeView[] => {
    const employeeListView = employees.map((employeePayroll) => {
      const departmentInfo = findDepartmentById(
        departmentsOptions,
        employeePayroll.structureId
      );

      return {
        id: employeePayroll.employeeGuid,
        workTimeId: employeePayroll.employeeGuid,
        payrollId: employeePayroll.payrollId,
        marker: (
          <Avatar
            name={employeePayroll.firstName}
            photo={""}
            size={2}
            data-testid={`employee-avatar-${employeePayroll.employeeGuid}`}
          />
        ),
        fullName: [
          employeePayroll.firstName,
          employeePayroll.secondName,
          employeePayroll.lastName,
        ]
          .filter((name) => name != null)
          .join(" "),
        egn: employeePayroll.egn,
        email: employeePayroll.email,
        position: employeePayroll.positionName,
        departmentId: departmentInfo?.identifier || "",
        department: departmentInfo?.name || "",
        contractType: employeePayroll.contractNumber,
        category: employeePayroll.category,
        typeOfAppointment: employeePayroll.typeOfAppointment,
        number: employeePayroll.contractNumber || "",
      };
    });

    return employeeListView;
  };

  return (
    <MainWindowContainer data-testid="employees-list-container">
      <Table
        data-testid="employees-table"
        onRowClick={onRowClick}
        data={filteredEmployees}
        columns={rowColumns}
        onSortChange={
          onSortChange as (columnKey: string, direction: Direction) => void
        }
        headerComponent={
          <TableHeaderExtended
            data-testid="table-header-extended"
            departmentOptions={departmentsOptions}
            categoryOptions={categoriesOptions}
            typeOfAppointmentOptions={typesOfAppointmentOptions}
            selectedDepartments={filters.departments}
            selectedCategories={filters.categories}
            selectedTypeOfAppointment={filters.typeOfAppointment}
            onDepartmentChange={(selected) =>
              onFilterChange("departments", selected)
            }
            onCategoryChange={(selected) =>
              onFilterChange("categories", selected)
            }
            onTypeOfAppointmentChange={(selected) =>
              onFilterChange("typeOfAppointment", selected)
            }
            onTextFilterChange={onTextFilterChange}
          />
        }
      />
    </MainWindowContainer>
  );
};

export default EmployeesListView;
