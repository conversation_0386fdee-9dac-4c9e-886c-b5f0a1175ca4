import { useContext } from "react";
import { LanguageContext, languageContextValue } from "./LanguageContext";

interface Props {
  getString: string;
  uppercase?: boolean;
}

const Translator = ({ getString, uppercase }: Props) => {
  const languageContext = useContext(LanguageContext);
  return uppercase
    ? languageContext.dictionary[getString]?.toUpperCase() || getString
    : languageContext.dictionary[getString] || getString;
};

export const translate = (
  getString: string | number,
  ...args: (string | number)[]
) => {
  const str =
    languageContextValue.dictionary[getString.toString()] ||
    getString.toString();

  return str.replace(/{(\d+)}/g, function (match: string, number: number) {
    return typeof args[number] != "undefined" ? args[number] : match;
  });
};

export default Translator;
