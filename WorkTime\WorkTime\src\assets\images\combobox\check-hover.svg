<svg id="check_hover" data-name="check hover" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20.56" height="19.781" viewBox="0 0 20.56 19.781">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_162" data-name="Rectangle 162" width="19.787" height="19.781" fill="#d9e7f5"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <rect id="Rectangle_163" data-name="Rectangle 163" width="16.56" height="15.022" fill="#d9e7f5"/>
    </clipPath>
  </defs>
  <circle id="white" cx="9" cy="9" r="9" transform="translate(1 1)" fill="#fff"/>
  <g id="base" clip-path="url(#clip-path)">
    <path id="Path_1035" data-name="Path 1035" d="M19.787,9.9A9.885,9.885,0,1,1,4.41,1.712a9.651,9.651,0,0,1,10.184-.5c.067.034.133.072.2.112a.607.607,0,0,1,.25.857A.6.6,0,0,1,14.2,2.4a9.665,9.665,0,0,0-2.718-.993A8.653,8.653,0,1,0,17.25,14.456a8.506,8.506,0,0,0,.905-7.109c-.146-.5-.011-.849.371-.953a.632.632,0,0,1,.774.458A10.229,10.229,0,0,1,19.787,9.9" transform="translate(0 0)" fill="#d9e7f5"/>
  </g>
  <g id="check_hover-2" data-name="check hover" transform="translate(4)">
    <g id="Group_976" data-name="Group 976" clip-path="url(#clip-path-2)">
      <path id="Path_1036" data-name="Path 1036" d="M4.66,15.02a1.343,1.343,0,0,1-1.137-.554A56.009,56.009,0,0,1,.143,9.577.8.8,0,0,1,.277,8.512a.8.8,0,0,1,1.073.006C2.227,9.2,3.114,9.869,4.031,10.5a.419.419,0,0,0,.611-.02A107.388,107.388,0,0,0,14.685.93c.2-.212.4-.423.6-.63A.712.712,0,0,1,16.3.152a.709.709,0,0,1,.092,1.016c-.95,1.385-1.893,2.775-2.87,4.14a66.088,66.088,0,0,1-7.59,9.037,1.7,1.7,0,0,1-1.271.676" transform="translate(0 0)" fill="#d9e7f5"/>
    </g>
  </g>
</svg>
