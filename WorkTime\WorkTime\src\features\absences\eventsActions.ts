import { Action, Reducer } from "redux";
import { AppThunk, ClearStateAction, RootState } from "../../app/store";
import { TRZEvents } from "../../models/DTOs/trz/TRZEventsDTO";
import {
  authenticatedGet,
  authenticatedPost,
} from "../../services/connectionService";
import { ImportTRZEventRequest } from "../../models/Requests/ImportTRZEventRequest";
import { AbsenceTypeDTO } from "../../models/DTOs/nomenclatures/AbsenceTypeDTO";
import { HospitalTypeDTO } from "../../models/DTOs/nomenclatures/HospitalTypeDTO";
import { toast } from "react-toastify";

interface EventsState {
  trzEvents: TRZEvents[];
  trzEvent: TRZEvents;
  absenceTypes: AbsenceTypeDTO[];
  hospitalTypes: HospitalTypeDTO[];
}

interface LoadTRZEventsAction {
  type: "LOAD_TRZEvents";
  trzEvents: TRZEvents[];
}

interface ImportTRZEventsAction {
  type: "IMPORT_TRZEvent";
  trzEvent: TRZEvents;
}

interface LoadAbsenceTypesAction {
  type: "LOAD_AbsenceTypes";
  absenceTypes: AbsenceTypeDTO[];
}

interface LoadHospitalTypesAction {
  type: "LOAD_HospitalTypes";
  hospitalTypes: HospitalTypeDTO[];
}

type KnownActions =
  | LoadTRZEventsAction
  | ImportTRZEventsAction
  | LoadAbsenceTypesAction
  | LoadHospitalTypesAction
  | ClearStateAction;

const loadTrzEventsAction = (trzEvents: TRZEvents[]): LoadTRZEventsAction => ({
  type: "LOAD_TRZEvents",
  trzEvents,
});

const importTrzEventAction = (trzEvent: TRZEvents): ImportTRZEventsAction => ({
  type: "IMPORT_TRZEvent",
  trzEvent,
});

const loadAbsenceTypesAction = (
  absenceTypes: AbsenceTypeDTO[]
): LoadAbsenceTypesAction => ({
  type: "LOAD_AbsenceTypes",
  absenceTypes,
});

const loadHospitalTypesAction = (
  hospitalTypes: HospitalTypeDTO[]
): LoadHospitalTypesAction => ({
  type: "LOAD_HospitalTypes",
  hospitalTypes,
});

export const actionCreators = {
  onTRZEventsLoaded: (payrollId: string): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedGet<TRZEvents[]>(`trz/events?payrollId=${payrollId}`).then(
        (trzEvents) => {
          dispatch(loadTrzEventsAction(trzEvents));
        }
      );
    };
  },
  onTRZEventImported: (trzEvent: TRZEvents): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      const importEventRequest = {
        trzEvent: trzEvent,
      } as ImportTRZEventRequest;
      authenticatedPost<TRZEvents>("trz/event", importEventRequest)
        .then(() => {
          dispatch(importTrzEventAction(trzEvent));
          toast.success("Successful event import!");
        })
        .catch((error) => {
          console.error(error);
        });
    };
  },
  onAbsenceTypesLoaded: (): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedGet<AbsenceTypeDTO[]>(`absence-types`).then(
        (absenceTypes) => {
          dispatch(loadAbsenceTypesAction(absenceTypes));
        }
      );
    };
  },
  onHospitalTypesLoaded: (): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      authenticatedGet<HospitalTypeDTO[]>(`hospital-types`).then(
        (hospitalTypes) => {
          dispatch(loadHospitalTypesAction(hospitalTypes));
        }
      );
    };
  },
};

export const {
  onTRZEventsLoaded,
  onTRZEventImported,
  onAbsenceTypesLoaded,
  onHospitalTypesLoaded,
} = actionCreators;

const initialState = {
  trzEvents: [],
  trzEvent: {} as TRZEvents,
  absenceTypes: [],
  hospitalTypes: [],
} as EventsState;

export const reducer: Reducer<EventsState> = (
  state = initialState,
  action: Action
) => {
  var incomingAction = action as KnownActions;
  switch (incomingAction.type) {
    case "LOAD_TRZEvents":
      return {
        ...state,
        trzEvents: [...incomingAction.trzEvents],
      };
    case "IMPORT_TRZEvent":
      var eventId = incomingAction.trzEvent.id;
      return {
        ...state,
        trzEvents: [...state.trzEvents.filter((e) => e.id !== eventId)],
      };
    case "LOAD_AbsenceTypes":
      return {
        ...state,
        absenceTypes: [...incomingAction.absenceTypes],
      };
    case "LOAD_HospitalTypes":
      return {
        ...state,
        hospitalTypes: [...incomingAction.hospitalTypes],
      };
    case "CLEAR_STATE":
      return initialState;
    default:
      return state;
  }
};

export const selectTRZEvents = (state: RootState) => state.trzEvents;
