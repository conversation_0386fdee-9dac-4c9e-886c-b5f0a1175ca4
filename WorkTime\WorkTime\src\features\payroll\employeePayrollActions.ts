import { Action, Reducer } from "redux";
import { AppThunk, ClearStateAction, RootState } from "../../app/store";
import { EmployeePayrollDTO } from "../../models/DTOs/payrolls/EmployeePayrollDTO";
import { getEmployeePayrolls } from "../../services/employees/employeePayrollsService";

interface EmployeePayrollsState {
  employeePayrolls: EmployeePayrollDTO[];
}

type KnownActions =
  | LoadEmployeePayrollsAction
  | ClearStateAction
  | AddEmployeePayrollAction;

interface AddEmployeePayrollAction {
  type: "ADD_EMPLOYEE_PAYROLL";
  employeePayroll: EmployeePayrollDTO;
}

interface LoadEmployeePayrollsAction {
  type: "LOAD_EMPLOYEE_PAYROLLS";
  employeePayrolls: EmployeePayrollDTO[];
}

export const loadEmployeePayrollsAction = (
  employeePayrolls: EmployeePayrollDTO[]
): LoadEmployeePayrollsAction => ({
  type: "LOAD_EMPLOYEE_PAYROLLS",
  employeePayrolls,
});

export const addEmployeePayrollAction = (
  employeePayroll: EmployeePayrollDTO
): AddEmployeePayrollAction => ({
  type: "ADD_EMPLOYEE_PAYROLL",
  employeePayroll,
});

export const actionCreators = {
  onEmployeePayrollsLoaded: (
    companyId: string
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      companyId &&
        getEmployeePayrolls(companyId).then((employeePayrolls) => {
          dispatch(loadEmployeePayrollsAction(employeePayrolls));
        });
    };
  },
  onEmployeePayrollAdded: (
    employeePayroll: EmployeePayrollDTO
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(addEmployeePayrollAction(employeePayroll));
    };
  },
};

export const { onEmployeePayrollsLoaded, onEmployeePayrollAdded } =
  actionCreators;

const initialState = {
  employeePayrolls: [],
} as EmployeePayrollsState;

export const reducer: Reducer<EmployeePayrollsState> = (
  state = initialState,
  action: Action
) => {
  var incomingAction = action as KnownActions;
  switch (incomingAction.type) {
    case "LOAD_EMPLOYEE_PAYROLLS":
      return {
        ...state,
        employeePayrolls: [...incomingAction.employeePayrolls],
      };
    case "ADD_EMPLOYEE_PAYROLL":
      return {
        ...state,
        employeePayrolls: [
          ...state.employeePayrolls,
          incomingAction.employeePayroll,
        ],
      };
    case "CLEAR_STATE":
      return initialState;
    default:
      return state;
  }
};

export const selectEmployeePayrolls = (state: RootState) =>
  state.employeePayrolls;
