import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";
import { ICitiesType } from "../models/DTOs/enums/ICitiesType";
import { IDistrictType } from "../models/DTOs/enums/IDistrictType";
import { IMunicipalityType } from "../models/DTOs/enums/IMunicipalityType";
import {
  initCities,
  initDistricts,
  initMunicipalities,
} from "../services/nomenclatures/defaultLocationDataService";

interface IDefaultLocationDataContext {
  districts: IDistrictType[];
  municipalities: IMunicipalityType[];
  cities: ICitiesType[];
  // more enums
}

interface DefaultLocationDataProps {
  children: ReactNode;
}

const DefaultLocationDataContext = createContext<IDefaultLocationDataContext>({
  districts: [],
  municipalities: [],
  cities: [],
});

export const DefaultLocationDataProvider: React.FC<
  DefaultLocationDataProps
> = ({ children }) => {
  const [enums, setEnums] = useState<IDefaultLocationDataContext>({
    districts: [],
    municipalities: [],
    cities: [],
    //more enums
  });

  useEffect(() => {
    initDistricts().then((districts) => {
      setEnums((enums) => ({ ...enums, districts }));
    });
    initMunicipalities().then((municipalities) => {
      setEnums((enums) => ({ ...enums, municipalities }));
    });
    initCities().then((cities) => {
      setEnums((enums) => ({ ...enums, cities }));
    });
    //more enum fetch
  }, []);

  return (
    <DefaultLocationDataContext.Provider value={enums}>
      {children}
    </DefaultLocationDataContext.Provider>
  );
};

export const useDefaultPlaces = () => useContext(DefaultLocationDataContext);
