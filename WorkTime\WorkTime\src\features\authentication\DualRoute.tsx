import React, { useEffect, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { isAuthenticated } from "../../services/authentication/authenticationService";
import { useAuth } from "./AuthContext";
import { useAppSelector } from "../../app/hooks";
import { companiesState } from "../companies/companiesActions";

interface Props {
  authenticatedRoute: React.ReactNode | React.ReactNode[];
  nonAuthenticatedRoute: React.ReactNode | React.ReactNode[];
}

const DualRoute = ({ authenticatedRoute, nonAuthenticatedRoute }: Props) => {
  const { user, isLoading } = useAuth();
  const [authenticated, setAuthenticated] = useState(true);
  const [isLoaded, setIsLoaded] = useState(false);
  const path = useLocation();
  const { activeCompanies, pendingCompanies } = useAppSelector(companiesState);

  const needsCompanySetup =
    (activeCompanies === undefined || activeCompanies.length === 0) &&
    (pendingCompanies === undefined || pendingCompanies.length === 0) &&
    (user.workTimeRoleName === undefined || user.workTimeRoleName === "");

  useEffect(() => {
    const authed = isAuthenticated();
    setAuthenticated(authed);
    setIsLoaded(true);
  }, [setAuthenticated]);

  return (
    <>
      {isLoaded && !isLoading ? (
        authenticated ? (
          user.hasSignedIn ? (
            needsCompanySetup && path.pathname !== "/" ? (
              <Navigate
                to="/"
                state={{ next: `${path.pathname}${path.search}` }}
              />
            ) : (
              authenticatedRoute
            )
          ) : path.pathname.includes(`/auth/change-password-code`) ? (
            authenticatedRoute
          ) : (
            <Navigate
              to={`/auth/change-password-code?returnAfterPasswordChanged=${encodeURIComponent(
                `${path.pathname}${path.search}`
              )}`}
            />
          )
        ) : (
          nonAuthenticatedRoute
        )
      ) : (
        // ���� ���� �� �� ����� ������� Spinner / Loader etc
        <></>
      )}
    </>
  );
};

export default DualRoute;
