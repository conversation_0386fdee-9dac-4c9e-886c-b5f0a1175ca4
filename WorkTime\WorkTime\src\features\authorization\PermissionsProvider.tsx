import { useEffect, useState } from "react";
import {
  ICompanyPermission,
  PermissionsContext,
  PermissionsMap,
} from "./PermissionsContext";
import { LOCAL_STORAGE_REFRESH_TOKEN } from "../../constants/local-storage-constants";

interface Props {
  children: React.ReactNode | React.ReactNode[];
}

export const PermissionsProvider = ({ children }: Props) => {
  const [permissions, setPermissions] = useState<PermissionsMap>();

  useEffect(() => {
    const setUserPermissions = () => {
      try {
        const token = localStorage.getItem(LOCAL_STORAGE_REFRESH_TOKEN);
        if (!token) return;

        const payload = token.split(".")[1];

        const decodedPayload = JSON.parse(window.atob(payload));

        const permissions = decodedPayload.Permissions;
        if (permissions === undefined) return;

        const companyPermissionsArray: ICompanyPermission[] =
          JSON.parse(permissions);

        const companyPermissionsMap: PermissionsMap = {};

        for (let cp of companyPermissionsArray) {
          companyPermissionsMap[cp.CompanyId] = cp.Permissions;
        }

        setPermissions(companyPermissionsMap);
      } catch (ex) {
        console.log(ex);
      }
    };

    setUserPermissions();

    window.addEventListener("storage", (e) => {
      if (e.key === LOCAL_STORAGE_REFRESH_TOKEN) {
        setUserPermissions();
      }
    });

    return () => {
      window.removeEventListener("storage", setUserPermissions);
    };
  }, []);

  return (
    <PermissionsContext.Provider value={{ permissions, setPermissions }}>
      {children}
    </PermissionsContext.Provider>
  );
};
