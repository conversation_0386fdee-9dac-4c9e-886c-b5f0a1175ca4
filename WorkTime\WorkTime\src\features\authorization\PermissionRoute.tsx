import { useContext, useEffect, useState } from "react";
import { isAuthenticated } from "../../services/authentication/authenticationService";
import { useCompany } from "../companies/CompanyContext";
import { PermissionsContext } from "./PermissionsContext";
import { isUserPermitted } from "../../services/authorization/authorizationService";

interface Props {
  children: React.ReactNode | React.ReactNode[];
  requiredPermissions: string[];
}

const PermissionRoute = ({ children, requiredPermissions }: Props) => {
  const [allowed, setAllowed] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  const { company } = useCompany();
  const { permissions } = useContext(PermissionsContext);

  useEffect(() => {
    const authed = isAuthenticated();
    if (authed) {
      setAllowed(isUserPermitted(requiredPermissions, permissions, company));
    }
    setIsLoaded(true);
  }, [setAllowed]);

  return <>{isLoaded ? allowed ? children : <></> : <></>}</>;
};

export default PermissionRoute;
