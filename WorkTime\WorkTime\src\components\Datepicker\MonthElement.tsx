import React from "react";
import styled from "styled-components";
import Translator from "../../services/language/Translator";
import { ViewMode } from "../../models/Enums/ViewMode";
import { monthsShort } from "../CalendarComponent/constants/Names";

const ContainerMonth = styled.div`
  display: flex;
  position: relative;
  justify-content: space-evenly;
  align-items: center;
  cursor: pointer;

  &::after,
  &::before {
    content: "";
    position: absolute;
    background-color: var(--datepicker-view-buttons-color);
    z-index: 999;
    pointer-events: none;
  }

  &::after {
    top: -0.05em;
    width: 50vw;
    height: 0.05em;
  }

  &::before {
    left: -0.1em;
    width: 0.05em;
    height: 50vh;
  }

  &:hover {
    background-color: var(--datepicker-view-buttons-hover-color);
  }
`;

const OutliningContainer = styled.div<{ active: ViewMode }>`
  display: none;
  padding: 0.45em;
  background-color: white;
  width: 15em;
  border-radius: 0em 0em 1em 1em;
  ${({ active }) =>
    active === ViewMode.MonthsView &&
    `
    display: block;
  `};
`;

const ContainerMonths = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(3, 1fr);

  border-color: var(--datepicker-view-buttons-color);
  background-color: var(--datepicker-view-border);

  height: 11.6rem;
  overflow: hidden;
`;

interface MonthsElementProps {
  handleMonthsClick: (selectedMonth: number) => void;
  active: ViewMode;
}

const MonthsElement: React.FC<MonthsElementProps> = ({
  handleMonthsClick,
  active,
}) => {
  return (
    <OutliningContainer data-testid="month-element-container" active={active}>
      <ContainerMonths data-testid="months-grid">
        {monthsShort.map((month, index) => (
          <ContainerMonth
            data-testid={`month-button-${month}`}
            key={`monts-${month}`}
            onClick={() => handleMonthsClick(index)}
          >
            <Translator getString={month} />
          </ContainerMonth>
        ))}
      </ContainerMonths>
    </OutliningContainer>
  );
};

export default MonthsElement;
