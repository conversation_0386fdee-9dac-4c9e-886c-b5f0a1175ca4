import { createContext, useContext, useState, ReactNode } from "react";
import ActionModal from "./ActionModal";
import { translate } from "../../services/language/Translator";

type ModalType = "info" | "warning" | "error";

interface ModalOptions {
  type?: ModalType;
  title: string;
  requireMessage?: boolean;
  confirmLabel?: string;
  cancelLabel?: string;
  onConfirm?: (textareaValue?: string) => void;
  onCancel?: () => void;
}

interface ModalContextType {
  openModal: (options: ModalOptions) => void;
  isModalOpen: boolean;
}

const ModalContext = createContext<ModalContextType | undefined>(undefined);

export const useModal = () => {
  const ctx = useContext(ModalContext);
  if (!ctx) throw new Error("useModal must be used within a ModalProvider");
  return ctx;
};

export const ModalProvider = ({ children }: { children: ReactNode }) => {
  const [modalProps, setModalProps] = useState<ModalOptions | null>(null);
  const [show, setShow] = useState(false);
  const [textareaValue, setTextareaValue] = useState("");

  const openModal = (options: ModalOptions) => {
    setModalProps(options);
    setTextareaValue("");
    setShow(true);
  };

  const handleConfirm = () => {
    modalProps?.onConfirm?.(textareaValue);
    setShow(false);
  };

  const handleCancel = () => {
    modalProps?.onCancel?.();
    setShow(false);
  };

  return (
    <ModalContext.Provider value={{ openModal, isModalOpen: show }}>
      {children}
      {modalProps && (
        <ActionModal
          show={show}
          type={modalProps.type}
          title={translate(modalProps.title)}
          textareaValue={textareaValue}
          onTextareaChange={setTextareaValue}
          onCancel={handleCancel}
          onConfirm={handleConfirm}
          confirmLabel={modalProps.confirmLabel}
          cancelLabel={modalProps.cancelLabel}
          requireMessage={modalProps.requireMessage}
        />
      )}
    </ModalContext.Provider>
  );
};
