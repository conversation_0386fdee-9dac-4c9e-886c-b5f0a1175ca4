import { useContext } from "react";
import { DropdownContext } from "./Context";

interface BodyProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  children: React.ReactNode | React.ReactNode[];
}

export const Body = (props: BodyProps) => {
  const { children, style } = props;
  const { isOpen, setIsOpen, isOpened } = useContext(DropdownContext);

  const closeDropdown = () => {
    isOpened && isOpened(false);
    setTimeout(() => setIsOpen(false), 200);
  };

  return (
    <>
      {isOpen && (
        <div
          {...props}
          data-testid="dropdown-body"
          style={{
            ...style,
            display: "inline-grid",
            position: "absolute",
            left: "50%",
            transform: "translateX(-50%)",
            zIndex: 999,
          }}
          onClick={closeDropdown}
        >
          {children}
        </div>
      )}
    </>
  );
};
