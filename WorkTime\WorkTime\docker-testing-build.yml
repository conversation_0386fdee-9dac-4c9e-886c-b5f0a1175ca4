trigger:
  branches:
    include:
      - testing
variables:
  - group: deployment-variables
steps:
  - checkout: self
  - script: echo $(Build.SourceBranchName)
    displayName: "Display branch name"
  - task: SSH@0
    displayName: "Create directory"
    inputs:
      sshEndpoint: "docker-linux"
      runOptions: "inline"
      inline: |
        mkdir -p /builds/WorkTime/$(Build.Repository.Name)/$(Build.SourceBranchName)
      failOnStdErr: true
  - task: CopyFilesOverSSH@0
    displayName: "Copy files from build directory"
    inputs:
      sshEndpoint: "docker-linux"
      sourceFolder: "$(Build.SourcesDirectory)"
      contents: |
        **
        !**/.git/**
        !**/.vscode/**
      targetFolder: "/builds/WorkTime/$(Build.Repository.Name)/$(Build.SourceBranchName)"
      cleanTargetFolder: true
      overwrite: true
      failOnEmptySource: true
  - task: SSH@0
    displayName: "Build and push Docker image"
    inputs:
      sshEndpoint: "docker-linux"
      runOptions: "inline"
      inline: |
        cd /builds/WorkTime/$(Build.Repository.Name)/$(Build.SourceBranchName)
        export RepositoryName=$(Build.Repository.Name)
        export TagName=$(Build.SourceBranchName)
        export InnerPort=80
        export OuterPort=80
        export VITE_GATEWAY_API=Testing
        export VITE_WORKTIME_API=Testing
        REPO_NAME=$(echo $(Build.Repository.Name) | tr '[:upper:]' '[:lower:]')
        docker build --pull --rm --build-arg VITE_GATEWAY_API="$VITE_GATEWAY_API" --build-arg VITE_WORKTIME_API="$VITE_WORKTIME_API" -f "Dockerfile" -t $REPO_NAME:$(Build.SourceBranchName) . 2>&1 || exit 1
        docker-compose -p worktime up -d 2>&1 || exit 1
        docker image prune --force 2>&1
      failOnStdErr: true
