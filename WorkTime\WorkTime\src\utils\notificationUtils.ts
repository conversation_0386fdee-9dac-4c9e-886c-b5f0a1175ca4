import { NotificationDTO } from "../models/DTOs/notifications/NotificationDTO";
import { AbsenceStatus } from "../models/DTOs/absence/AbsenceStatus";

export const getNotificationText = (notification: NotificationDTO): string => {
  if (
    notification.name === "Notifications.Absences.Added.Push" ||
    notification.name === "Notifications.Absences.Updated.Push" ||
    notification.name === "Notifications.Absences.Approved.Push" ||
    notification.name === "Notifications.Absences.Declined.Push" ||
    notification.name === "Notifications.Absences.Pending.Push" ||
    notification.name === "Notifications.Absences.EditedByEmployee.Push" ||
    notification.name === "Notifications.Absences.EditedByAdmin.Push" ||
    notification.name === "Notifications.Absences.Deleted.Push" ||
    notification.name === "Notifications.Absences.DeletedByAdmin.Push" 
  ) {
    if (notification.payload) {
      try {
        let payload;
        if (typeof notification.payload === 'string') {
          payload = JSON.parse(notification.payload);
        } else {
          payload = notification.payload;
        }

        if (payload !== undefined) {
          if (payload.status !== undefined) {
            const status = payload.status as AbsenceStatus;
            switch (status) {
              case AbsenceStatus.Approved:
                return "Notifications.Absences.Approved.Push";
              case AbsenceStatus.Declined:
                console.log("declined", notification.name);
                return "Notifications.Absences.Declined.Push";
              case AbsenceStatus.Pending:
                return "Notifications.Absences.Pending.Push";
              case AbsenceStatus.EditedByEmployee:
                return "Notifications.Absences.EditedByEmployee.Push";
              case AbsenceStatus.EditedByAdmin:
                return "Notifications.Absences.EditedByAdmin.Push";
              case AbsenceStatus.Deleted:
                return "Notifications.Absences.Deleted.Push";
              case AbsenceStatus.DeletedByAdmin:
                return "Notifications.Absences.DeletedByAdmin.Push";
              case AbsenceStatus.DeletedByUserAfterApproval:
                return "Notifications.Absences.DeletedByUserAfterApproval.Push";
              default:
                console.log("default", notification.name);
                return notification.name;
            }
          }
        }
      } catch (error) {
        console.error("Failed to parse notification payload:", error);
      }
    }
    return notification.name;
  }

  return notification.name;
}; 