import { createContext } from "react";
import { AvailableLanguages, DictionaryList } from "./constants";

export interface LanguageContextProps {
  userLanguage: AvailableLanguages;
  dictionary: any;
  userLanguageChange: (selected: AvailableLanguages) => void;
}

export const languageContextValue: LanguageContextProps = {
  userLanguage: "bg",
  dictionary: DictionaryList.bg,
  userLanguageChange: (selected: AvailableLanguages) => {},
};

export const LanguageContext =
  createContext<LanguageContextProps>(languageContextValue);
