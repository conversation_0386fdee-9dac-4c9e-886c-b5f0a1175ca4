import { NomenclatureWithDescriptionType } from "../../models/DTOs/enums/NomenclatureWithDescriptionType";
import { authenticatedGet } from "../connectionService";

export const initQualificationGroups = async (): Promise<
  NomenclatureWithDescriptionType[]
> => {
  try {
    const response = await authenticatedGet<NomenclatureWithDescriptionType[]>(
      `qualification-groups`
    );
    return response;
  } catch (error) {
    console.error("Error fetching qualification groups:", error);
    return [];
  }
};
