import React from "react";
import { styled } from "styled-components";
import plane from "../../../assets/images/attendancies/airplane.svg";
import heart from "../../../assets/images/attendancies/heart.svg";
import editNormal from "../../../assets/images/button/edit-normal.svg";
import editHover from "../../../assets/images/button/edit-hover.svg";
import docUploaded from "../../../assets/images/attendancies/doc-uploaded.svg";
import { translate } from "../../../services/language/Translator";
import { getLeaveType } from "../../../utils/leaveTypeUtils";
import Container from "../../../components/Container";
import { AbsenceInfo } from "../../../components/CalendarComponent/types/AbsenceInfo";
import Avatar, {
  getAbsenceAvatarColor,
} from "../../../components/CalendarComponent/Avatar";
import Button from "../../../components/Inputs/Button";
import { useUserEmployee } from "../../UserEmployeeContext";
import { DefaultPermissions } from "../../../constants/permissions";

const AbsenceDetailsContainer = styled(Container)`
  display: flex;
  flex-direction: column;
`;

const DetailsContainer = styled(Container)`
  display: flex;
  flex-direction: row;
  padding: 1rem;
  gap: 1.5rem;
`;

const ContentWrapper = styled(Container)`
  display: flex;
  flex-direction: column;
  flex: 0 0 65%;
`;

const ImageContainer = styled(Container)`
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    max-width: 100%;
    height: auto;
  }
`;

const HeaderSection = styled(Container)`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.75rem;
`;

const HeaderText = styled(Container)`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

const Title = styled.h2`
  margin: 0;
  font: normal normal 600 22px/30px Segoe UI;
`;

const Subtitle = styled(Container)`
  opacity: 0.9;
  font: normal normal 600 16px/21px Segoe UI;
`;

const ContentSection = styled(Container)`
  flex: 1;
`;

const DateRange = styled(Container)`
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin-bottom: 1rem;
  position: relative;
`;

const EditButton = styled.button`
  background: url(${editNormal}) no-repeat center;
  border: none;
  width: 1rem;
  height: 1rem;
  cursor: pointer;
  margin-left: 0.375rem;
  align-self: flex-end;
  margin-bottom: 0.375rem;

  &:hover {
    background: url(${editHover}) no-repeat center;
  }
`;

const DateGroup = styled(Container)`
  display: flex;
  flex-direction: column;
  margin-top: 1rem;
`;

const DateLabel = styled(Container)`
  font: normal normal normal 14px/18px Segoe UI;
  color: var(--combobox-header-text-color);
`;

const DateSeparator = styled(Container)`
  font: normal normal normal 18px/24px Segoe UI;
  color: var(--app-label-color);
  margin: 0;
  align-self: flex-end;
  margin-bottom: 0.0625rem;
`;

const DateText = styled(Container)`
  font: normal normal normal 18px/24px Segoe UI;
  color: var(--app-label-color);
`;

const TypeSection = styled(Container)`
  margin-top: 1.5rem;
`;

const TypeLabel = styled(Container)`
  font: normal normal normal 12px/16px Segoe UI;
  color: var(--combobox-header-text-color);
`;

const TypeValue = styled(Container)`
  font: normal normal normal 18px/24px Segoe UI;
  color: var(--app-label-color);
`;

const CommentSection = styled(Container)`
  margin-top: 1.5rem;
`;

const CommentLabel = styled(Container)`
  font: normal normal normal 12px/16px Segoe UI;
  color: var(--combobox-header-text-color);
`;

const CommentText = styled(Container)`
  font: normal normal normal 18px/24px Segoe UI;
  color: var(--app-label-color);
`;

const ActionButton = styled(Button)`
  margin-top: 2rem;
  margin-bottom: 1rem;
  // Много приятна стойност ;)
  height: 3.313rem;

  &:hover {
    background-color: var(--button-hover-color);
  }
`;

interface AbsenceDetailsProps {
  absence: AbsenceInfo;
  onBack: () => void;
  onEdit?: () => void;
}

const formatDate = (date: string) => {
  const dateObj = new Date(date);
  const day = dateObj.getDate().toString().padStart(2, "0");
  const month = (dateObj.getMonth() + 1).toString().padStart(2, "0");
  const year = dateObj.getFullYear();
  return `${day}.${month}.${year}`;
};

const AbsenceDetails: React.FC<AbsenceDetailsProps> = ({
  absence,
  onBack,
  onEdit,
}) => {
  const { userEmployee } = useUserEmployee();

  return (
    absence && (
      <AbsenceDetailsContainer data-testid="absence-details">
        <DetailsContainer>
          <ContentWrapper>
            <HeaderSection>
              <Avatar
                background={getAbsenceAvatarColor(
                  absence.status,
                  absence.isHospital
                )}
                name={absence.employeeName}
                photo={absence.isHospital ? heart : plane}
                size={2}
                isVisible={true}
                style={{
                  marginLeft: "0rem",
                  marginTop: "0.4rem",
                  padding: "0.4rem",
                  borderRadius: "100%",
                }}
                data-testid={`employee-avatar-${absence.payrollId}`}
              />
              <HeaderText>
                <Title>
                  {absence.isHospital
                    ? translate("Sick leave")
                    : translate("Absence")}
                </Title>
                <Subtitle>{absence.employeeName}</Subtitle>
              </HeaderText>
            </HeaderSection>

            <ContentSection>
              <DateRange>
                <DateGroup>
                  <DateLabel>{translate("From date")}</DateLabel>
                  <DateText>{formatDate(absence.startDate)}</DateText>
                </DateGroup>
                <DateSeparator>-</DateSeparator>
                <DateGroup>
                  <DateLabel>{translate("To date")}</DateLabel>
                  <DateText>{formatDate(absence.endDate)}</DateText>
                </DateGroup>
                {(userEmployee.permissions.includes(
                  DefaultPermissions.Attendances.Write
                ) ||
                  (absence.userId === userEmployee.userId &&
                    absence.endDate >= new Date().toISOString())) && (
                  <EditButton onClick={onEdit} />
                )}
              </DateRange>

              <TypeSection>
                <TypeLabel>{translate("Type")}:</TypeLabel>
                <TypeValue>{getLeaveType(absence)}</TypeValue>
              </TypeSection>

              <CommentSection>
                <CommentLabel>{translate("Comment")}:</CommentLabel>
                <CommentText>
                  {absence.isHospital ? absence.sickNote : absence.comment}
                </CommentText>
              </CommentSection>
            </ContentSection>
          </ContentWrapper>

          <ImageContainer>
            <img src={docUploaded} alt="Document uploaded" />
          </ImageContainer>
        </DetailsContainer>

        <ActionButton onClick={onBack} label={translate("Ok")} />
      </AbsenceDetailsContainer>
    )
  );
};

export default AbsenceDetails;
