import styled from "styled-components";

const TreeCard = styled.div`
  box-sizing: border-box;
  background-color: var(--auth-button-background-color);
  color: var(--company-name-label);
  text-decoration: none;
  justify-content: center;
  align-items: center;
  display: inline-block;
  border-radius: 1.5em;
  width: 10rem;
  height: 6.5rem;
  &:before {
    content: "";
    box-sizing: border-box;
    position: absolute;
    margin-top: -0.34375rem;
    left: 50%;
    transform: translateX(-50%);
    width: 0.6875rem;
    height: 0.6875rem;
    background-color: var(--company-structure-dots);
    border-radius: 50%;
    z-index: 1;
  }
`;

const TreeText = styled.p`
  margin: 0;
  box-sizing: border-box;
  text-align: center;
  text-transform: uppercase;
  margin-top: 0.1rem;
  font-size: 1rem;
  overflow: break-word;
  white-space: normal;
`;

const TreeImage = styled.img`
  width: 15%;
  box-sizing: border-box;
  margin-top: 1.4rem;
  object-fit: cover;
`;

interface Props {
  structureLevelName: string;
  imageSrc?: string;
}

const StructureLevelCard = ({ structureLevelName, imageSrc }: Props) => {
  return (
    <TreeCard data-testid="tree-card">
      <TreeImage src={imageSrc} alt="" data-testid="tree-image" />
      <TreeText data-testid="tree-text">{structureLevelName}</TreeText>
    </TreeCard>
  );
};

export default StructureLevelCard;
