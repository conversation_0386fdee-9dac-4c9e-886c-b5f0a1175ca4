import { ChangeEvent, useEffect, useState } from "react";
import Textbox from "./Textbox";
import styled from "styled-components";
import EyeImage from "../../assets/images/passowrd-box/eye.png";
import EyeHideImage from "../../assets/images/passowrd-box/eyeHide.png";
import Image from "../../components/Image";

interface Props
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  name: string;
  label: string;
  value: string | number;
  handleChange: (e: ChangeEvent<HTMLInputElement>) => void;
  handleBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  validation?: {
    isValid: boolean;
    alertMessage: string;
  };
}

const Wrapper = styled.div`
  position: relative;
`;

const EyeIconDiv = styled.div<{ isVisible: boolean }>`
  position: absolute;
  top: 50%;
  right: 0.625rem;
  transform: translateY(-50%);
  transition: background-color 0.3s ease;
  visibility: ${(p) => (p.isVisible ? "visible" : "hidden")};
`;

const EyeImg = styled(Image)`
  width: 1.125rem;
  height: 1.125rem;
  margin-top: 0.25rem;
`;

const PasswordBox = (props: Props) => {
  const { name, label, value, handleChange, handleBlur, validation, ...rest } =
    props;
  const [showPassword, setShowPassword] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [type, setType] = useState("password");

  useEffect(() => {
    setIsVisible(!value);
  }, [value]);

  const handleShowPassword = () => {
    setShowPassword((prevState) => {
      setType(!prevState ? "text" : "password");
      return !prevState;
    });
  };

  return (
    <Wrapper data-testid="password-box-wrapper">
      <Textbox
        name={name}
        handleChange={handleChange}
        handleBlur={handleBlur}
        label={label}
        inputType={type}
        value={value}
        data-testid="password-box-input"
        validation={validation}
        {...rest}
      />
      <EyeIconDiv
        data-testid="password-box-eye-icon"
        isVisible={!isVisible}
        onClick={handleShowPassword}
      >
        {showPassword ? (
          <EyeImg
            data-testid="password-box-eye-hide-image"
            src={EyeHideImage}
          />
        ) : (
          <EyeImg data-testid="password-box-eye-image" src={EyeImage} />
        )}
      </EyeIconDiv>
    </Wrapper>
  );
};

export default PasswordBox;
