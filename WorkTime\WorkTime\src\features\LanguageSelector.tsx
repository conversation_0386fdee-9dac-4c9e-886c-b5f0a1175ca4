import { MouseEvent, useContext, useEffect } from "react";
import Dropdown from "../components/Dropdown/Dropdown";
import Image from "../components/Image";
import {
  AvailableLanguages,
  LanguageOptions,
} from "../services/language/constants";
import { LanguageContext } from "../services/language/LanguageContext";
import styled from "styled-components";

import BgImage from "../assets/images/languages/bg.png";
import EnImage from "../assets/images/languages/en.png";

const DropdownComponent = styled(Dropdown)`
  margin-right: 1.5rem;
`;

const LanguageSelector = () => {
  const { userLanguage, userLanguageChange } = useContext(LanguageContext);

  const handleLanguageChange = (e: MouseEvent<HTMLDivElement>) => {
    userLanguageChange(
      e.currentTarget.getAttribute("data-language") as AvailableLanguages
    );
  };

  const languageImaegs: any = {
    bg: BgImage,
    en: EnImage,
  };

  useEffect(() => {
    let defaultLanguage = window.localStorage.getItem(
      "selected-language"
    ) as AvailableLanguages;

    if (!defaultLanguage) {
      defaultLanguage = window.navigator.language.substring(
        0,
        2
      ) as AvailableLanguages;
    }

    userLanguageChange(defaultLanguage);
  }, [userLanguageChange]);

  return (
    <DropdownComponent data-testid="language-selector-dropdown">
      <Dropdown.Header data-testid="language-selector-header">
        <Image
          alt="LanguageImage"
          size="small"
          src={languageImaegs[userLanguage]}
          data-testid="language-selector-current-language"
        />
      </Dropdown.Header>
      <Dropdown.Body data-testid="language-selector-body">
        {Object.entries(LanguageOptions).map(([id, name]) => {
          return (
            <Image
              key={id}
              data-language={id}
              onClick={handleLanguageChange}
              alt="LanguageImage"
              size="small"
              title={name}
              src={languageImaegs[id]}
              data-testid={`language-selector-option-${id}`}
            />
          );
        })}
      </Dropdown.Body>
    </DropdownComponent>
  );
};

export default LanguageSelector;
