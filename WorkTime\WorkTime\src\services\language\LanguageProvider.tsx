import { useState } from "react";
import {
  AvailableLanguages,
  DictionaryList,
  LanguageOptions,
} from "./constants";
import { LanguageContext, languageContextValue } from "./LanguageContext";

interface Props {
  children: React.ReactNode | React.ReactNode[];
}

export const LanguageProvider = ({ children }: Props) => {
  const [userLanguage, setUserLanguage] = useState<AvailableLanguages>("bg");

  const provider = {
    userLanguage,
    dictionary: DictionaryList[userLanguage],
    userLanguageChange: (selected: AvailableLanguages) => {
      const newLanguage = LanguageOptions[selected] ? selected : "en";
      setUserLanguage(newLanguage);
      window.localStorage.setItem("selected-language", newLanguage);
    },
  };

  Object.assign(languageContextValue, provider);

  return (
    <LanguageContext.Provider value={provider}>
      {children}
    </LanguageContext.Provider>
  );
};
