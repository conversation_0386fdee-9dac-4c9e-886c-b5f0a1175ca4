import { facebookLogin } from "../../../services/authentication/authenticationService";
import OAuthButton from "../../../components/Inputs/OAuthButton";
import FacebookLogo from "../../../assets/images/logos/facebook-logo.png";
import { useNavigate } from "react-router-dom";
import { useAuth } from "../AuthContext";
import { getWorkTimeRole } from "../../../services/authorization/authorizationService";
import { LOCAL_STORAGE_WORKTIME_ROLE_NAME } from "../../../constants/local-storage-constants";

interface Props {
  returnAfterLogin?: string;
}

const FacebookLogin = ({ returnAfterLogin }: Props) => {
  const navigate = useNavigate();
  const { setUser } = useAuth();

  const handleFacebookLogin = () => {
    FB.login(
      function (response: any) {
        if (response.authResponse) {
          facebookLogin(response.authResponse.accessToken).then(
            async (email) => {
              if (email) {
                await getWorkTimeRole();
                // TODO: Това трябва да се преправи, защото не работи така или иначе
                navigate(returnAfterLogin ?? "/");
              }
            }
          );
        }
      },
      { scope: "email" }
    );
  };

  return (
    <OAuthButton
      logo={FacebookLogo}
      content="Sign In with Facebook account"
      onClick={handleFacebookLogin}
      data-testid="facebook-login-button"
    />
  );
};

export default FacebookLogin;
