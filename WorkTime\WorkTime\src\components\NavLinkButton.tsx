import { NavLink, useLocation } from "react-router-dom";
import styled from "styled-components";
import { useState } from "react";

import Label from "./Inputs/Label";

const StyledLink = styled(NavLink)<{
  currentpath: string;
  to: string;
}>`
  display: flex;
  justify-content: left;
  align-items: center;
  text-decoration: none;
  font-family: Segoe UI;
  font-weight: Regular;
  width: 10rem;
  font-size: 1.1rem;
  min-height: 2.5rem;
  padding: 0.5rem;
  background-color: var(--link-background-color);
  z-index: 999;

  border-radius: 3.75rem;
  margin: 0.3rem;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: var(--link-background-color-hover);
    color: var(--link-color-hover);
  }
  ${({ currentpath, to }) =>
    currentpath === to &&
    `
      background-color: var(--link-background-color-active);
      color: var(--link-color-active);`}
`;

const Image = styled.img`
  cursor: pointer;
  margin: 0.5rem;
`;

const LinkLabel = styled(Label)<{
  currentpath: string;
  to: string;
}>`
  color: var(--navlink-text-color);
  cursor: pointer;

  &:hover {
    color: var(--navlink-text-selected-color);
  }
  ${({ currentpath, to }) =>
    currentpath === to &&
    `      
    color: var(--navlink-text-selected-color);`}
`;

interface Props {
  to: string;
  imageSrc: string;
  hoverImageSrc: string;
  label: string;
  currentpath: string;
}
const NavLinkButton = ({ to, imageSrc, hoverImageSrc, label }: Props) => {
  const location = useLocation();
  const [isHovered, setIsHovered] = useState(false);

  return (
    <StyledLink
      to={to}
      currentpath={location.pathname}
      data-testid="nav-link-button"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Image
        src={isHovered ? hoverImageSrc : imageSrc}
        data-testid="nav-link-button-image"
      />
      <LinkLabel
        to={to}
        currentpath={location.pathname}
        data-testid="nav-link-button-label"
      >
        {label}
      </LinkLabel>
    </StyledLink>
  );
};

export default NavLinkButton;
