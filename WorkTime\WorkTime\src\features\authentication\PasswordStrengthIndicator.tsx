import { styled } from "styled-components";
import Container from "../../components/Container";
import { PasswordStrengthType } from "../../models/Enums/PasswortStrengthType";
import { getPasswordStrengthLabel } from "../../services/authentication/passwordService";
import passwordStrengthStar from "../../assets/images/passowrd-box/password-strength-star.svg";

const PasswordStrengthRectangle = styled(Container)<{
  $passwordStrength: PasswordStrengthType;
}>`
  background-color: var(--password-strength-rectangle-empty-background-color);
  width: 100%;
  height: 0.4rem;
  margin: 0.5rem 0.5rem 0.25rem 0.5rem;
`;

const PasswordStrengthRectangleEmpty = styled(PasswordStrengthRectangle)`
  background-color: ${(props) =>
    props.$passwordStrength >= PasswordStrengthType.Strong
      ? "var(--password-strength-rectangle-strong-background-color)"
      : props.$passwordStrength > PasswordStrengthType.Empty
      ? "var(--password-strength-rectangle-not-empty-background-color)"
      : "var(--password-strength-rectangle-empty-background-color)"};
`;

const PasswordStrengthRectangleOne = styled(PasswordStrengthRectangle)`
  background-color: ${(props) =>
    props.$passwordStrength >= PasswordStrengthType.Strong
      ? "var(--password-strength-rectangle-strong-background-color)"
      : props.$passwordStrength > PasswordStrengthType.NotEmpty
      ? "var(--password-strength-rectangle-weak-background-color)"
      : "var(--password-strength-rectangle-empty-background-color)"};
`;

const PasswordStrengthRectangleTwo = styled(PasswordStrengthRectangle)`
  background-color: ${(props) =>
    props.$passwordStrength >= PasswordStrengthType.Strong
      ? "var(--password-strength-rectangle-strong-background-color)"
      : props.$passwordStrength > PasswordStrengthType.Weak
      ? "var(--password-strength-rectangle-middle-background-color)"
      : "var(--password-strength-rectangle-empty-background-color)"};
`;

const PasswordStrengthRectangleThree = styled(PasswordStrengthRectangle)`
  background-color: ${(props) =>
    props.$passwordStrength >= PasswordStrengthType.Strong
      ? "var(--password-strength-rectangle-strong-background-color)"
      : props.$passwordStrength > PasswordStrengthType.Middle
      ? "var(--password-strength-rectangle-good-background-color)"
      : "var(--password-strength-rectangle-empty-background-color)"};
`;

const PasswordStrengthRectangleFour = styled(PasswordStrengthRectangle)`
  background-color: ${(props) =>
    props.$passwordStrength >= PasswordStrengthType.Strong
      ? "var(--password-strength-rectangle-strong-background-color)"
      : "var(--password-strength-rectangle-empty-background-color)"};
`;

const PasswordStrengthContainer = styled(Container)`
  width: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  flex-grow: 1;
`;

const PasswordStrengthLabel = styled.div`
  width: 100%;
  text-align: right;
  font-size: 0.95rem;
  color: #888;
  margin-top: -0.2rem;
  margin-bottom: 0.5rem;
`;

const PasswordStrengthRow = styled.div`
  display: flex;
  align-items: center;
  width: 100%;
`;

export const PasswordStrengthIndicator = ({
  passwordStrength,
  setShowStrengthHint,
}: {
  passwordStrength: PasswordStrengthType;
  setShowStrengthHint: (show: boolean) => void;
}) => {
  return (
    <>
      <PasswordStrengthRow>
        <PasswordStrengthContainer
          data-testid="password-strength-container"
          onMouseOver={() => setShowStrengthHint(true)}
        >
          <PasswordStrengthRectangleEmpty
            data-testid="password-strength-rectangle-empty"
            $passwordStrength={passwordStrength}
          />
          <PasswordStrengthRectangleOne
            data-testid="password-strength-rectangle-one"
            $passwordStrength={passwordStrength}
          />
          <PasswordStrengthRectangleTwo
            data-testid="password-strength-rectangle-two"
            $passwordStrength={passwordStrength}
          />
          <PasswordStrengthRectangleThree
            data-testid="password-strength-rectangle-three"
            $passwordStrength={passwordStrength}
          />
          <PasswordStrengthRectangleFour
            data-testid="password-strength-rectangle-four"
            $passwordStrength={passwordStrength}
          />
        </PasswordStrengthContainer>
        <div style={{ width: "1rem", flexShrink: 0 }}>
          <img src={passwordStrengthStar} />
        </div>
      </PasswordStrengthRow>
      <PasswordStrengthLabel>
        {getPasswordStrengthLabel(passwordStrength)}
      </PasswordStrengthLabel>
    </>
  );
};
