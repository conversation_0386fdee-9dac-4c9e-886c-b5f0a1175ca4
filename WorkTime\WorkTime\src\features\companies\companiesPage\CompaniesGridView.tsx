import { useAppDispatch } from "../../../app/hooks";
import { useContext, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { onGetCompanies } from "../companiesActions";
import { useCompany } from "../CompanyContext";
import { CompanyDTO } from "../../../models/DTOs/companies/CompanyDTO";
import { LOCAL_STORAGE_COMPANY_ID } from "../../../constants/local-storage-constants";
import CompaniesGridElements from "./CompaniesGridElements";
import styled from "styled-components";
import { useUserEmployee } from "../../UserEmployeeContext";
import { initUserEmployee } from "../../../services/users/userService";
import { UserEmployeeDTO } from "../../../models/DTOs/users/UserEmployeeDTO";
import Container from "../../../components/Container";
import { loadEmployeePayrollsAction } from "../../payroll/employeePayrollActions";
import { getEmployeePayrolls } from "../../../services/employees/employeePayrollsService";
import { DefaultPermissions } from "../../../constants/permissions";

const CompaniesContainer = styled(Container)`
  margin-top: 1rem;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-height: 50vh;
  overflow-y: auto;
  overflow-x: hidden;
`;

interface GridViewProps {
  data: CompanyDTO[];
  highlightCompanyId?: string;
}
const CompaniesGridView: React.FC<GridViewProps> = ({
  data,
  highlightCompanyId,
}) => {
  const dispatch = useAppDispatch();
  const { setCompany } = useCompany();
  const navigate = useNavigate();
  const { setUserEmployee } = useUserEmployee();
  const location = useLocation();

  useEffect(() => {
    dispatch(onGetCompanies());
  }, [dispatch]);

  const selectCompany = async (id: string) => {
    const company = data.find((companyItem) => companyItem.id === id);

    if (company) {
      setCompany(company);

      localStorage.setItem(LOCAL_STORAGE_COMPANY_ID, company.id);

      dispatch({ type: "CHANGE_COMPANY", company: company });
      await initUserEmployee().then(async (response: UserEmployeeDTO) => {
        setUserEmployee(response);

        const employeePayrollsResponse = await getEmployeePayrolls(company.id);
        dispatch(loadEmployeePayrollsAction(employeePayrollsResponse));

        if (
          response.permissions.includes(DefaultPermissions.Employees.Write) ||
          employeePayrollsResponse.length > 1
        )
          navigate(`/${company.id}/employees`);
        else
          navigate(
            `/${company.id}/employees/${response.employeeId}/${
              employeePayrollsResponse[0]?.payrollId ?? ""
            }`
          );
      });
    } else {
      console.error(`Failed to find company with id: ${id}`);
    }
  };

  const highlightId = highlightCompanyId || location.state?.highlightCompanyId;

  return (
    <CompaniesContainer>
      {data && data.length > 0 && (
        <CompaniesGridElements
          data-testid="companies-grid-elements"
          data={data.map((curData) => ({
            id: curData.id,
            name: curData.name,
            bulstat: curData.bulstat,
          }))}
          handleClick={selectCompany}
          imgSize={5.5}
          highlightCompanyId={highlightId}
        />
      )}
    </CompaniesContainer>
  );
};

export default CompaniesGridView;
