import { Header } from "./Header";

interface FormProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  children: React.ReactNode | React.ReactNode[];
}

const Form = (props: FormProps) => {
  const { children } = props;
  return (
    <div data-testid="form-container" {...props}>
      {children}
    </div>
  );
};

const _default = Object.assign(Form, {
  Header,
});

export default _default;
