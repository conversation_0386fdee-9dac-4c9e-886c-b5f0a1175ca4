import { IEntity } from "../IEntity";
import { NomenclatureDTO } from "../nomenclatures/NomenclatureDTO";

export interface EmployeePayrollDTO extends IEntity {
  payrollId: string;
  employeeGuid: string;
  firstName: string;
  secondName: string;
  lastName: string;
  egn: string;
  email: string;
  positionName: string;
  contractNumber: string;
  structureId: string;
  companyId: string;
  typeOfAppointment: NomenclatureDTO;
  category: NomenclatureDTO;
}
