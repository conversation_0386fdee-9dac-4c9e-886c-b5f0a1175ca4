import { PasswordStrengthType } from "../../models/Enums/PasswortStrengthType";
import { translate } from "../language/Translator";

export const passwordStrengthCheck = (
  password: string
): PasswordStrengthType => {
  let score = 0;

  // Regular expression to match special characters
  var specialChars = /["!@#$%^&*()_+{}\[\]:;<>,.?~\-]/;

  // Check if password length is greater than or equal to 8
  if (password.length >= 8) {
    score++;
  }

  // Check if password has a number
  if (/\d/.test(password)) {
    score++;
  }

  // Check if password has a lowercase letter
  if (/[a-z]/.test(password)) {
    score++;
  }

  // Check if password has an uppercase letter
  if (/[A-Z]/.test(password)) {
    score++;
  }

  // Check if password has a special character (Cyrillic or non-Cyrillic)
  if (specialChars.test(password)) {
    score++;
  }

  return score;
};

export function getPasswordStrengthLabel(
  passwordStrength: PasswordStrengthType
): string {
  switch (passwordStrength) {
    case PasswordStrengthType.NotEmpty:
      return translate("Very weak");
    case PasswordStrengthType.Weak:
      return translate("Weak");
    case PasswordStrengthType.Middle:
      return translate("Good");
    case PasswordStrengthType.Good:
      return translate("Very good");
    case PasswordStrengthType.Strong:
      return translate("Excellent");
    default:
      return "";
  }
}
