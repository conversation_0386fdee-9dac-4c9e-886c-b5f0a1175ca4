import React from "react";
import styled, { css } from "styled-components";
import { IGridViewEntity } from "../models/Interfaces/IGridViewEntity";

interface GridViewProps<T extends IGridViewEntity> {
  data: T[];
  renderElement: (item: T) => React.ReactNode;
}

const GridViewContainer = styled.div`
  display: grid;
  max-width: 100%;
  margin-left: 5rem;
  grid-template-columns: repeat(auto-fill, minmax(12rem, 1fr));
  grid-gap: 1.8rem;
  justify-items: center;
  margin: 1rem;
`;

export const GridViewDiv = styled.div<{ isHighlighted?: boolean }>`
  height: 12rem;
  width: 12rem;
  cursor: pointer;
  position: relative;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 1.8rem;
  border: 0.2rem solid transparent;
  transition: transform 0.3s ease-in-out, border 0.3s ease-in-out;
  box-shadow: 0px 2px 5px var(--container-box-shadow-gridView);
  background: var(--container-backround-gridView);
  cursor: pointer;
  transform-origin: center;

  &:hover {
    border: 0.2rem solid var(--container-hover-border-gridView);
    transform: scale(1.04);
  }
  ${({ isHighlighted }) =>
    isHighlighted &&
    css`
      border: 0.2rem solid var(--container-hover-border-gridView);
      transform: scale(1.04);
    `}
`;

const GridView = <T extends IGridViewEntity>({
  data,
  renderElement,
}: GridViewProps<T>) => {
  return (
    <GridViewContainer data-testid="grid-view-container">
      {data.map((item) => renderElement(item))}
    </GridViewContainer>
  );
};

export default GridView;
