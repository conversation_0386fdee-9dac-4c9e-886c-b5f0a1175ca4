import { ChangeEvent, MouseEvent, useEffect, useState } from "react";
import styled from "styled-components";
import { useAppDispatch } from "../../app/hooks";
import Container from "../../components/Container";
import Form from "../../components/Form/Form";
import Button from "../../components/Inputs/Button";
import Textbox from "../../components/Inputs/Textbox";
import MainWindowContainer from "../../components/MainWindowContainer";
import UploadButton from "../../components/UploadButton/UploadButton";
import { CompanyDTO } from "../../models/DTOs/companies/CompanyDTO";
import { SenderaCompanyDTO } from "../../models/DTOs/companies/SenderaCompanyDTO";
import { authenticatedPost } from "../../services/connectionService";
import { onCompanyCreated, onCompanyImported } from "./companiesActions";
import { useNavigate } from "react-router-dom";
import { useMenu } from "../MenuContext";

const StyledButton = styled(Button)`
  width: -webkit-fill-available;
  margin-top: 0.5rem;
`;

const LogoContainer = styled(Container)`
  float: left;
  margin-top: 0.75rem;
  background-color: var(--upload-button-background-color);
  border-radius: 50%;
`;

const CompanyInfoContainer = styled(Container)`
  width: 63%;
  float: right;
`;

interface Props {
  importCompany?: SenderaCompanyDTO;
}

const CreateCompany = (CompanyProp: Props): JSX.Element => {
  const dispatch = useAppDispatch();
  const { importCompany } = CompanyProp;
  const { closeMenu } = useMenu();
  const navigate = useNavigate();

  useEffect(() => {
    if (importCompany) {
      setCompany({
        id: "",
        name: importCompany.name ?? "",
        bulstat: importCompany.bulstat ?? "",
        contactName: importCompany.contactName ?? "",
        userRegistrationCompanyId: importCompany.id,
      } as CompanyDTO);
      setIsDisabled(
        importCompany?.name?.length > 0 && importCompany?.bulstat?.length > 0
      );
    }
  }, [importCompany]);

  const [company, setCompany] = useState({} as CompanyDTO);
  const [isdisabled, setIsDisabled] = useState(false);
  const [isImporting, setIsImporting] = useState(false);

  const handleCompanyChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newCompany = {
      ...company,
      [e.currentTarget.name]: e.currentTarget.value,
    };
    setCompany(newCompany);

    setIsDisabled(
      newCompany?.name?.length > 0 && newCompany?.bulstat?.length > 0
    );
  };

  const handleCreateCompany = async (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    setIsImporting(true);

    if (Object.keys(CompanyProp.importCompany || {}).length !== 0) {
      await authenticatedPost<CompanyDTO>("add-employee-company", company)
        .then((importedCompany) => {
          dispatch(onCompanyImported(importedCompany));
          navigate("/", { state: { highlightCompanyId: importedCompany.id } });
        })
        .catch((error) => console.error(error));
    } else {
      await authenticatedPost<CompanyDTO>(`company`, company)
        .then((createdCompany) => {
          dispatch(onCompanyCreated(createdCompany));
          navigate("/", { state: { highlightCompanyId: createdCompany.id } });
        })
        .catch((error) => {});
    }

    setIsImporting(false);

    closeMenu();
  };

  return (
    <MainWindowContainer>
      <Form>
        <LogoContainer>
          <UploadButton />
        </LogoContainer>
        <CompanyInfoContainer>
          <Textbox
            name="name"
            handleChange={handleCompanyChange}
            value={company.name}
            label="Company Name"
            data-testid="textbox-name"
          />
          <Textbox
            name="bulstat"
            handleChange={handleCompanyChange}
            value={company.bulstat}
            label="EIK"
            data-testid="textbox-bulstat"
          />
          <Textbox
            name="contactName"
            handleChange={handleCompanyChange}
            value={company.contactName}
            label="MOL"
            data-testid="textbox-contactName"
          />
        </CompanyInfoContainer>
        <StyledButton
          label="Create Company"
          onClick={handleCreateCompany}
          disabled={!isdisabled || isImporting}
          data-testid="button-create-company"
        />
      </Form>
    </MainWindowContainer>
  );
};

export default CreateCompany;
