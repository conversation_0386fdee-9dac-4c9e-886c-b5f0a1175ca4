import styled from "styled-components";
import Translator from "../../services/language/Translator";

interface LabelProps
  extends React.DetailedHTMLProps<
    React.LabelHTMLAttributes<HTMLLabelElement>,
    HTMLLabelElement
  > {
  children: string;
  uppercase?: boolean;
}

const LabelField = styled.label`
  color: var(--label-color);
  align-self: center;
`;

const Label = (props: LabelProps) => {
  const { children, uppercase } = props;
  return (
    <LabelField {...props} data-testid="label-field">
      <Translator
        getString={children}
        uppercase={uppercase}
        data-testid="label-translator"
      />
    </LabelField>
  );
};

export default Label;
