import React, {
  createContext,
  useState,
  useContext,
  useEffect,
  ReactNode,
} from "react";

import { initQualificationGroups } from "../services/nomenclatures/qualificationGroupsService";
import { NomenclatureWithDescriptionType } from "../models/DTOs/enums/NomenclatureWithDescriptionType";

interface NomenclatureWithDescriptionContext {
  qualificationGroups: NomenclatureWithDescriptionType[];
  // more enums
}

interface NomenclatureWithDescriptionProviderProps {
  children: ReactNode;
}

const NomenclatureWithDescriptionContext =
  createContext<NomenclatureWithDescriptionContext>({
    qualificationGroups: [],
    // more enums default values
  });

export const NomenclatureWithDescriptionProvider: React.FC<
  NomenclatureWithDescriptionProviderProps
> = ({ children }) => {
  const [nomenclatures, setNomenclatures] =
    useState<NomenclatureWithDescriptionContext>({
      qualificationGroups: [],
      // more enums
    });

  useEffect(() => {
    initQualificationGroups().then((qualificationGroups) => {
      setNomenclatures((prevNomenclatures) => ({
        ...prevNomenclatures,
        qualificationGroups,
      }));
    });
  }, []);

  return (
    <NomenclatureWithDescriptionContext.Provider value={nomenclatures}>
      {children}
    </NomenclatureWithDescriptionContext.Provider>
  );
};

export const useNomenclatureWithDescription = () =>
  useContext(NomenclatureWithDescriptionContext);
