import { useEffect, useState } from "react";
import { GetCoworkersResponse } from "../../models/Responses/GetCoworkersResponse";
import { authenticatedGet } from "../../services/connectionService";
import Container from "../../components/Container";
import { useAppSelector } from "../../app/hooks";
import { companiesState } from "../companies/companiesActions";
import { CoworkerDTO } from "../../models/DTOs/employees/CoworkerDTO";
import { RoleDTO } from "../../models/DTOs/RoleDTO";
import { getRoles } from "../../services/authorization/authorizationService";
import Dropdown from "../../components/Dropdown/Dropdown";
import styled, { css, keyframes } from "styled-components";
import upImage from "../../assets/images/button/up.png";
import { translate } from "../../services/language/Translator";
import { toast } from "react-toastify";
import { changeUserRole } from "../../services/companies/companiesService";
import { EmployeeDTO } from "../../models/DTOs/employees/EmployeeDTO";

const HeaderWrapper = styled(Container)<{
  isOpen: boolean;
}>`
  box-sizing: border-box;
  width: 100%;
  border: 0;
  border-radius: 1.9rem;
  padding: 1rem 1rem 1rem 1.5rem;
  transition: 0.4s;
  background: var(--company-dropdown-opened-color);
  outline: none;
  cursor: pointer;
  font-weight: bold;
  &:first-child {
    border-radius: ${(props) =>
      props.isOpen ? "1.625rem 1.625rem 0 0" : "2rem"};
  }
`;

const HeaderImage = styled(Container)<{ isClicked: boolean }>`
  position: absolute;
  background-size: cover;
  height: 1rem;
  width: 1rem;
  right: 1.5rem;
  top: 40%;
  cursor: pointer;
  background-image: url(${upImage});
  transition-duration: 0.5s;
  ${({ isClicked }) =>
    !isClicked &&
    `transform:rotate(180deg);
    transition-duration: 0.5s;
    background-image: url(${upImage});
    top: 45%;
  `}
`;

const DropdownBody = styled(Dropdown.Body)`
  width: 100%;
  max-height: 30rem;
  overflow-y: auto;
  scrollbar-width: none;
  overflow-x: hidden;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--scrollbar-track-color);
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--scrollbar-thumb-color);
    border-radius: 10px;
    border: 2px solid var(--scrollbar-track-color);
  }
`;
const Wrapper = styled(Container)<{
  isOpen: boolean;
}>`
  box-sizing: border-box;
  width: 100%;
  border: 0;
  padding: 0.5rem 1rem 0.5rem 1.5rem;
  background: var(--textbox-color);
  outline: none;
  cursor: pointer;
  &:last-child {
    border-radius: 0 0 2rem 2rem;
    padding-bottom: 2rem;
  }

  animation: ${({ isOpen }) =>
    isOpen
      ? css`
          ${fadeIn} 0.3s ease-in
        `
      : css`
          ${fadeOut} 0.3s ease-out
        `};
`;

const fadeIn = keyframes`
  from {
      opacity: 0;
      transform: translateY(-0.625rem);
      padding-bottom: 0;
    }
    to {
      opacity: 2;
      transform: translateY(0);
      padding-bottom: 0.625rem;
    }
`;

const fadeOut = keyframes`
  from {
      opacity: 2;
      transform: translateY(0);
      padding-bottom: 0.188rem;
    }
    to {
      opacity: 0;
      transform: translateY(-0.625rem);
      padding-bottom: 0;
    }
`;

interface CoworkersSideMenuProps {}

const CoworkersSideMenu: React.FC<CoworkersSideMenuProps> = ({}) => {
  const [pendingEmployees, setPendingEmployees] = useState<EmployeeDTO[]>([]);
  const [coworkers, setCoworkers] = useState<CoworkerDTO[]>([]);
  const [roles, setRoles] = useState([] as RoleDTO[]);

  const [openStates, setOpenStates] = useState<Record<string, boolean>>({});
  const [selectedRoles, setSelectedRoles] = useState<Record<string, RoleDTO>>(
    {}
  );

  const { activeCompanies } = useAppSelector(companiesState);

  const loadCompanies = async () => {
    const getPendingCoworkersResponse =
      await authenticatedGet<GetCoworkersResponse>("employees/coworkers");

    if (getPendingCoworkersResponse) {
      setPendingEmployees(getPendingCoworkersResponse.pendingEmployees ?? []);
      setCoworkers(getPendingCoworkersResponse.coworkers ?? []);
    }
  };

  useEffect(() => {
    loadCompanies();
  }, []);

  useEffect(() => {
    getRoles().then((response: RoleDTO[]) => {
      setRoles(response);
    });
  }, []);

  const handleIsOpened = (id: string, isOpen: boolean) => {
    setOpenStates((prev) => ({ ...prev, [id]: isOpen }));
  };

  const handleUserAccepted = async (employee: EmployeeDTO, role: RoleDTO) => {
    setSelectedRoles((prev) => ({ ...prev, [employee.id]: role }));
    if (!role) {
      return;
    }

    await changeUserRole(
      employee.id,
      employee.companyId,
      employee.company.userRegistrationCompanyId,
      undefined,
      role.id,
      true
    );

    toast.success(translate("strRoleChangedSuccessfully"));
  };

  const handleChangeRole = async (
    coworker: CoworkerDTO,
    role: RoleDTO,
    company: any
  ) => {
    setSelectedRoles((prev) => ({
      ...prev,
      [coworker.userRegistrationCompanyId]: role,
    }));
    if (!role) {
      return;
    }

    await changeUserRole(
      coworker.employee.workTimeId,
      company.id,
      company.userRegistrationCompanyId,
      coworker.roles[0].id,
      role.id,
      false
    );

    toast.success(translate("strRoleChangedSuccessfully"));
  };

  return (
    <Container>
      {pendingEmployees &&
        pendingEmployees.map((pendingEmployee) => {
          const isOpen = openStates[pendingEmployee.id] || false;
          const selectedRole = selectedRoles[pendingEmployee.id];

          return (
            <Container
              key={pendingEmployee.id}
              data-testid={`pending-employee-${pendingEmployee.id}`}
            >
              <Container
                style={{
                  display: "flex",
                  gap: "2rem",
                }}
              >
                {`${translate("strEmployee")}`}:{"     "}
                <Container
                  style={{ fontWeight: "bold" }}
                  data-testid={`pending-employee-name-${pendingEmployee.id}`}
                >
                  {pendingEmployee.firstName} {pendingEmployee.lastName}
                </Container>
              </Container>

              <Container>
                <Container>{`${translate("strWaitingApproval")}`}:</Container>
                <Container
                  style={{ fontWeight: "bold" }}
                  data-testid={`pending-employee-company-${pendingEmployee.id}`}
                >
                  {pendingEmployee.company.name}
                </Container>
              </Container>

              <Dropdown
                style={{ marginTop: "0.5rem" }}
                isOpened={(isOpen) =>
                  handleIsOpened(pendingEmployee.id, isOpen)
                }
                data-testid={`pending-employee-dropdown-${pendingEmployee.id}`}
              >
                <Dropdown.Header>
                  <HeaderWrapper isOpen={isOpen}>
                    <span
                      style={{
                        color: selectedRole
                          ? "inherit"
                          : "var(--input-placeholder-color, #999)",
                      }}
                    >
                      {selectedRole
                        ? translate(selectedRole.name)
                        : translate("strChooseRole")}
                    </span>
                  </HeaderWrapper>
                  <HeaderImage isClicked={isOpen}></HeaderImage>
                </Dropdown.Header>
                <DropdownBody>
                  {roles.map((role) => (
                    <Wrapper
                      isOpen={isOpen}
                      key={role.name}
                      onClick={() => handleUserAccepted(pendingEmployee, role)}
                    >
                      {translate(role.name)}
                    </Wrapper>
                  ))}
                </DropdownBody>
              </Dropdown>
            </Container>
          );
        })}
      {coworkers.map((coworker) => {
        const isOpen = openStates[coworker.userRegistrationCompanyId] || false;
        const selectedRole = selectedRoles[coworker.userRegistrationCompanyId];

        const company = activeCompanies.find(
          (company) =>
            company.userRegistrationCompanyId ===
            coworker.userRegistrationCompanyId
        );

        if (!company) {
          return null;
        }

        return (
          <Container
            key={coworker.userRegistrationCompanyId}
            data-testid={`coworker-${coworker.userRegistrationCompanyId}`}
          >
            <Container
              style={{
                display: "flex",
                gap: "2rem",
              }}
            >
              {`${translate("strEmployee")}`}:{"     "}
              <Container
                style={{ fontWeight: "bold" }}
                data-testid={`coworker-name-${coworker.userRegistrationCompanyId}`}
              >
                {coworker.employee.firstName} {coworker.employee.lastName}
              </Container>
            </Container>

            <Container>
              <Container>{`${translate("strCompanyRoleTxt")}`}:</Container>
              <Container
                style={{ fontWeight: "bold" }}
                data-testid={`coworker-company-${coworker.userRegistrationCompanyId}`}
              >
                {company.name}
              </Container>
            </Container>

            <Dropdown
              style={{ marginTop: "0.5rem" }}
              isOpened={(isOpen) =>
                handleIsOpened(coworker.employee.id, isOpen)
              }
              data-testid={`coworker-dropdown-${coworker.userRegistrationCompanyId}`}
            >
              <Dropdown.Header>
                <HeaderWrapper isOpen={isOpen}>
                  <span
                    style={{
                      color: selectedRole
                        ? "inherit"
                        : "var(--input-placeholder-color, #999)",
                    }}
                  >
                    {selectedRole
                      ? translate(selectedRole.name)
                      : translate(
                          coworker.roles[0]?.name ?? translate("strChooseRole")
                        )}
                  </span>
                </HeaderWrapper>
                <HeaderImage isClicked={isOpen}></HeaderImage>
              </Dropdown.Header>
              <DropdownBody>
                {roles.map((role) => (
                  <Wrapper
                    isOpen={isOpen}
                    key={role.name}
                    onClick={() => handleChangeRole(coworker, role, company)}
                  >
                    {translate(role.name)}
                  </Wrapper>
                ))}
              </DropdownBody>
            </Dropdown>
          </Container>
        );
      })}
    </Container>
  );
};

export default CoworkersSideMenu;
