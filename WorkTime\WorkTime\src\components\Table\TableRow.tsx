import styled from "styled-components";
import { IEntity } from "../../models/DTOs/IEntity";
import { ColumnDefinitionType } from "./Table";
import React from "react";
import { NomenclatureDTO } from "../../models/DTOs/nomenclatures/NomenclatureDTO";
import Container from "../Container";

export interface ButtonCell {
  renderButton: (id: string) => React.ReactNode;
}

interface TableRowProps<T extends IEntity, K extends keyof T>
  extends React.DetailedHTMLProps<
    React.HTMLAttributes<HTMLTableRowElement>,
    HTMLTableRowElement
  > {
  data: Array<T>;
  columns: Array<ColumnDefinitionType<T, K>>;
  searchFiled?: string | undefined;
  handleDelete?: (idToDelete: string) => Promise<void>;
  onRowClick?: (row: T) => void;
  buttons?: ButtonCell[];
}

const TableWrapper = styled(Container)`
  height: 30rem;
  overflow-y: auto;
  scrollbar-color: var(--scrollbar-hover) var(--scrollbar-background);
  background-color: var(--table-cell-backgroundColor);
  scrollbar-gutter: stable;
`;

const TableRowItem = styled.tr`
  padding-top: 0.3rem;
  height: 2.3rem;
  border-bottom: 0.01rem solid var(--table-row-bottom-line-color);
  display: grid;
  grid-template-columns: 0.2fr 1.5fr 1fr 1fr 1fr 1fr 0.2fr;
  background-color: var(--table-cell-backgroundColor);

  &:hover {
    background-color: var(--table-row-hover-color);
    transition: background-color 0.3s ease-in-out;
  }
`;

const TableCell = styled.td<{ children?: React.ReactNode }>`
  font-size: 0.875rem;
  color: var(--table-cell-color);
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  &:first-child {
    min-width: 1.4rem;
  }
  &:nth-child(2) {
    grid-column: 2 / 3;
  }
`;

const TableRows = <T extends IEntity, K extends keyof T>({
  data,
  columns,
  onRowClick,
  buttons,
}: TableRowProps<T, K>): JSX.Element => {
  const getCellValue = (value: any): React.ReactNode => {
    if (value == null) return "";

    if (typeof value === "object" && "description" in value) {
      return (
        (value as NomenclatureDTO).description ||
        (value as NomenclatureDTO).name ||
        "N/A"
      );
    }

    return value;
  };

  return (
    <TableWrapper data-testid="table-row-wrapper">
      {data.map((row, index) => (
        <TableRowItem
          key={`row-${index}`}
          onClick={() => {
            if (onRowClick) {
              onRowClick(row);
            }
          }}
        >
          {columns.map((column, index2) => (
            <TableCell key={`cell-${index2}`}>
              {getCellValue(row[column.key])}
            </TableCell>
          ))}
          {buttons &&
            buttons.map((b) => (
              <TableCell>{b.renderButton(row.workTimeId)}</TableCell>
            ))}
        </TableRowItem>
      ))}
    </TableWrapper>
  );
};

export default TableRows;
