import { useGoogleLogin } from "@react-oauth/google";
import { googleLogin } from "../../../services/authentication/authenticationService";
import { useNavigate } from "react-router";
import { useAuth } from "../AuthContext";
import OAuthButton from "../../../components/Inputs/OAuthButton";
import GoogleLogo from "../../../assets/images/logos/google-logo.png";
import { getWorkTimeRole } from "../../../services/authorization/authorizationService";
import { LOCAL_STORAGE_WORKTIME_ROLE_NAME } from "../../../constants/local-storage-constants";

interface Props {
  returnAfterLogin?: string;
}

const GoogleLogin = ({ returnAfterLogin }: Props) => {
  const navigate = useNavigate();
  const { setUser } = useAuth();

  const login = useGoogleLogin({
    onSuccess: (codeResponse) => {
      googleLogin(codeResponse.code).then(async (email) => {
        if (email) {
          await getWorkTimeRole();

          setUser({
            userId: "",
            firstName: "",
            secondName: "",
            lastName: "",
            email: email,
            hasSignedIn: true,
            workTimeRoleName:
              localStorage.getItem(LOCAL_STORAGE_WORKTIME_ROLE_NAME) ?? "",
          });
          navigate(returnAfterLogin ?? "/");
        }
      });
    },
    onError: (error) => console.log(error),
    flow: "auth-code",
  });

  return (
    <OAuthButton
      logo={GoogleLogo}
      content="Sign In with Google account"
      onClick={login}
      data-testid="google-login-button"
    />
  );
};

export default GoogleLogin;
