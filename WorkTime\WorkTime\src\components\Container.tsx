import { forwardRef } from "react";
import styled from "styled-components";

interface ContainerProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  as?: undefined;
  children?: React.ReactNode | React.ReactNode[];
}

const ContainerDiv = styled.div<ContainerProps>``;

const Container = forwardRef<HTMLDivElement, ContainerProps>((props, ref) => {
  const { children } = props;
  return (
    <ContainerDiv {...props} ref={ref} data-testid="container-div">
      {children}
    </ContainerDiv>
  );
});

export default Container;
