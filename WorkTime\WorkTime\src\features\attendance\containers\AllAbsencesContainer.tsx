import React, { useState, useMemo } from "react";
import styled from "styled-components";
import { useAppSelector } from "../../../app/hooks";
import { useEmployeesWithLeaves } from "../useFilteredEmployees";
import { selectPayrolls } from "../../payroll/payrollsActions";
import { calculateLeaveDaysInPeriod } from "../../../services/calendar/calendarService";
import Container from "../../../components/Container";
import Avatar from "../../../components/CalendarComponent/Avatar";
import Label from "../../../components/Inputs/Label";
import { generateColorFromName } from "../../../utils/colorUtils";
import { Header } from "../../../components/Header";
import { HeaderType } from "../../../models/Enums/HeaderType";
import BaseFilter from "../../../components/Table/BaseFilter";
import ComboboxMultipleChoices from "../../../components/Combobox/ComboboxMultipleChoices";
import { useEnums } from "../../EnumContext";
import { Employee } from "../useFilteredEmployees";
import Button from "../../../components/Inputs/Button";
import backArrowIcon from "../../../assets/images/arrows/back-arrow.svg";

const AbsencesContainer = styled(Container)`
  display: block;
  background-color: var(--attendancies-right-view-admin-table-background-color);
  border-radius: 1.8rem;
  height: calc(100% - 3rem);
  overflow-y: auto;
  box-sizing: border-box;
  position: relative;
  min-height: 40rem;
`;

const ComboboxRow = styled.div`
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
  width: 100%;

  > * {
    width: 100%;
  }
`;

const FiltersContainer = styled(Container)`
  padding: 1rem;
  display: block;
  height: 7rem;
`;

const EmployeesContainer = styled(Container)`
  position: absolute;
  top: 9rem;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 0.2rem;
  padding-top: 0rem;
  background-color: var(
    --attendancies-right-view-admin-employees-background-color
  );
`;

const EmployeeRow = styled(Container)<{ isHovered?: boolean }>`
  display: flex;
  align-items: center;
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 0.5rem;
  transition: background-color 0.2s;

  &:hover {
    background-color: var(--attendancies-hover-emploeeys);
  }

  ${({ isHovered }) =>
    isHovered &&
    `
    background-color: var(--attendancies-hover-emploeeys);
  `}
`;

const StyledAvatar = styled(Avatar)`
  margin-right: 0.5rem;
`;

const EmployeeInfo = styled(Container)`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex: 1;
`;

const EmployeeName = styled(Label)`
  margin-left: 0.5rem;
  font-weight: 450;
  font-size: 0.9375rem;
  color: var(--attendancies-right-view-admin-list-font-color);
`;

const EmployeePosition = styled(Label)`
  font-weight: 450;
  font-size: 0.9375rem;
  color: var(--attendancies-right-view-position-font-color);
`;

const StyledHeader = styled(Header)`
  margin-bottom: 1rem;
  text-transform: uppercase;
  font-weight: 450;
  font: var(--unnamed-font-style-normal) normal
    var(--unnamed-font-weight-normal) 1rem/1.3125rem Inter;
  letter-spacing: var(--unnamed-character-spacing-0);
  text-align: left;
  font: normal normal normal 1rem/1.3125rem Inter;
  letter-spacing: 0;
  color: #3e485a;
  text-transform: uppercase;
  opacity: 1;
  padding-left: 0.4rem;
`;

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.1rem;
`;

const BackButton = styled(Button)`
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: var(--attendancies-back-button-background-color);
  color: var(--attendancies-back-button-color);
  border: none;
  border-radius: 3.75rem;
  padding: 0.5rem 1.5rem;
  font: normal normal normal 1.125rem/1.1875rem Segoe UI;
  min-width: 7.75rem;
  min-height: 2.75rem;
  box-shadow: none;
  transition: background 0.2s;
  line-height: 1;
  margin-bottom: 0.05rem;

  &:hover {
    background: var(--attendancies-back-button-background-color-hover);
  }

  & > * {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  img {
    position: absolute;
    left: 18%;
    top: 50%;
    transform: translateX(-100%) translateY(-50%);
    width: 0.5rem;
    height: 0.875rem;
    margin-right: 0;
  }
`;

const ClearFilterButton = styled.button<{ disabled?: boolean }>`
  background: none;
  border: none;
  cursor: ${({ disabled }) => (disabled ? "default" : "pointer")};
  width: 2.0625rem;
  height: 2.0625rem;
  padding: 0;
  background-image: ${({ disabled }) =>
    disabled
      ? 'url("/src/assets/images/attendancies/clear-filter-disabled.svg")'
      : 'url("/src/assets/images/attendancies/clear-filter-active.svg")'};

  &:hover {
    background-image: ${({ disabled }) =>
      disabled
        ? 'url("/src/assets/images/attendancies/clear-filter-disabled.svg")'
        : 'url("/src/assets/images/attendancies/clear-filter-hover.svg")'};
  }
`;

interface AllAbsencesContainerProps {
  selectedEmployee: Employee | undefined;
  onSelectEmployee: (employee: Employee | undefined) => void;
  selectedMonth?: number;
  selectedYear?: number;
}

export const AllAbsencesContainer: React.FC<AllAbsencesContainerProps> = ({
  selectedEmployee,
  onSelectEmployee,
  selectedMonth,
  selectedYear,
}) => {
  const payrollsState = useAppSelector(selectPayrolls);
  const { appointmentTypes } = useEnums();
  const [selectedPositions, setSelectedPositions] = useState<string[]>([]);
  const [selectedAppointmentTypes, setSelectedAppointmentTypes] = useState<
    string[]
  >([]);
  const [textFilter, setTextFilter] = useState<string>("");

  const employeesWithLeaves = useEmployeesWithLeaves(payrollsState.payrolls);

  const options = Array.from(
    new Set(
      employeesWithLeaves
        .map((employee) =>
          employee.payrolls.map((payroll) => payroll.position.name ?? "")
        )
        .flat()
        .filter((name) => name && name !== "")
    )
  ).map((position) => ({
    id: position,
    label: position,
    value: position,
  }));

  const typesOfAppointmentOptions = appointmentTypes.map((type) => ({
    id: type.identifier.toString(),
    label: type.name,
    value: type.identifier.toString(),
  }));

  const filteredEmployees = useMemo(() => {
    const employeesWithActiveLeaves = employeesWithLeaves.filter((employee) =>
      employee.payrolls.some((payroll) => payroll.leaves.length > 0)
    );

    const filtered = employeesWithActiveLeaves.filter((employee) => {
      const matchesText =
        textFilter === "" ||
        employee.name.toLowerCase().includes(textFilter.toLowerCase()) ||
        employee.egn?.toLowerCase().includes(textFilter.toLowerCase()) ||
        employee.email?.toLowerCase().includes(textFilter.toLowerCase()) ||
        employee.payrolls.some((p) =>
          p.contractNumber.toLowerCase().includes(textFilter.toLowerCase())
        );

      const matchesPosition =
        selectedPositions.length === 0 ||
        employee.payrolls.some((payroll) =>
          selectedPositions.includes(payroll.position.name ?? "")
        );

      const matchesAppointmentType =
        selectedAppointmentTypes.length === 0 ||
        employee.payrolls.some((payroll) =>
          selectedAppointmentTypes.includes(
            typesOfAppointmentOptions.find(
              (option) => option.id === payroll.contractType.toString()
            )?.id || ""
          )
        );

      const matchesSelectedMonth =
        selectedMonth !== undefined && selectedYear !== undefined
          ? employee.payrolls.some((payroll) =>
              payroll.leaves.some((leave) => {
                const leaveDaysInMonth = calculateLeaveDaysInPeriod(
                  leave,
                  new Date(selectedYear, selectedMonth, 1),
                  new Date(selectedYear, selectedMonth + 1, 0)
                );
                return leaveDaysInMonth > 0;
              })
            )
          : true;

      return (
        matchesText &&
        matchesPosition &&
        matchesAppointmentType &&
        matchesSelectedMonth
      );
    });

    return filtered.sort((a, b) =>
      a.name.localeCompare(b.name, undefined, { sensitivity: "base" })
    );
  }, [
    employeesWithLeaves,
    selectedPositions,
    selectedAppointmentTypes,
    textFilter,
    typesOfAppointmentOptions,
    selectedMonth,
    selectedYear,
  ]);

  const onSelectedChange = (selected: string[]) => {
    setSelectedPositions(selected);
  };

  const onAppointmentTypeChange = (selected: string[]) => {
    setSelectedAppointmentTypes(selected);
  };

  const handleTextFilterChange = (column: string, value: string) => {
    setTextFilter(value);
  };

  const hasActiveFilters = useMemo(() => {
    return (
      textFilter !== "" ||
      selectedPositions.length > 0 ||
      selectedAppointmentTypes.length > 0
    );
  }, [textFilter, selectedPositions, selectedAppointmentTypes]);

  const clearAllFilters = () => {
    setTextFilter("");
    setSelectedPositions([]);
    setSelectedAppointmentTypes([]);
  };

  const onBackButtonClick = () => {
    clearAllFilters();
    onSelectEmployee(undefined);
  };

  return (
    <AbsencesContainer data-testid="all-absences-container">
      <FiltersContainer>
        <BaseFilter
          data-testid="personal-data-filter"
          showSort={false}
          value={textFilter}
          column={"personalData"}
          placeholder={"strSearchByPersonalData"}
          onFilterChange={handleTextFilterChange}
        />
        <ComboboxRow>
          <ComboboxMultipleChoices
            data-testid="absence-type-combobox"
            options={options}
            selectedValues={selectedPositions}
            onChange={onSelectedChange}
            placeholder="strPosition"
          />
          <ComboboxMultipleChoices
            data-testid="type-of-appointment-combobox"
            options={typesOfAppointmentOptions}
            selectedValues={selectedAppointmentTypes}
            onChange={onAppointmentTypeChange}
            placeholder="Type of appointment"
          />
        </ComboboxRow>
      </FiltersContainer>
      <EmployeesContainer>
        <HeaderContainer>
          {selectedEmployee ? (
            <>
              <BackButton
                onClick={onBackButtonClick}
                label="Назад"
                imageSrc={backArrowIcon}
              />
              <ClearFilterButton
                disabled={!hasActiveFilters}
                onClick={hasActiveFilters ? clearAllFilters : undefined}
              />
            </>
          ) : (
            <>
              <StyledHeader content="Employees" headerType={HeaderType.H3} />
              {hasActiveFilters && (
                <ClearFilterButton onClick={clearAllFilters} />
              )}
            </>
          )}
        </HeaderContainer>
        {filteredEmployees.map((employee) =>
          employee.payrolls.map((payroll) => (
            <EmployeeRow
              key={payroll.id}
              onClick={() =>
                onSelectEmployee(
                  selectedEmployee?.payrolls[0]?.id === payroll.id
                    ? undefined
                    : employee
                )
              }
              isHovered={selectedEmployee?.payrolls[0]?.id === payroll.id}
            >
              <StyledAvatar
                name={employee.name}
                photo={""}
                size={1}
                isVisible={true}
                background={generateColorFromName(employee.name)}
              />
              <EmployeeInfo>
                <EmployeeName>{employee.name}</EmployeeName>
                <EmployeePosition>
                  {payroll.position.name ?? ""}
                </EmployeePosition>
              </EmployeeInfo>
            </EmployeeRow>
          ))
        )}
      </EmployeesContainer>
    </AbsencesContainer>
  );
};
