export enum EventType {
    ПлатенГодишенОтпуск = 1,
    ПлатенГодишенОтпускЗаМиналаГодина = 2,
    НеплатенСОсигурителенСтажОтОсигурител = 3,
    НеплатенСОсигурителенСтажОтОсигурен = 4,
    НеплатенЗаДетеДо8 = 5,
    НеплатенБезСтажОтОсигурител = 6,
    НеплатенБезСтажОтОсигурен = 7,
    Самоотлъчване = 8,
    Б<PERSON>лн<PERSON>чен = 9,
    ГледанеНаБоленЧлен = 10,
    ТрудоваЗлополука = 11,
    ПрофесионалнаБолест = 12,
    НеплатенПоНетрудоспособност = 13,
    БолниченПоБременност = 14,
    БолниченСледРаждане = 15,
    НеплатенЗаБременност = 16,
    ОтпускМайка135До410 = 17,
    ОтпускБащаНад6Месеца = 18,
    Отпуск15ДниРаждане = 19,
    ОтпускЗаДетеДо2 = 20,
    ПлатенПоДругиЧленове = 21,
    ОтпускОсиновяванеДо5 = 22,
    НеплатенОсиновяванеДо5 = 23,
    БащаОсиновителДо5 = 24,
    ПлатенПоЧл173а = 25,
    БащаГледаДетеДо8 = 26
}

export const EventTypeDescriptions: Record<EventType, string> = {
    [EventType.ПлатенГодишенОтпуск]: "Платен годишен отпуск",
    [EventType.ПлатенГодишенОтпускЗаМиналаГодина]: "Платен годишен отпуск за минала година",
    [EventType.НеплатенСОсигурителенСтажОтОсигурител]: "Неплатен отпуск с осигурителен стаж от осигурител",
    [EventType.НеплатенСОсигурителенСтажОтОсигурен]: "Неплатен отпуск с осигурителен стаж от осигурен",
    [EventType.НеплатенЗаДетеДо8]: "Неплатен отпуск за отглеждане на дете до 8 години",
    [EventType.НеплатенБезСтажОтОсигурител]: "Неплатен отпуск без осигурителен стаж от осигурител",
    [EventType.НеплатенБезСтажОтОсигурен]: "Неплатен отпуск без осигурителен стаж от осигурен",
    [EventType.Самоотлъчване]: "Самоотлъчване",
    [EventType.Болничен]: "Болничен",
    [EventType.ГледанеНаБоленЧлен]: "Гледане на болен член от семейството",
    [EventType.ТрудоваЗлополука]: "Трудова злополука",
    [EventType.ПрофесионалнаБолест]: "Професионална болест",
    [EventType.НеплатенПоНетрудоспособност]: "Неплатен отпуск за временна нетрудоспособност",
    [EventType.БолниченПоБременност]: "Болничен по бременност",
    [EventType.БолниченСледРаждане]: "Болничен след раждане",
    [EventType.НеплатенЗаБременност]: "Неплатен отпуск за бременност и раждане",
    [EventType.ОтпускМайка135До410]: "Отпуск за майка след раждане от 135 до 410",
    [EventType.ОтпускБащаНад6Месеца]: "Отпуск за баща за гледане на дете над 6 месеца",
    [EventType.Отпуск15ДниРаждане]: "Отпуск до 15 дни при раждане на дете",
    [EventType.ОтпускЗаДетеДо2]: "Отпуск за отглеждане на дете до 2 години",
    [EventType.ПлатенПоДругиЧленове]: "Платен отпуск по други чл от КТ",
    [EventType.ОтпускОсиновяванеДо5]: "Отпуск при осиновяване на дете до 5-годишна възраст",
    [EventType.НеплатенОсиновяванеДо5]: "Неплатен отпуск при осиновяване на дете до 5-годишна възраст",
    [EventType.БащаОсиновителДо5]: "Отпуск за баща при осиновяване на дете до 5-годишна възраст",
    [EventType.ПлатенПоЧл173а]: "Платен отпуск по чл. 173а, ал. 1 от КТ",
    [EventType.БащаГледаДетеДо8]: "Отпуск за отглеждане на дете до 8-годишна възраст от бащата (осиновителя)"
};