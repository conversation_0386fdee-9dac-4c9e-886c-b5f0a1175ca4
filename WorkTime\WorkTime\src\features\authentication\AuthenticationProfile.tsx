import { MouseEvent } from "react";
import Button from "../../components/Inputs/Button";
import { logout } from "../../services/authentication/authenticationService";
import { useAuth, User } from "./AuthContext";
import { useNavigate } from "react-router-dom";
import { useAppDispatch } from "../../app/hooks";
import { Status, onSetCompanyStatus } from "../companies/companiesActions";

const AuthenticationProfile = () => {
  const { setUser } = useAuth();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const handleLogout = async (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    logout();
    setUser({} as User);
    navigate("/");
    dispatch(onSetCompanyStatus(Status.Fetching));
  };

  return (
    <>
      <Button
        label="strLogout"
        onClick={handleLogout}
        data-testid="logout-button"
      />
    </>
  );
};

export default AuthenticationProfile;
