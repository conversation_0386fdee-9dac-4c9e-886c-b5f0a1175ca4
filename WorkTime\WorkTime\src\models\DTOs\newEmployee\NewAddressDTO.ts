import { AddressPurpose } from "../address/AddressDTO";
import { ICitiesType } from "../enums/ICitiesType";
import { IDistrictType } from "../enums/IDistrictType";
import { IMunicipalityType } from "../enums/IMunicipalityType";

export interface NewAddressDTO {
  id: string | undefined;
  city: ICitiesType | null;
  country: number | undefined;
  district: IDistrictType | null;
  municipality: IMunicipalityType | null;
  description: string;
  block: string;
  street: string;
  apartment: string;
  postalCode: string;
  neighborhood: string;
  phone: string;
  workPhone: string;
  email: string;
  workEmail: string;
  purpose: AddressPurpose;
  cityName: string;
  districtName: string;
  municipalityName: string;
}
