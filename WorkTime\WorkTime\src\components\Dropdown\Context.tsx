import React, { Dispatch, SetStateAction } from "react";
import { createContext } from "react";

interface DropdownContextProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  isOpened?: (isOpen: boolean) => void;
}

export const DropdownContext = createContext({} as DropdownContextProps);
