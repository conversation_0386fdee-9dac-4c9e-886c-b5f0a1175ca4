import { AbsenceHospitalDTO } from "../absence/AbsenceHospitalDTO";
import { EmployeeDTO } from "../employees/EmployeeDTO";
import { IEntity } from "../IEntity";
import { NomenclatureDTO } from "../nomenclatures/NomenclatureDTO";

export interface PayrollDTO extends IEntity {
  employee: EmployeeDTO;
  payrollId: string;
  companyId: string;
  contractNumber: string;
  position: NomenclatureDTO;
  structureLevelId: string;
  contractType: number;
  annualPaidLeave: number;
  additionalAnnualPaidLeave: number;
  annualPaidLeavePastYears: number;
  leaves: AbsenceHospitalDTO[];
}
