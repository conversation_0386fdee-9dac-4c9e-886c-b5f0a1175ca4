import styled from "styled-components";
import { ViewMode } from "../../models/Enums/ViewMode";

const OutliningContainer = styled.div<{ active: ViewMode }>`
  display: none;
  padding: 0.45em;
  background-color: white;
  width: 15em;
  border-radius: 0em 0em 1em 1em;

  ${({ active }) =>
    active === ViewMode.YearsView &&
    `
    display: block;
  `};
`;

const ContainerYear = styled.div`
  display: flex;
  position: relative;
  justify-content: center;
  align-items: center;
  padding: 0.5em;
  cursor: pointer;

  &::after,
  &::before {
    content: "";
    position: absolute;
    background-color: var(--datepicker-view-buttons-color);
    z-index: 999;
    pointer-events: none;
  }

  &::after {
    top: -0.05em;
    width: 50vw;
    height: 0.05em;
  }

  &::before {
    left: -0.1em;
    width: 0.05em;
    height: 50vh;
  }

  &:hover {
    background-color: var(--datepicker-view-buttons-hover-color);
  }
`;

const ContainerYears = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(3, 1fr);
  background-color: var(--datepicker-view-border);

  height: 11.6rem;
  overflow: hidden;
  ${ContainerYear}:hover {
    background-color: var(--datepicker-view-buttons-hover-color);
  }
`;

interface YearsElementProps {
  handleYearsClick: (selectedYear: number) => void;
  selectedGroupIndex: number;
  active: ViewMode;
}

const YearsElement: React.FC<YearsElementProps> = ({
  handleYearsClick,
  selectedGroupIndex,
  active,
}) => {
  const startYear = 1900;
  const endYear = 2100;
  const groupSize = 12;

  const years: number[] = [];
  for (let year = startYear; year <= endYear; year++) {
    years.push(year);
  }

  const startYearIndex = selectedGroupIndex * groupSize;
  const endYearIndex = (selectedGroupIndex + 1) * groupSize;

  const yearGroups = years.slice(startYearIndex, endYearIndex);

  return (
    <OutliningContainer data-testid="years-element-container" active={active}>
      <ContainerYears data-testid="years-grid">
        {yearGroups.map((year) => (
          <ContainerYear
            key={`years-${year}`}
            data-testid={`year-${year}`}
            onClick={() => handleYearsClick(year)}
          >
            {year}
          </ContainerYear>
        ))}
      </ContainerYears>
    </OutliningContainer>
  );
};

export default YearsElement;
